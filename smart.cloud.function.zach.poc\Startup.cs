﻿using AutoMapper;
using Google.Cloud.Functions.Hosting;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Serilog;
using Smart.Cloud.Core.Logging;
using Smart.Cloud.Core.PubSub.Interfaces;
using Smart.Cloud.Core.PubSub.Services;
using smart.cloud.function.zach.poc.Core;
using smart.cloud.function.zach.poc.Core.Interfaces.IInfrastructure;
using smart.cloud.function.zach.poc.Core.Interfaces.IServices;
using smart.cloud.function.zach.poc.Infrastructure.HttpHandlers;
using smart.cloud.function.zach.poc.Infrastructure.Infrastructure;
using smart.cloud.function.zach.poc.Services.Services;

namespace smart.cloud.function.zach.poc;

public class Startup : FunctionsStartup
{
    public override void ConfigureLogging(WebHostBuilderContext context, ILoggingBuilder logging)
    {
        logging.AddStructuredLoggerToCloudFunction(context);
    }

    public override void ConfigureServices(WebHostBuilderContext context, IServiceCollection services)
    {
        //Auto Mapper Configurations
        var mapperConfig = new MapperConfiguration(mc =>
        {
            mc.AddProfile(new AutoMapperProfile());
        });

        var mapper = mapperConfig.CreateMapper();
        services.AddSingleton(mapper);

        services.AddHttpContextAccessor();
        services.AddSingleton<IApiWrapper, ApiWrapper>();
        services.AddScoped<IProcessMessageService, ProcessMessageService>();
        services.AddScoped<IApiConnectorService, ApiConnectorService>();
        services.AddSingleton<IPayloadHandlerService, PayloadHandlerService>();
        // services.AddSingleton<IPublishMessageService, PubSubPublishMessageService>(); // Removed

        services.AddHttpClients();

        var builder = Host.CreateDefaultBuilder();
        builder.Build();
    }
}