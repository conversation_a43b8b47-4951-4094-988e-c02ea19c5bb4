locals {
  function = {
    name                    = "jeremiahpoc"
    entrypoint              = "Smart.Cloud.JeremiahPOC.Function"
    runtime                 = "dotnet"
    source_code_zip         = "function-code/Smart.Cloud.JeremiahPOC.zip"
    build_env_variables     = { "GOOGLE_BUILDABLE" = "Smart.Cloud.JeremiahPOC" }
  }
}

module "cloud_functions_jeremiahpoc" {   
  for_each = local.function
  source  = "app.terraform.io/hca-healthcare/cloud-functions/gcp"
  version = "3.0.3"
  name       = each.value.function.name
  region     = var.gcp_region
  project_id = var.gcp_project_id
  labels     = module.tagging.metadata
  entrypoint = each.value.function.entrypoint
  runtime    = each.value.function.runtime
  storage_source = {
    bucket = data.google_storage_bucket.cloud_functions_appcode_sensitive_smr_us.name
  }
  members = {
    developers = ["serviceAccount:${data.google_project.project.number}-<EMAIL>"]
  }
  source_code_zip     = each.value.function.source_code_zip
  build_env_variables = each.value.function.build_env_variables
  service_config = {
    vpc_connector                 = var.vpc_connector
    min_instance_count            = 0
    max_instance_count            = 1
    available_memory              = "256Mi"
    vpc_connector_egress_settings = "ALL_TRAFFIC"
    service_account_email         = module.cloud_functions_sa.service_account_email
    runtime_env_variables = {
      GCP_PROJECT_ID = var.gcp_project_id
    }
  }

  depends_on = [
    module.pubsub,
    module.storage,
    module.cloud_functions_sa,
  ]

}