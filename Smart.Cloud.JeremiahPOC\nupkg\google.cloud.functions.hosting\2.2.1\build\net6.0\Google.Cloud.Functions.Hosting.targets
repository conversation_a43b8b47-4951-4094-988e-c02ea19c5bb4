<Project>
  <!-- 
    - Target just to set the _GeneratedEntryPointFile property. This has to execute before CoreGenerateFunctionEntryPoint
    - so that it has a reasonable output, but we don't want to generate the file unless we're really doing a build.
    - (If the IntermediateOutputPath is just the current directory, we'll end up creating the file in the wrong
    - place, which seems to happen if this target file is imported directly.)
    - TODO: Work out exactly why this is required.
    -->
  <Target Name="SetGeneratedEntryPointFile" Condition="'$(AutoGenerateEntryPoint)' == 'true' AND '$(OutputType)' == 'Exe'">
    <PropertyGroup>
      <!-- The location of the generated program file.-->
      <_GeneratedEntryPointFile>$(IntermediateOutputPath)$(MSBuildProjectName).Program.g$(DefaultLanguageSourceExtension)</_GeneratedEntryPointFile>
    </PropertyGroup>
  </Target>
  
  <Target Name="_GenerateRealEntryPointType"
          BeforeTargets="CoreCompile"
          DependsOnTargets="PrepareForBuild;CoreGenerateFunctionEntryPoint"
          Condition="'$(AutoGenerateEntryPoint)' == 'true' AND '$(OutputType)' == 'Exe'">
    <PropertyGroup>
      <StartupObject>AutoGeneratedProgram</StartupObject>
    </PropertyGroup>
  </Target>

  <Target Name="CoreGenerateFunctionEntryPoint"
          DependsOnTargets="SetGeneratedEntryPointFile"
          Condition="'$(Language)' == 'C#' Or '$(Language)' == 'VB' Or '$(Language)' == 'F#'"
          Inputs="$(MSBuildAllProjects)"
          Outputs="$(_GeneratedEntryPointFile)">
    <PropertyGroup Condition="'$(Language)' == 'C#'">
      <_GeneratedProgramFileContent>
        <![CDATA[
// <auto-generated>This file was created automatically</auto-generated>
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
[CompilerGenerated]
internal class AutoGeneratedProgram
{
    public static Task<int> Main(string[] args) =>
        Google.Cloud.Functions.Hosting.EntryPoint.StartAsync(
             typeof(global::AutoGeneratedProgram).Assembly, args);
}
]]>
      </_GeneratedProgramFileContent>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Language)' == 'F#'">
      <_GeneratedProgramFileContent>
        <![CDATA[
module GeneratedEntryPoint

type AutoGeneratedProgram() =
    [<EntryPoint>]
    static let main argv =
        Google.Cloud.Functions.Hosting.EntryPoint.StartAsync(typeof<AutoGeneratedProgram>.Assembly, argv).Result
]]>
      </_GeneratedProgramFileContent>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Language)' == 'VB'">
      <_GeneratedProgramFileContent>
        <![CDATA[
' <auto-generated>This file was created automatically</auto-generated>  
Namespace Global
    <Global.System.Runtime.CompilerServices.CompilerGenerated()>
    Module AutoGeneratedProgram
        Public Sub Main(args As String())
            Global.Google.Cloud.Functions.Hosting.EntryPoint.
                StartAsync(GetType(AutoGeneratedProgram).Assembly, args).
                    GetAwaiter().GetResult()
        End Sub
    End Module
End Namespace
]]>
      </_GeneratedProgramFileContent>
    </PropertyGroup>
    <WriteLinesToFile File="$(_GeneratedEntryPointFile)"
                      Overwrite="true"
                      Lines="$([MSBuild]::Escape($(_GeneratedProgramFileContent)))"
                      Encoding="utf-8"/>

    <ItemGroup>
      <Compile Include="$(_GeneratedEntryPointFile)" />
    </ItemGroup>
  </Target>
</Project>