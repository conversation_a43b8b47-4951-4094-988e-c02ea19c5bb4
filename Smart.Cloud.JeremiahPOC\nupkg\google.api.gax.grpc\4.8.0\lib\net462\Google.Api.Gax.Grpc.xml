<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Google.Api.Gax.Grpc</name>
    </assembly>
    <members>
        <member name="T:Google.Api.Gax.Grpc.ApiBidirectionalStreamingCall`2">
            <summary>
            Bridge between a duplex streaming RPC method and higher level
            abstractions, applying call settings as required.
            </summary>
            <typeparam name="TRequest">RPC request type</typeparam>
            <typeparam name="TResponse">RPC response type</typeparam>
        </member>
        <member name="P:Google.Api.Gax.Grpc.ApiBidirectionalStreamingCall`2.BaseCallSettings">
            <summary>
            The base <see cref="T:Google.Api.Gax.Grpc.CallSettings"/> for this API call; these can be further overridden by providing
            a <c>CallSettings</c> to <see cref="M:Google.Api.Gax.Grpc.ApiBidirectionalStreamingCall`2.Call(Google.Api.Gax.Grpc.CallSettings)"/>.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.Grpc.ApiBidirectionalStreamingCall`2.StreamingSettings">
            <summary>
            Streaming settings.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Grpc.ApiBidirectionalStreamingCall`2.Call(Google.Api.Gax.Grpc.CallSettings)">
            <summary>
            Initializes a streaming RPC call.
            </summary>
            <param name="perCallCallSettings">The call settings to apply to this specific call,
            overriding defaults where necessary.</param>
            <returns>A gRPC duplex streaming call object.</returns>
        </member>
        <member name="M:Google.Api.Gax.Grpc.ApiBidirectionalStreamingCall`2.WithMergedBaseCallSettings(Google.Api.Gax.Grpc.CallSettings)">
            <summary>
            Returns a new API call using the original base call settings merged with <paramref name="callSettings"/>.
            Where there's a conflict, the original base call settings have priority.
            </summary>
        </member>
        <member name="T:Google.Api.Gax.Grpc.ApiCall.GrpcCallAdapter`2">
            <summary>
            Adapter used to mask the fact that when we need response/trailing metadata, a sync call may need
            to use the async gRPC code.
            </summary>
        </member>
        <member name="T:Google.Api.Gax.Grpc.ApiCall`2">
            <summary>
            Bridge between an RPC method (with synchronous and asynchronous variants) and higher level
            abstractions, applying call settings as required.
            </summary>
            <typeparam name="TRequest">RPC request type</typeparam>
            <typeparam name="TResponse">RPC response type</typeparam>
        </member>
        <member name="P:Google.Api.Gax.Grpc.ApiCall`2.BaseCallSettings">
            <summary>
            The base <see cref="T:Google.Api.Gax.Grpc.CallSettings"/> for this API call; these can be further overridden by providing
            a <c>CallSettings</c> to <see cref="M:Google.Api.Gax.Grpc.ApiCall`2.Async(`0,Google.Api.Gax.Grpc.CallSettings)"/> or <see cref="M:Google.Api.Gax.Grpc.ApiCall`2.Sync(`0,Google.Api.Gax.Grpc.CallSettings)"/>.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Grpc.ApiCall`2.Async(`0,Google.Api.Gax.Grpc.CallSettings)">
            <summary>
            Performs an RPC call asynchronously.
            </summary>
            <param name="request">The RPC request.</param>
            <param name="perCallCallSettings">The call settings to apply to this specific call,
            overriding defaults where necessary.</param>
            <returns>A task representing the asynchronous operation. The result of the completed task
            will be the RPC response.</returns>
        </member>
        <member name="M:Google.Api.Gax.Grpc.ApiCall`2.Sync(`0,Google.Api.Gax.Grpc.CallSettings)">
            <summary>
            Performs an RPC call synchronously.
            </summary>
            <param name="request">The RPC request.</param>
            <param name="perCallCallSettings">The call settings to apply to this specific call,
            overriding defaults where necessary.</param>
            <returns>The RPC response.</returns>
        </member>
        <member name="M:Google.Api.Gax.Grpc.ApiCall`2.WithMergedBaseCallSettings(Google.Api.Gax.Grpc.CallSettings)">
            <summary>
            Returns a new API call using the original base call settings merged with <paramref name="callSettings"/>.
            Where there's a conflict, the original base call settings have priority.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Grpc.ApiCall`2.WithCallSettingsOverlay(System.Func{`0,Google.Api.Gax.Grpc.CallSettings})">
            <summary>
            Constructs a new <see cref="T:Google.Api.Gax.Grpc.ApiCall`2"/> that applies an overlay to
            the underlying <see cref="T:Google.Api.Gax.Grpc.CallSettings"/>. If a value exists in both the original and
            the overlay, the overlay takes priority.
            </summary>
            <param name="callSettingsOverlayFn">Function that builds the overlay <see cref="T:Google.Api.Gax.Grpc.CallSettings"/>.</param>
            <returns>A new <see cref="T:Google.Api.Gax.Grpc.ApiCall`2"/> with the overlay applied.</returns>
        </member>
        <member name="M:Google.Api.Gax.Grpc.ApiCall`2.WithGoogleRequestParam(System.String,System.Func{`0,System.String})">
            <summary>
            Constructs a new <see cref="T:Google.Api.Gax.Grpc.ApiCall`2"/> that applies an x-goog-request-params header to each request,
            using the specified parameter name and a value derived from the request.
            </summary>
            <remarks>Values produced by the function are URL-encoded; it is expected that <paramref name="parameterName"/> is already URL-encoded.</remarks>
            <param name="parameterName">The parameter name in the header. Must not be null.</param>
            <param name="valueSelector">A function to call on each request, to determine the value to specify in the header.
            The parameter must not be null, but may return null.</param>
            <returns>A new <see cref="T:Google.Api.Gax.Grpc.ApiCall`2"/> which applies the header on each request.</returns>
        </member>
        <member name="M:Google.Api.Gax.Grpc.ApiCall`2.WithExtractedGoogleRequestParam(Google.Api.Gax.Grpc.RoutingHeaderExtractor{`0})">
            <summary>
            Constructs a new <see cref="T:Google.Api.Gax.Grpc.ApiCall`2"/> that applies an x-goog-request-params header to each request,
            using the <see cref="T:Google.Api.Gax.Grpc.RoutingHeaderExtractor`1"/>.
            </summary>
            <remarks>Values produced by the function are URL-encoded.</remarks>
            <param name="extractor">The <see cref="T:Google.Api.Gax.Grpc.RoutingHeaderExtractor`1"/> that extracts the value of the routing header from a request.</param>
            <returns>>A new <see cref="T:Google.Api.Gax.Grpc.ApiCall`2"/> which applies the header on each request.</returns>
        </member>
        <member name="T:Google.Api.Gax.Grpc.ApiClientStreamingCall`2">
            <summary>
            Bridge between a client streaming RPC method and higher level
            abstractions, applying call settings as required.
            </summary>
            <typeparam name="TRequest">RPC request type</typeparam>
            <typeparam name="TResponse">RPC response type</typeparam>
        </member>
        <member name="P:Google.Api.Gax.Grpc.ApiClientStreamingCall`2.BaseCallSettings">
            <summary>
            The base <see cref="T:Google.Api.Gax.Grpc.CallSettings"/> for this API call; these can be further overridden by providing
            a <c>CallSettings</c> to <see cref="M:Google.Api.Gax.Grpc.ApiClientStreamingCall`2.Call(Google.Api.Gax.Grpc.CallSettings)"/>.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.Grpc.ApiClientStreamingCall`2.StreamingSettings">
            <summary>
            Streaming settings.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Grpc.ApiClientStreamingCall`2.Call(Google.Api.Gax.Grpc.CallSettings)">
            <summary>
            Initializes a streaming RPC call.
            </summary>
            <param name="perCallCallSettings">The call settings to apply to this specific call,
            overriding defaults where necessary.</param>
            <returns>A gRPC client streaming call object.</returns>
        </member>
        <member name="M:Google.Api.Gax.Grpc.ApiClientStreamingCall`2.WithMergedBaseCallSettings(Google.Api.Gax.Grpc.CallSettings)">
            <summary>
            Returns a new API call using the original base call settings merged with <paramref name="callSettings"/>.
            Where there's a conflict, the original base call settings have priority.
            </summary>
        </member>
        <member name="T:Google.Api.Gax.Grpc.ApiMetadata">
            <summary>
            Provides metadata about an API. This is expected to be constructed with a single instance
            per API; equality is by simple identity.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.Grpc.ApiMetadata.ProtobufDescriptors">
            <summary>
            The protobuf descriptors used by this API.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.Grpc.ApiMetadata.TypeRegistry">
            <summary>
            A type registry containing all the types in <see cref="P:Google.Api.Gax.Grpc.ApiMetadata.ProtobufDescriptors"/>.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.Grpc.ApiMetadata.Name">
            <summary>
            The name of the API (typically the fully-qualified name of the client library package).
            This is never null or empty.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.Grpc.ApiMetadata.RequestNumericEnumJsonEncoding">
            <summary>
            When true, <see cref="T:Google.Api.Gax.Grpc.Rest.RestGrpcAdapter"/> will request that enums are encoded as numbers in JSON
            rather than as strings, preserving unknown values.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.Grpc.ApiMetadata.HttpRuleOverrides">
            <summary>
            A dictionary (based on ordinal string comparisons) from fully-qualified RPC names
            to byte strings representing overrides for the HTTP rule. This is designed to support
            mixins which are hosted at individual APIs, but which are exposed via different URLs
            to the original mixin definition. This is never null, but may be empty.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Grpc.ApiMetadata.#ctor(System.String,System.Collections.Generic.IEnumerable{Google.Protobuf.Reflection.FileDescriptor})">
            <summary>
            Creates an API descriptor from a sequence of file descriptors.
            </summary>
            <remarks>
            The sequence is evaluated once, on construction.
            </remarks>
            <param name="name">The name of the API. Must not be null or empty.</param>
            <param name="descriptors">The protobuf descriptors of the API. Must not be null.</param>
        </member>
        <member name="M:Google.Api.Gax.Grpc.ApiMetadata.BuildDescriptorsProviderFromDescriptors(System.Collections.Generic.IEnumerable{Google.Protobuf.Reflection.FileDescriptor})">
            <summary>
            Method used in the above constructor to create the lazy provider.
            (Unfortunately this can't be a local method.)
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Grpc.ApiMetadata.#ctor(System.String,System.Func{System.Collections.Generic.IEnumerable{Google.Protobuf.Reflection.FileDescriptor}})">
            <summary>
            Creates an API descriptor which lazily requests the protobuf descriptors when <see cref="P:Google.Api.Gax.Grpc.ApiMetadata.ProtobufDescriptors"/> is first called.
            </summary>
            <param name="name">The name of the API. Must not be null or empty.</param>
            <param name="descriptorsProvider">A provider function for the protobuf descriptors of the API. Must not be null, and must not
            return a null value. This will only be called once by this API descriptor, when first requested.</param>
        </member>
        <member name="M:Google.Api.Gax.Grpc.ApiMetadata.BuildDescriptorsProviderFromOtherProvider(System.Func{System.Collections.Generic.IEnumerable{Google.Protobuf.Reflection.FileDescriptor}})">
            <summary>
            Method used in the above constructor to create the lazy provider.
            (Unfortunately this can't be a local method.)
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Grpc.ApiMetadata.WithRequestNumericEnumJsonEncoding(System.Boolean)">
            <summary>
            Returns a new instance based on this one, but with the specified value for <see cref="P:Google.Api.Gax.Grpc.ApiMetadata.RequestNumericEnumJsonEncoding"/>.
            </summary>
            <param name="value">The desired value of <see cref="P:Google.Api.Gax.Grpc.ApiMetadata.RequestNumericEnumJsonEncoding"/> in the new instance.</param>
            <returns>The new instance.</returns>
        </member>
        <member name="M:Google.Api.Gax.Grpc.ApiMetadata.WithHttpRuleOverrides(System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{System.String,Google.Protobuf.ByteString}})">
            <summary>
            Creates a new instance with the same values as this one, other than the given set of HttpRule overrides.
            </summary>
            <param name="overrides">The HttpRule overrides for services in this package; typically used to override
            URLs for the REST transport. Must not be null. Will be cloned in the form of an immutable dictionary,
            after which the original sequence is discarded.</param>
            <returns>The new instance.</returns>
        </member>
        <member name="T:Google.Api.Gax.Grpc.ApiServerStreamingCall`2">
            <summary>
            Bridge between a server streaming RPC method and higher level
            abstractions, applying call settings as required.
            </summary>
            <typeparam name="TRequest">RPC request type</typeparam>
            <typeparam name="TResponse">RPC response type</typeparam>
        </member>
        <member name="P:Google.Api.Gax.Grpc.ApiServerStreamingCall`2.BaseCallSettings">
            <summary>
            The base <see cref="T:Google.Api.Gax.Grpc.CallSettings"/> for this API call; these can be further overridden by providing
            a <c>CallSettings</c> to <see cref="M:Google.Api.Gax.Grpc.ApiServerStreamingCall`2.Call(`0,Google.Api.Gax.Grpc.CallSettings)"/>.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Grpc.ApiServerStreamingCall`2.CallAsync(`0,Google.Api.Gax.Grpc.CallSettings)">
            <summary>
            Initializes a streaming RPC call asynchronously.
            </summary>
            <param name="request">The RPC request.</param>
            <param name="perCallCallSettings">The call settings to apply to this specific call,
            overriding defaults where necessary.</param>
            <returns>A task representing the gRPC duplex streaming call object.</returns>
        </member>
        <member name="M:Google.Api.Gax.Grpc.ApiServerStreamingCall`2.Call(`0,Google.Api.Gax.Grpc.CallSettings)">
            <summary>
            Initializes a streaming RPC call.
            </summary>
            <param name="request">The RPC request.</param>
            <param name="perCallCallSettings">The call settings to apply to this specific call,
            overriding defaults where necessary.</param>
            <returns>A gRPC duplex streaming call object.</returns>
        </member>
        <member name="M:Google.Api.Gax.Grpc.ApiServerStreamingCall`2.WithMergedBaseCallSettings(Google.Api.Gax.Grpc.CallSettings)">
            <summary>
            Returns a new API call using the original base call settings merged with <paramref name="callSettings"/>.
            Where there's a conflict, the original base call settings have priority.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Grpc.ApiServerStreamingCall`2.WithCallSettingsOverlay(System.Func{`0,Google.Api.Gax.Grpc.CallSettings})">
            <summary>
            Constructs a new <see cref="T:Google.Api.Gax.Grpc.ApiServerStreamingCall`2"/> that applies an overlay to
            the underlying <see cref="T:Google.Api.Gax.Grpc.CallSettings"/>. If a value exists in both the original and
            the overlay, the overlay takes priority.
            </summary>
            <param name="callSettingsOverlayFn">Function that builds the overlay <see cref="T:Google.Api.Gax.Grpc.CallSettings"/>.</param>
            <returns>A new <see cref="T:Google.Api.Gax.Grpc.ApiServerStreamingCall`2"/> with the overlay applied.</returns>
        </member>
        <member name="M:Google.Api.Gax.Grpc.ApiServerStreamingCall`2.WithGoogleRequestParam(System.String,System.Func{`0,System.String})">
            <summary>
            Constructs a new <see cref="T:Google.Api.Gax.Grpc.ApiServerStreamingCall`2"/> that applies an x-goog-request-params header to each request,
            using the specified parameter name and a value derived from the request.
            </summary>
            <remarks>Values produced by the function are URL-encoded; it is expected that <paramref name="parameterName"/> is already URL-encoded.</remarks>
            <param name="parameterName">The parameter name in the header. Must not be null.</param>
            <param name="valueSelector">A function to call on each request, to determine the value to specify in the header.
            The parameter must not be null, but may return null.</param>
            <returns>A new <see cref="T:Google.Api.Gax.Grpc.ApiServerStreamingCall`2"/> which applies the header on each request.</returns>
        </member>
        <member name="M:Google.Api.Gax.Grpc.ApiServerStreamingCall`2.WithExtractedGoogleRequestParam(Google.Api.Gax.Grpc.RoutingHeaderExtractor{`0})">
            <summary>
            Constructs a new <see cref="T:Google.Api.Gax.Grpc.ApiCall`2"/> that applies an x-goog-request-params header to each request,
            using the <see cref="T:Google.Api.Gax.Grpc.RoutingHeaderExtractor`1"/>.
            </summary>
            <remarks>Values produced by the function are URL-encoded.</remarks>
            <param name="extractor">The <see cref="T:Google.Api.Gax.Grpc.RoutingHeaderExtractor`1"/> that extracts the value of the routing header from a request.</param>
            <returns>>A new <see cref="T:Google.Api.Gax.Grpc.ApiCall`2"/> which applies the header on each request.</returns>
        </member>
        <member name="T:Google.Api.Gax.Grpc.AsyncResponseStream`1">
            <summary>
            An adapter from the gRPC stream representation (<see cref="T:Grpc.Core.IAsyncStreamReader`1"/>) to <see cref="T:System.Collections.Generic.IAsyncEnumerable`1"/>
            and <see cref="T:System.Collections.Generic.IAsyncEnumerator`1"/>. Note that <see cref="M:Google.Api.Gax.Grpc.AsyncResponseStream`1.GetAsyncEnumerator(System.Threading.CancellationToken)"/> can only
            be called once per instance due to the "only iterate once" nature of the response stream.
            </summary>
            <remarks>
            This type implements both of the standard asynchronous sequence interfaces for simplicity of use:
            <list type="bullet">
              <item>C# 8 users can use  <c>await foreach</c> because it implements <see cref="T:System.Collections.Generic.IAsyncEnumerable`1"/></item>
              <item>It's compatible with the System.Linq.Async package for query transformations.</item>
              <item>Pre-C# 8 users who will be calling <see cref="M:Google.Api.Gax.Grpc.AsyncResponseStream`1.MoveNextAsync" /> and <see cref="P:Google.Api.Gax.Grpc.AsyncResponseStream`1.Current"/> directly don't need
              to call <see cref="M:Google.Api.Gax.Grpc.AsyncResponseStream`1.GetAsyncEnumerator(System.Threading.CancellationToken)"/>.</item>
            </list>
            </remarks>
            <typeparam name="TResponse">The response type.</typeparam>
        </member>
        <member name="P:Google.Api.Gax.Grpc.AsyncResponseStream`1.Current">
            <inheritdoc />
        </member>
        <member name="M:Google.Api.Gax.Grpc.AsyncResponseStream`1.DisposeAsync">
            <inheritdoc />
        </member>
        <member name="M:Google.Api.Gax.Grpc.AsyncResponseStream`1.GetAsyncEnumerator(System.Threading.CancellationToken)">
            <summary>
            Begins iterating over the response stream, using the specified cancellation token. This method can only be called
            once per instance.
            </summary>
            <param name="cancellationToken">The cancellation token to use in subsequent <see cref="M:Google.Api.Gax.Grpc.AsyncResponseStream`1.MoveNextAsync"/> calls.</param>
            <exception cref="T:System.InvalidOperationException">This method has already been called on this instance.</exception>
            <returns>An iterator over the response stream.</returns>
        </member>
        <member name="M:Google.Api.Gax.Grpc.AsyncResponseStream`1.MoveNextAsync(System.Threading.CancellationToken)">
            <summary>
            Moves to the next item, using the specified cancellation token.
            </summary>
            <remarks></remarks>
            <param name="cancellationToken">The cancellation token to use for this step.</param>
            <returns>A task that will complete with a result of true if the enumerator was successfully advanced to the next element, or false if the enumerator has passed the end of the collection.</returns>
        </member>
        <member name="M:Google.Api.Gax.Grpc.AsyncResponseStream`1.MoveNextAsync">
            <summary>
            Moves to the next item, using the cancellation token configured by <see cref="M:Google.Api.Gax.Grpc.AsyncResponseStream`1.GetAsyncEnumerator(System.Threading.CancellationToken)"/>.
            </summary>
        </member>
        <member name="T:Google.Api.Gax.Grpc.BidirectionalStreamingBase`2">
            <summary>
            Base class for bidirectional streaming RPC methods. This wraps an underlying call returned by gRPC,
            in order to provide a wrapper for the async response stream, allowing users to take advantage
            of <code>await foreach</code> support from C# 8 onwards. Additionally, it wraps the
            request stream in a buffer, allowing multiple requests to be written without waiting for them
            to be transmitted.
            </summary>
            <remarks>
            To avoid memory leaks, users must dispose of gRPC streams.
            Additionally, you are strongly advised to read the whole response stream, even if the data
            is not required - this avoids effectively cancelling the call.
            </remarks>
            <typeparam name="TRequest">RPC request type</typeparam>
            <typeparam name="TResponse">RPC response type</typeparam>
        </member>
        <member name="P:Google.Api.Gax.Grpc.BidirectionalStreamingBase`2.GrpcCall">
            <summary>
            The underlying gRPC duplex streaming call.
            Warning: DO NOT USE <c>GrpcCall.RequestStream</c> at all if using
            <see cref="M:Google.Api.Gax.Grpc.BidirectionalStreamingBase`2.TryWriteAsync(`0)"/>, <see cref="M:Google.Api.Gax.Grpc.BidirectionalStreamingBase`2.WriteAsync(`0)"/>,
            <see cref="M:Google.Api.Gax.Grpc.BidirectionalStreamingBase`2.TryWriteAsync(`0,Grpc.Core.WriteOptions)"/> , or <see cref="M:Google.Api.Gax.Grpc.BidirectionalStreamingBase`2.WriteAsync(`0,Grpc.Core.WriteOptions)"/>.
            Doing so will cause conflict with the write-buffer used within the <c>[Try]WriteAsync</c> methods.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Grpc.BidirectionalStreamingBase`2.TryWriteAsync(`0)">
            <summary>
            Writes a message to the stream, if there is enough space in the buffer and <see cref="M:Google.Api.Gax.Grpc.BidirectionalStreamingBase`2.WriteCompleteAsync"/>
            hasn't already been called. The same write options will be used as for the previous message.
            </summary>
            <param name="message">The message to write.</param>
            <returns><c>null</c> if the message queue is full or the stream has already been completed;
            otherwise, a <see cref="T:System.Threading.Tasks.Task"/> which will complete when the message has been written to the stream.</returns>
        </member>
        <member name="M:Google.Api.Gax.Grpc.BidirectionalStreamingBase`2.WriteAsync(`0)">
            <summary>
            Writes a message to the stream, if there is enough space in the buffer and <see cref="M:Google.Api.Gax.Grpc.BidirectionalStreamingBase`2.WriteCompleteAsync"/>
            hasn't already been called. The same write options will be used as for the previous message.
            </summary>
            <param name="message">The message to write.</param>
            <exception cref="T:System.InvalidOperationException">There isn't enough space left in the buffer,
            or <see cref="M:Google.Api.Gax.Grpc.BidirectionalStreamingBase`2.WriteCompleteAsync"/> has already been called.</exception>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> which will complete when the message has been written to the stream.</returns>
        </member>
        <member name="M:Google.Api.Gax.Grpc.BidirectionalStreamingBase`2.TryWriteAsync(`0,Grpc.Core.WriteOptions)">
            <summary>
            Writes a message to the stream, if there is enough space in the buffer and <see cref="M:Google.Api.Gax.Grpc.BidirectionalStreamingBase`2.WriteCompleteAsync"/>
            hasn't already been called.
            </summary>
            <param name="message">The message to write.</param>
            <param name="options">The write options to use for this message.</param>
            <returns><c>null</c> if the message queue is full or the stream has already been completed.</returns>
        </member>
        <member name="M:Google.Api.Gax.Grpc.BidirectionalStreamingBase`2.WriteAsync(`0,Grpc.Core.WriteOptions)">
            <summary>
            Writes a message to the stream, if there is enough space in the buffer and <see cref="M:Google.Api.Gax.Grpc.BidirectionalStreamingBase`2.WriteCompleteAsync"/>
            hasn't already been called.
            </summary>
            <param name="message">The message to write.</param>
            <param name="options">The write options to use for this message.</param>
            <exception cref="T:System.InvalidOperationException">There isn't enough space left in the buffer,
            or <see cref="M:Google.Api.Gax.Grpc.BidirectionalStreamingBase`2.WriteCompleteAsync"/> has already been called.</exception>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> which will complete when the message has been written to the stream.</returns>
        </member>
        <member name="M:Google.Api.Gax.Grpc.BidirectionalStreamingBase`2.TryWriteCompleteAsync">
            <summary>
            Completes the stream when all buffered messages have been sent.
            Only the first call to this method on any instance will have any effect;
            subsequent calls will return <c>null</c>.
            </summary>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> which will complete when the stream has finished being completed;
            or <c>null</c> if this method has already been called.</returns>
        </member>
        <member name="M:Google.Api.Gax.Grpc.BidirectionalStreamingBase`2.WriteCompleteAsync">
            <summary>
            Completes the stream when all buffered messages have been sent. This method can only be called
            once, and further messages cannot be written after it has been called.
            </summary>
            <exception cref="T:System.InvalidOperationException">This method has already been called.</exception>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> which will complete when the stream has finished being completed.</returns>
        </member>
        <member name="M:Google.Api.Gax.Grpc.BidirectionalStreamingBase`2.GetResponseStream">
            <summary>
            Async stream to read streaming responses, exposed as an async sequence.
            The default implementation will use <see cref="P:Google.Api.Gax.Grpc.BidirectionalStreamingBase`2.GrpcCall"/> to extract a response
            stream, and adapt it to <see cref="T:Google.Api.Gax.Grpc.AsyncResponseStream`1"/>.
            </summary>
            <remarks>
            If this method is called more than once, all the returned enumerators will be enumerating over the
            same underlying response stream, which may cause confusion. Additionally, the sequence returned by
            this method can only be iterated over a single time. Attempting to iterate more than once will cause
            an <see cref="T:System.InvalidOperationException"/>.
            </remarks>
        </member>
        <member name="M:Google.Api.Gax.Grpc.BidirectionalStreamingBase`2.Dispose">
            <summary>
            Disposes of the underlying gRPC call. There is no need to dispose of both the wrapper
            and the underlying call; it's typically simpler to dispose of the wrapper with a
            <code>using</code> statement as the wrapper is returned by client libraries.
            </summary>
            <remarks>The default implementation just calls Dispose on the result of <see cref="P:Google.Api.Gax.Grpc.BidirectionalStreamingBase`2.GrpcCall"/>.</remarks>
        </member>
        <member name="T:Google.Api.Gax.Grpc.BidirectionalStreamingSettings">
            <summary>
            Settings for bidirectional streaming.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Grpc.BidirectionalStreamingSettings.#ctor(System.Int32)">
            <summary>
            Configure settings for bidirectional streaming.
            </summary>
            <param name="bufferedClientWriterCapacity">The capacity of the write buffer.</param>
        </member>
        <member name="P:Google.Api.Gax.Grpc.BidirectionalStreamingSettings.BufferedClientWriterCapacity">
            <summary>
            The capacity of the write buffer, that locally buffers streaming requests
            before they are sent to the server.
            </summary>
        </member>
        <member name="T:Google.Api.Gax.Grpc.BufferedClientStreamWriter`1">
            <summary>
            A wrapper around <see cref="T:Grpc.Core.IClientStreamWriter`1"/> which removes the "one write at a time"
            restriction by buffering messages (and the completion signal) up to a given capacity.
            </summary>
            <typeparam name="T">The type of message in the stream.</typeparam>
        </member>
        <member name="F:Google.Api.Gax.Grpc.BufferedClientStreamWriter`1._queue">
            <summary>
            Queue of requests. If this is non-empty, there's at least one request in-flight, which
            is always the head of the queue.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.Grpc.BufferedClientStreamWriter`1.Capacity">
            <summary>
            The capacity of the writer.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.Grpc.BufferedClientStreamWriter`1.BufferedWriteCount">
            <summary>
            The number of write calls that have been buffered.
            </summary>
            <remarks>
            The value of this property may change due to activity from other threads. It should only be used
            for testing and similar scenarios where the system state is well understood.
            </remarks>
        </member>
        <member name="M:Google.Api.Gax.Grpc.BufferedClientStreamWriter`1.#ctor(Grpc.Core.IClientStreamWriter{`0},System.Int32)">
            <summary>
            Constructs an instance which writes to the specified writer, and with the given capacity.
            </summary>
            <param name="writer">The writer to delegate to.</param>
            <param name="capacity">The maximum number of messages to buffer.</param>
        </member>
        <member name="M:Google.Api.Gax.Grpc.BufferedClientStreamWriter`1.TryWriteAsync(`0)">
            <summary>
            Writes a message to the stream, if there is enough space in the buffer and <see cref="M:Google.Api.Gax.Grpc.BufferedClientStreamWriter`1.WriteCompleteAsync"/>
            hasn't already been called. The same write options will be used as for the previous message.
            </summary>
            <param name="message">The message to write.</param>
            <returns><c>null</c> if the message queue is full or the stream has already been completed;
            otherwise, a <see cref="T:System.Threading.Tasks.Task"/> which will complete when the message has been written to the stream.</returns>
        </member>
        <member name="M:Google.Api.Gax.Grpc.BufferedClientStreamWriter`1.TryWriteAsync(`0,Grpc.Core.WriteOptions)">
            <summary>
            Writes a message to the stream, if there is enough space in the buffer and <see cref="M:Google.Api.Gax.Grpc.BufferedClientStreamWriter`1.WriteCompleteAsync"/>
            hasn't already been called.
            </summary>
            <param name="message">The message to write.</param>
            <param name="options">The write options to use for this message.</param>
            <returns><c>null</c> if the message queue is full or the stream has already been completed.</returns>
        </member>
        <member name="M:Google.Api.Gax.Grpc.BufferedClientStreamWriter`1.WriteAsync(`0)">
            <summary>
            Writes a message to the stream, if there is enough space in the buffer and <see cref="M:Google.Api.Gax.Grpc.BufferedClientStreamWriter`1.WriteCompleteAsync"/>
            hasn't already been called. The same write options will be used as for the previous message.
            </summary>
            <param name="message">The message to write.</param>
            <exception cref="T:System.InvalidOperationException">There isn't enough space left in the buffer,
            or the stream has been completed.</exception>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> which will complete when the message has been written to the stream.</returns>
        </member>
        <member name="M:Google.Api.Gax.Grpc.BufferedClientStreamWriter`1.WriteAsync(`0,Grpc.Core.WriteOptions)">
            <summary>
            Writes a message to the stream, if there is enough space in the buffer and <see cref="M:Google.Api.Gax.Grpc.BufferedClientStreamWriter`1.WriteCompleteAsync"/>
            hasn't already been called.
            </summary>
            <param name="message">The message to write.</param>
            <param name="options">The write options to use for this message.</param>
            <exception cref="T:System.InvalidOperationException">There isn't enough space left in the buffer,
            or <see cref="M:Google.Api.Gax.Grpc.BufferedClientStreamWriter`1.WriteCompleteAsync"/> has already been called.</exception>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> which will complete when the message has been written to the stream.</returns>
        </member>
        <member name="M:Google.Api.Gax.Grpc.BufferedClientStreamWriter`1.HandleWriteComplete(System.Threading.Tasks.Task)">
            <summary>
            One of the writes completes - possibly successfully, possibly not. On success,
            we start the next write (or complete) sending if there is one. On failure, we propagate the result
            of this task to all other tasks. Those will in turn trigger further calls to this method,
            but by that time we'll have retained the failed task so we can just exit quickly.
            </summary>
            <param name="writeResult"></param>
        </member>
        <member name="M:Google.Api.Gax.Grpc.BufferedClientStreamWriter`1.ValidateStateForWrite(System.Boolean,System.Boolean)">
            <summary>
            Validates that we can write to the stream, optionally throwing if there's an error.
            This is basically to avoid a big chunk of code appearing in WriteAsyncImpl.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Grpc.BufferedClientStreamWriter`1.TryWriteCompleteAsync">
            <summary>
            Completes the stream when all buffered messages have been sent, if there is enough space in the buffer.
            This method can only be successfully called once, and further messages cannot be written after it
            has been successfully called.
            </summary>
            <returns><c>null</c> if this stream has already be completed, or if the buffer is full; otherwise a
            <see cref="T:System.Threading.Tasks.Task"/> which will complete when the stream has finished being completed.</returns>
        </member>
        <member name="M:Google.Api.Gax.Grpc.BufferedClientStreamWriter`1.WriteCompleteAsync">
            <summary>
            Completes the stream when all buffered messages have been sent, if there is enough space in the buffer.
            This method can only be successfully called once, and further messages cannot be written after it
            has been successfully called.
            </summary>
            <exception cref="T:System.InvalidOperationException">This stream has already be completed, or the buffer is full</exception>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> which will complete when the stream has finished being completed.</returns>
        </member>
        <member name="T:Google.Api.Gax.Grpc.CallSettings">
            <summary>
            Settings to determine how an RPC operates. This type is immutable.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Grpc.CallSettings.#ctor(System.Nullable{System.Threading.CancellationToken},Google.Api.Gax.Expiration,Google.Api.Gax.Grpc.RetrySettings,System.Action{Grpc.Core.Metadata},Grpc.Core.WriteOptions,Grpc.Core.ContextPropagationToken)">
            <summary>
            Constructs an instance with the specified settings.
            </summary>
            <param name="cancellationToken">Cancellation token that can be used for cancelling the call.</param>
            <param name="expiration"><see cref="P:Google.Api.Gax.Grpc.CallSettings.Expiration"/> to use, or null for default expiration behavior.</param>
            <param name="retry"><see cref="P:Google.Api.Gax.Grpc.CallSettings.Retry"/> to use, or null for default retry behavior.</param>
            <param name="headerMutation">Action to modify the headers to send at the beginning of the call.</param>
            <param name="writeOptions"><see cref="T:Grpc.Core.WriteOptions"/> that will be used for the call.</param>
            <param name="propagationToken"><see cref="T:Grpc.Core.ContextPropagationToken"/> for propagating settings from a parent call.</param>
        </member>
        <member name="M:Google.Api.Gax.Grpc.CallSettings.#ctor(System.Nullable{System.Threading.CancellationToken},Google.Api.Gax.Expiration,Google.Api.Gax.Grpc.RetrySettings,System.Action{Grpc.Core.Metadata},Grpc.Core.WriteOptions,Grpc.Core.ContextPropagationToken,System.Action{Grpc.Core.Metadata},System.Action{Grpc.Core.Metadata})">
            <summary>
            Constructs an instance with the specified settings.
            </summary>
            <param name="cancellationToken">Cancellation token that can be used for cancelling the call.</param>
            <param name="expiration"><see cref="P:Google.Api.Gax.Grpc.CallSettings.Expiration"/> to use, or null for default expiration behavior.</param>
            <param name="retry"><see cref="P:Google.Api.Gax.Grpc.CallSettings.Retry"/> to use, or null for default retry behavior.</param>
            <param name="headerMutation">Action to modify the headers to send at the beginning of the call.</param>
            <param name="writeOptions"><see cref="T:Grpc.Core.WriteOptions"/> that will be used for the call.</param>
            <param name="propagationToken"><see cref="T:Grpc.Core.ContextPropagationToken"/> for propagating settings from a parent call.</param>
            <param name="responseMetadataHandler">Action to invoke when response metadata is received.</param>
            <param name="trailingMetadataHandler">Action to invoke when trailing metadata is received.</param>
        </member>
        <member name="P:Google.Api.Gax.Grpc.CallSettings.HeaderMutation">
            <summary>
            Delegate to mutate the metadata which will be sent at the start of the call,
            typically to add custom headers.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.Grpc.CallSettings.CancellationToken">
            <summary>
            Cancellation token that can be used for cancelling the call.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.Grpc.CallSettings.WriteOptions">
            <summary>
            <see cref="T:Grpc.Core.WriteOptions"/> that will be used for the call.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.Grpc.CallSettings.PropagationToken">
            <summary>
            <see cref="T:Grpc.Core.ContextPropagationToken"/> for propagating settings from a parent call.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.Grpc.CallSettings.Expiration">
            <summary>
            The expiration for the call (either a timeout or a deadline), or null for the default expiration.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.Grpc.CallSettings.Retry">
            <summary>
            <see cref="T:Google.Api.Gax.Grpc.RetrySettings"/> to use, or null for default retry behavior.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.Grpc.CallSettings.ResponseMetadataHandler">
            <summary>
            Delegate to receive the metadata associated with a response.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.Grpc.CallSettings.TrailingMetadataHandler">
            <summary>
            Delegate to receive the metadata sent after the response.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Grpc.CallSettings.Merge(Google.Api.Gax.Grpc.CallSettings,Google.Api.Gax.Grpc.CallSettings)">
            <summary>
            Merges the settings in <paramref name="overlaid"/> with those in
            <paramref name="original"/>, with <paramref name="overlaid"/> taking priority.
            If both arguments are null, the result is null. If one argument is null,
            the other argument is returned. Otherwise, a new object is created with a property-wise
            overlay. Any header mutations are combined, however: the mutation from the original is
            performed, then the mutation in the overlay.
            </summary>
            <param name="original">Original settings. May be null.</param>
            <param name="overlaid">Settings to overlay. May be null.</param>
            <returns>A merged set of call settings.</returns>
        </member>
        <member name="M:Google.Api.Gax.Grpc.CallSettings.FromCancellationToken(System.Threading.CancellationToken)">
            <summary>
            Creates a <see cref="T:Google.Api.Gax.Grpc.CallSettings"/> for the specified cancellation token.
            </summary>
            <param name="cancellationToken">The cancellation token for the new settings.</param>
            <returns>A new instance.</returns>
        </member>
        <member name="M:Google.Api.Gax.Grpc.CallSettings.FromExpiration(Google.Api.Gax.Expiration)">
            <summary>
            Creates a <see cref="T:Google.Api.Gax.Grpc.CallSettings"/> for the specified call expiration, or returns null
            if <paramref name="expiration"/> is null.
            </summary>
            <param name="expiration">The call timing for the new settings.</param>
            <returns>A new instance or null if <paramref name="expiration"/> is null..</returns>
        </member>
        <member name="M:Google.Api.Gax.Grpc.CallSettings.FromRetry(Google.Api.Gax.Grpc.RetrySettings)">
            <summary>
            Creates a <see cref="T:Google.Api.Gax.Grpc.CallSettings"/> for the specified retry settings, or returns null
            if <paramref name="retry"/> is null.
            </summary>
            <param name="retry">The call timing for the new settings.</param>
            <returns>A new instance or null if <paramref name="retry"/> is null..</returns>
        </member>
        <member name="M:Google.Api.Gax.Grpc.CallSettings.FromHeaderMutation(System.Action{Grpc.Core.Metadata})">
            <summary>
            Creates a <see cref="T:Google.Api.Gax.Grpc.CallSettings"/> for the specified header mutation, or returns null
            if <paramref name="headerMutation"/> is null.
            </summary>
            <param name="headerMutation">Action to modify the headers to send at the beginning of the call.</param>
            <returns>A new instance, or null if <paramref name="headerMutation"/> is null..</returns>
        </member>
        <member name="M:Google.Api.Gax.Grpc.CallSettings.FromResponseMetadataHandler(System.Action{Grpc.Core.Metadata})">
            <summary>
            Creates a <see cref="T:Google.Api.Gax.Grpc.CallSettings"/> for the specified response metadata handler, or returns null
            if <paramref name="responseMetadataHandler"/> is null.
            </summary>
            <param name="responseMetadataHandler">Action to receive response metadata when the call completes.</param>
            <returns>A new instance, or null if <paramref name="responseMetadataHandler"/> is null..</returns>
        </member>
        <member name="M:Google.Api.Gax.Grpc.CallSettings.FromTrailingMetadataHandler(System.Action{Grpc.Core.Metadata})">
            <summary>
            Creates a <see cref="T:Google.Api.Gax.Grpc.CallSettings"/> for the specified trailing metadata handler, or returns null
            if <paramref name="trailingMetadataHandler"/> is null.
            </summary>
            <param name="trailingMetadataHandler">Action to receive trailing metadata when the call completes.</param>
            <returns>A new instance, or null if <paramref name="trailingMetadataHandler"/> is null..</returns>
        </member>
        <member name="M:Google.Api.Gax.Grpc.CallSettings.FromHeader(System.String,System.String)">
            <summary>
            Creates a <see cref="T:Google.Api.Gax.Grpc.CallSettings"/> for the specified header name and value.
            </summary>
            <param name="name">The name of the header to add. Must not be null.</param>
            <param name="value">The value of the header to add. Must not be null.</param>
            <returns>A new instance.</returns>
        </member>
        <member name="M:Google.Api.Gax.Grpc.CallSettings.FromFieldMask(System.String)">
            <summary>
            Creates a <see cref="T:Google.Api.Gax.Grpc.CallSettings"/> that will include a field mask in the request, to
            limit which fields are returned in the response.
            </summary>
            <remarks>
            The precise effect on the request is not guaranteed: it may be through a header or a side-channel,
            for example. Likewise the effect of combining multiple settings containing field masks is not specified.
            </remarks>
            <param name="fieldMask">The field mask for the request. Must not be null.</param>
            <returns>A new instance.</returns>
        </member>
        <member name="M:Google.Api.Gax.Grpc.CallSettings.FromGoogleRequestParamsHeader(System.String,System.String)">
            <summary>
            Creates a <see cref="T:Google.Api.Gax.Grpc.CallSettings"/> which applies an x-goog-request-params header with the specified
            parameter name and value.
            </summary>
            <remarks>
            <para>
            The value is URL-encoded; it is expected that <paramref name="parameterName"/> is already URL-encoded.
            </para>
            <para>
            This method is intended to be called from API-specific client libraries; it would be very unusual
            for it to be appropriate to call from application code.
            </para>
            </remarks>
            <param name="parameterName">The name of the parameter. Must not be null.</param>
            <param name="value">The value of the parameter, which may be null. A null value is equivalent to providing an empty string.</param>
            <returns>A <see cref="T:Google.Api.Gax.Grpc.CallSettings"/> which applies the appropriate header with a single parameter.</returns>
        </member>
        <member name="M:Google.Api.Gax.Grpc.CallSettings.FromGoogleRequestParamsHeader(System.String)">
            <summary>
            Creates a <see cref="T:Google.Api.Gax.Grpc.CallSettings"/> which applies an x-goog-request-params header with the specified
            escaped header value.
            </summary>
            <remarks>This method is intended to be called from API-specific client libraries; it would be very unusual
            for it to be appropriate to call from application code.</remarks>
            <param name="escapedHeaderValue">The value of the x-goog-request-params header.
            Must be escaped. Must not be null or empty.</param>
            <returns>A <see cref="T:Google.Api.Gax.Grpc.CallSettings"/> which applies the appropriate header.</returns>
        </member>
        <member name="M:Google.Api.Gax.Grpc.CallSettings.FromRequestReasonHeader(System.String)">
            <summary>
            Creates a CallSettings which applies an x-goog-request-reason header with the specified reason.
            </summary>
            <param name="reason">The request reason to specify in the x-goog-request-reason header. Must not be null</param>
            <returns>A CallSettings which applies the appropriate header.</returns>
        </member>
        <member name="T:Google.Api.Gax.Grpc.CallSettings.MetadataMutations">
            <summary>
            Helper class defining some common metadata mutation actions.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Grpc.CallSettings.MetadataMutations.RemoveAll(Grpc.Core.Metadata,System.String)">
            <summary>
            Removes from <paramref name="entries"/> all entries with <paramref name="name"/> if any.
            </summary>
            <param name="entries">The metadata set to modify. Must no be null.</param>
            <param name="name">The name of entries to override. Must no be null.</param>
        </member>
        <member name="M:Google.Api.Gax.Grpc.CallSettings.MetadataMutations.Override(Grpc.Core.Metadata,System.String,System.String)">
            <summary>
            Removes from <paramref name="entries"/> all entries with <paramref name="name"/> if any
            and adds a single entry with the given name and value.
            </summary>
            <param name="entries">The metadata set to modify. Must no be null.</param>
            <param name="name">The name of entries to override. Must no be null.</param>
            <param name="value">The value to associate to a new entry with the given name. Must not be null.</param>
        </member>
        <member name="M:Google.Api.Gax.Grpc.CallSettings.MetadataMutations.Concatenate(Grpc.Core.Metadata,System.String,System.String)">
            <summary>
            If two or more entries with <paramref name="name"/> exist in <paramref name="entries"/>
            they are removed and their values concatenated using <paramref name="separator"/> and a new entry
            is added for the given name, with the resulting concatenated value.
            </summary>
            <param name="entries">The metadata set to modify. Must not be null.</param>
            <param name="name">The name of entries whose values are to be concatenated. Must not be null.</param>
            <param name="separator">The separator to use for concatenation. Must not be null.</param>
        </member>
        <member name="T:Google.Api.Gax.Grpc.CallSettingsExtensions">
            <summary>
            Extension methods for <see cref="T:Google.Api.Gax.Grpc.CallSettings"/>.
            All methods accept a null first parameter as valid unless stated otherwise.
            </summary>
        </member>
        <member name="F:Google.Api.Gax.Grpc.CallSettingsExtensions.QuotaProjectHeaderName">
            <summary>
            The header used to send the project ID used for billing and quotas.
            The value should be set through the credential or the client builder,
            never explicitly as a header.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Grpc.CallSettingsExtensions.MergedWith(Google.Api.Gax.Grpc.CallSettings,Google.Api.Gax.Grpc.CallSettings)">
            <summary>
            This method merges the settings in <paramref name="overlaid"/> with those in
            <paramref name="original"/>, with <paramref name="overlaid"/> taking priority.
            If both arguments are null, the result is null. If one argument is null,
            the other argument is returned. Otherwise, a new object is created with a property-wise
            overlay, where null values do not override non-null values.
            Any header mutations are combined, however: the mutation from the original is
            performed, then the mutation in the overlay.
            </summary>
            <param name="original">Original settings. May be null.</param>
            <param name="overlaid">Settings to overlay. May be null.</param>
            <returns>A merged set of call settings, or null if both parameters are null.</returns>
        </member>
        <member name="M:Google.Api.Gax.Grpc.CallSettingsExtensions.WithCancellationToken(Google.Api.Gax.Grpc.CallSettings,System.Threading.CancellationToken)">
            <summary>
            Returns a new <see cref="T:Google.Api.Gax.Grpc.CallSettings"/> with the specified cancellation token,
            merged with the (optional) original settings specified by <paramref name="settings"/>.
            </summary>
            <param name="settings">Original settings. May be null, in which case the returned settings
            will only contain the cancellation token.</param>
            <param name="cancellationToken">Cancellation token for the new call settings.</param>
            <returns>A new set of call settings.</returns>
        </member>
        <member name="M:Google.Api.Gax.Grpc.CallSettingsExtensions.WithExpiration(Google.Api.Gax.Grpc.CallSettings,Google.Api.Gax.Expiration)">
            <summary>
            Returns a new <see cref="T:Google.Api.Gax.Grpc.CallSettings"/> with the specified expiration,
            merged with the (optional) original settings specified by <paramref name="settings"/>.
            </summary>
            <param name="settings">Original settings. May be null, in which case the returned settings
            will only contain the expiration.</param>
            <param name="expiration">Expiration to use in the returned settings, possibly as part of a retry. May be null,
            in which case any expiration in <paramref name="settings"/> is not present in the new call settings. If
            both this and <paramref name="settings"/> are null, the return value is null.</param>
            <returns>A new set of call settings with the specified expiration, or null of both parameters are null.</returns>
        </member>
        <member name="M:Google.Api.Gax.Grpc.CallSettingsExtensions.WithRetry(Google.Api.Gax.Grpc.CallSettings,Google.Api.Gax.Grpc.RetrySettings)">
            <summary>
            Returns a new <see cref="T:Google.Api.Gax.Grpc.CallSettings"/> with the specified retry settings,
            merged with the (optional) original settings specified by <paramref name="settings"/>.
            </summary>
            <param name="settings">Original settings. May be null, in which case the returned settings
            will only contain call timing.</param>
            <param name="retry">Call timing for the new call settings.
            This may be null, in which case any retry settings in <paramref name="settings"/> are
            not present in the new call settings. If both this and <paramref name="settings"/> are null,
            the return value is null.</param>
            <returns>A new set of call settings, or null if both parameters are null.</returns>
        </member>
        <member name="M:Google.Api.Gax.Grpc.CallSettingsExtensions.WithHeader(Google.Api.Gax.Grpc.CallSettings,System.String,System.String)">
            <summary>
            Returns a new <see cref="T:Google.Api.Gax.Grpc.CallSettings"/> with the specified header,
            merged with the (optional) original settings specified by <paramref name="settings"/>.
            </summary>
            <remarks>
            Existing headers in settings will not be overritten, that is, if settings
            already contains a header for <paramref name="name"/> the new value
            will be included in that header's set of values, even if it was already present
            in settings for the header with the given name.
            </remarks>
            <param name="settings">Original settings. May be null, in which case the returned settings
            will only contain the header.</param>
            <param name="name">Header name. Must not be null.</param>
            <param name="value">Header value. Must not be null.</param>
            <returns>A new set of call settings including the specified header.</returns>
        </member>
        <member name="M:Google.Api.Gax.Grpc.CallSettingsExtensions.WithResponseMetadataHandler(Google.Api.Gax.Grpc.CallSettings,System.Action{Grpc.Core.Metadata})">
            <summary>
            
            </summary>
            <param name="handler"></param>
            <param name="settings"></param>
            <returns></returns>
        </member>
        <member name="M:Google.Api.Gax.Grpc.CallSettingsExtensions.WithTrailingMetadataHandler(Google.Api.Gax.Grpc.CallSettings,System.Action{Grpc.Core.Metadata})">
            <summary>
            
            </summary>
            <param name="settings"></param>
            <param name="handler"></param>
            <returns></returns>
        </member>
        <member name="M:Google.Api.Gax.Grpc.CallSettingsExtensions.WithDeadline(Google.Api.Gax.Grpc.CallSettings,System.DateTime)">
            <summary>
            Returns a <see cref="T:Google.Api.Gax.Grpc.CallSettings"/> which will have the specified deadline.
            </summary>
            <param name="settings">Existing settings. May be null, meaning there are currently no settings.</param>
            <param name="deadline">The deadline for the new settings.</param>
            <returns>A new <see cref="T:Google.Api.Gax.Grpc.CallSettings"/> with the given deadline.</returns>
        </member>
        <member name="M:Google.Api.Gax.Grpc.CallSettingsExtensions.WithTimeout(Google.Api.Gax.Grpc.CallSettings,System.TimeSpan)">
            <summary>
            Returns a <see cref="T:Google.Api.Gax.Grpc.CallSettings"/> which will have the specified timeout.
            </summary>
            <param name="settings">Existing settings. May be null, meaning there are currently no settings.</param>
            <param name="timeout">The timeout for the new settings.</param>
            <returns>A new <see cref="T:Google.Api.Gax.Grpc.CallSettings"/> with the given timeout.</returns>
        </member>
        <member name="M:Google.Api.Gax.Grpc.CallSettingsExtensions.WithEarlierDeadline(Google.Api.Gax.Grpc.CallSettings,System.Nullable{System.DateTime},Google.Api.Gax.IClock)">
            <summary>
            Returns a <see cref="T:Google.Api.Gax.Grpc.CallSettings"/> which will have an effective deadline of at least <paramref name="deadline"/>.
            If <paramref name="settings"/> already observes an earlier deadline (with respect to <paramref name="clock"/>),
            or if <paramref name="deadline"/> is null, the original settings will be returned.
            </summary>
            <param name="settings">Existing settings. May be null, meaning there are currently no settings.</param>
            <param name="deadline">Deadline to enforce. May be null, meaning there is no deadline to enforce.</param>
            <param name="clock">The clock to use when computing deadlines. Must not be null.</param>
            <returns>The call settings to use to observe the given deadline.</returns>
        </member>
        <member name="M:Google.Api.Gax.Grpc.CallSettingsExtensions.ValidateNoRetry(Google.Api.Gax.Grpc.CallSettings)">
            <summary>
            Throws <see cref="T:System.InvalidOperationException"/> if <paramref name="callSettings"/> is non-null and has a Retry;
            otherwise returns the parameter.
            </summary>
            <param name="callSettings">The call settings for the call. May be null.</param>
            <returns><paramref name="callSettings"/></returns>
        </member>
        <member name="M:Google.Api.Gax.Grpc.CallSettingsExtensions.ToCallOptions(Google.Api.Gax.Grpc.CallSettings,Google.Api.Gax.IClock)">
            <summary>
            Transfers settings contained in this into a <see cref="T:Grpc.Core.CallOptions"/>.
            </summary>
            <param name="callSettings">The call settings for the call. May be null.</param>
            <param name="clock">The clock to use for deadline calculation.</param>
            <returns>A <see cref="T:Grpc.Core.CallOptions"/> configured from this <see cref="T:Google.Api.Gax.Grpc.CallSettings"/>.</returns>
        </member>
        <member name="M:Google.Api.Gax.Grpc.CallSettingsExtensions.CheckMetadata(Grpc.Core.Metadata)">
            <summary>
            Method used to check that the headers set by the uer are valid.
            Current only checks that the <code>x-goog-user-project</code> header is not set
            directly by the user. It should be set either through the credential or the client builder.
            </summary>
            <param name="metadata">The user set headers.</param>
        </member>
        <member name="M:Google.Api.Gax.Grpc.CallSettingsExtensions.CheckHeader(System.String)">
            <summary>
            Method used to check if a header set by the uer is valid.
            Current only checks that the <code>x-goog-user-project</code> header is not set
            directly by the user. It should be set either through the credential or the client builder.
            </summary>
            <param name="header">The user set header.</param>
        </member>
        <member name="T:Google.Api.Gax.Grpc.ChannelBaseExtensions">
            <summary>
            Extension methods for <see cref="T:Grpc.Core.ChannelBase"/>.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Grpc.ChannelBaseExtensions.Shutdown(Grpc.Core.ChannelBase,Microsoft.Extensions.Logging.ILogger)">
            <summary>
            Shuts down a channel semi-synchronously. This method initially calls <see cref="M:System.IDisposable.Dispose"/>
            if the channel implements <see cref="T:System.IDisposable"/> (e.g. in the case of <see cref="T:Grpc.Net.Client.GrpcChannel"/>)
            and then calls <see cref="M:Grpc.Core.ChannelBase.ShutdownAsync"/>. This method does not wait for the task
            to complete, but observes any exceptions (whether the task is faulted or canceled), optionally logging
            them to <paramref name="logger"/>.
            </summary>
            <param name="channel">The channel to shut down.</param>
            <param name="logger">An optional logger to record any errors during asynchronous shutdown.</param>
        </member>
        <member name="T:Google.Api.Gax.Grpc.ChannelPool">
            <summary>
            A pool of channels for the same service, but with potentially different endpoints. Each endpoint
            has a single channel. All channels created by this pool use default application credentials.
            This class is thread-safe.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Grpc.ChannelPool.#ctor(Google.Api.Gax.Grpc.ServiceMetadata)">
            <summary>
            Creates a channel pool which will use the given service metadata to determine scopes and the use of self-signed JWTs.
            </summary>
            <param name="serviceMetadata">The metadata for the service that this pool will be used with. Must not be null.</param>
        </member>
        <member name="M:Google.Api.Gax.Grpc.ChannelPool.ShutdownChannelsAsync">
            <summary>
            Shuts down all the currently-allocated channels asynchronously. This does not prevent the channel
            pool from being used later on, but the currently-allocated channels will not be reused.
            </summary>
            <returns>A task which will complete when all the (current) channels have been shut down.</returns>
        </member>
        <member name="M:Google.Api.Gax.Grpc.ChannelPool.GetChannel(Google.Api.Gax.Grpc.GrpcAdapter,System.String,System.String,Google.Api.Gax.Grpc.GrpcChannelOptions)">
            <summary>
            Returns a channel from this pool, creating a new one if there is no channel
            already associated with <paramref name="endpoint"/>.
            The specified channel options are applied, but only those options.
            </summary>
            <param name="grpcAdapter">The gRPC implementation to use. Must not be null.</param>
            <param name="universeDomain">The universe domain configured for the service client,
            to validate against the one configured for the credential. Must not be null.</param>
            <param name="endpoint">The endpoint to connect to. Must not be null.</param>
            <param name="channelOptions">The channel options to include. May be null.</param>
            <returns>A channel for the specified endpoint.</returns>
        </member>
        <member name="M:Google.Api.Gax.Grpc.ChannelPool.GetChannelAsync(Google.Api.Gax.Grpc.GrpcAdapter,System.String,System.String,Google.Api.Gax.Grpc.GrpcChannelOptions,System.Threading.CancellationToken)">
            <summary>
            Asynchronously returns a channel from this pool, creating a new one if there is no channel
            already associated with <paramref name="endpoint"/>.
            The specified channel options are applied, but only those options.
            </summary>
            <param name="grpcAdapter">The gRPC implementation to use. Must not be null.</param>
            <param name="universeDomain">The universe domain configured for the service client,
            to validate against the one configured for the credential. Must not be null.</param>
            <param name="endpoint">The endpoint to connect to. Must not be null.</param>
            <param name="channelOptions">The channel options to include. May be null.</param>
            <param name="cancellationToken">A cancellation token for the operation.</param>
            <returns>A task representing the asynchronous operation. The value of the completed
            task will be channel for the specified endpoint.</returns>
        </member>
        <member name="T:Google.Api.Gax.Grpc.ClientBuilderBase`1">
            <summary>
            Base class for API-specific builders.
            </summary>
            <typeparam name="TClient">The type of client created by this builder.</typeparam>
        </member>
        <member name="P:Google.Api.Gax.Grpc.ClientBuilderBase`1.DefaultOptions">
            <summary>
            The default gRPC options.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.Grpc.ClientBuilderBase`1.ServiceMetadata">
            <summary>
            The metadata associated with the service that this client will make requests to.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.Grpc.ClientBuilderBase`1.UniverseDomain">
            <summary>
            The universe domain to connect to, or null to use the default universe domain <see cref="F:Google.Api.Gax.Grpc.ServiceMetadata.DefaultUniverseDomain"/>.
            </summary>
            <remarks>
            <para>
            <see cref="P:Google.Api.Gax.Grpc.ClientBuilderBase`1.UniverseDomain"/> is used to build the endpoint to connect to, unless <see cref="P:Google.Api.Gax.Grpc.ClientBuilderBase`1.Endpoint"/>
            is set, in which case <see cref="P:Google.Api.Gax.Grpc.ClientBuilderBase`1.Endpoint"/> will be used without further modification.
            </para>
            <para>
            If default credentials or one of <see cref="P:Google.Api.Gax.Grpc.ClientBuilderBase`1.GoogleCredential"/>, <see cref="P:Google.Api.Gax.Grpc.ClientBuilderBase`1.CredentialsPath"/> or <see cref="P:Google.Api.Gax.Grpc.ClientBuilderBase`1.JsonCredentials"/>
            is used, <see cref="M:Google.Apis.Auth.OAuth2.GoogleCredential.GetUniverseDomain"/> should be:
            <list type="bullet">
            <item>The same as <see cref="P:Google.Api.Gax.Grpc.ClientBuilderBase`1.UniverseDomain"/> if <see cref="P:Google.Api.Gax.Grpc.ClientBuilderBase`1.UniverseDomain"/> has been set.</item>
            <item><see cref="F:Google.Api.Gax.Grpc.ServiceMetadata.DefaultUniverseDomain"/> otherwise.</item>
            </list>
            </para>
            </remarks>
        </member>
        <member name="P:Google.Api.Gax.Grpc.ClientBuilderBase`1.EffectiveUniverseDomain">
            <summary>
            Effective, and known, universe domain to connect to.
            Will be null if <see cref="P:Google.Api.Gax.Grpc.ClientBuilderBase`1.UniverseDomain"/> is not set and there's nothing to gain
            from defaulting to <see cref="F:Google.Api.Gax.Grpc.ServiceMetadata.DefaultUniverseDomain"/>. For instance,
            if <see cref="P:Google.Api.Gax.Grpc.ClientBuilderBase`1.CallInvoker"/> has been set, which is self contained, we really don't know
            the universe we are in, and we really don't care.
            </summary>
            <remarks>
            This will be:
            <list type="bullet">
            <item>The value of <see cref="P:Google.Api.Gax.Grpc.ClientBuilderBase`1.UniverseDomain"/> if set.</item>
            <item>null if <see cref="P:Google.Api.Gax.Grpc.ClientBuilderBase`1.CallInvoker"/> is set.</item>
            <item>null if both <see cref="P:Google.Api.Gax.Grpc.ClientBuilderBase`1.Endpoint"/> and one of the non <see cref="P:Google.Api.Gax.Grpc.ClientBuilderBase`1.GoogleCredential"/> options is used.</item>
            <item><see cref="F:Google.Api.Gax.Grpc.ServiceMetadata.DefaultUniverseDomain"/> otherwise.</item>
            </list>
            Note that we don't validate here that the builder properties are set in a valid combination. <see cref="P:Google.Api.Gax.Grpc.ClientBuilderBase`1.EffectiveUniverseDomain"/>
            is to be called from <see cref="M:Google.Api.Gax.Grpc.ClientBuilderBase`1.CreateCallInvoker"/> and <see cref="M:Google.Api.Gax.Grpc.ClientBuilderBase`1.CreateCallInvokerAsync(System.Threading.CancellationToken)"/> after <see cref="M:Google.Api.Gax.Grpc.ClientBuilderBase`1.Validate"/>
            has been called.
            </remarks>
        </member>
        <member name="P:Google.Api.Gax.Grpc.ClientBuilderBase`1.Endpoint">
            <summary>
            The endpoint to connect to, or null to use the default endpoint.
            </summary>
            <remarks>
            If <see cref="P:Google.Api.Gax.Grpc.ClientBuilderBase`1.Endpoint"/> is set, its value will take preference over that built using <see cref="P:Google.Api.Gax.Grpc.ClientBuilderBase`1.UniverseDomain"/>.
            </remarks>
        </member>
        <member name="P:Google.Api.Gax.Grpc.ClientBuilderBase`1.Logger">
            <summary>
            The logger to include in the client, if any.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.Grpc.ClientBuilderBase`1.Scopes">
            <summary>
            The scopes to use, or null to use the default scopes.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.Grpc.ClientBuilderBase`1.ChannelCredentials">
            <summary>
            The channel credentials to use, or null if credentials are being provided in a different way.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.Grpc.ClientBuilderBase`1.CredentialsPath">
            <summary>
            The path to the credentials file to use, or null if credentials are being provided in a different way.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.Grpc.ClientBuilderBase`1.JsonCredentials">
            <summary>
            The credentials to use as a JSON string, or null if credentials are being provided in a different way.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.Grpc.ClientBuilderBase`1.GoogleCredential">
            <summary>
            The credentials to use as a <see cref="P:Google.Api.Gax.Grpc.ClientBuilderBase`1.GoogleCredential"/>, or null if credentials are being provided in
            a different way. Note that unlike <see cref="P:Google.Api.Gax.Grpc.ClientBuilderBase`1.ChannelCredentials"/> and <see cref="P:Google.Api.Gax.Grpc.ClientBuilderBase`1.TokenAccessMethod"/>,
            settings for <see cref="P:Google.Api.Gax.Grpc.ClientBuilderBase`1.Scopes"/>, <see cref="P:Google.Api.Gax.Grpc.ClientBuilderBase`1.QuotaProject"/> and self-signed JWTs will be applied to this
            credential (creating a new one), in the same way as for application default credentials and credentials
            specified using <see cref="P:Google.Api.Gax.Grpc.ClientBuilderBase`1.CredentialsPath"/> or <see cref="P:Google.Api.Gax.Grpc.ClientBuilderBase`1.JsonCredentials"/>.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.Grpc.ClientBuilderBase`1.Credential">
            <summary>
            The credentials to use in "raw" form, for conversion into channel credentials. No other settings
            (e.g. <see cref="P:Google.Api.Gax.Grpc.ClientBuilderBase`1.QuotaProject"/> or <see cref="P:Google.Api.Gax.Grpc.ClientBuilderBase`1.Scopes"/>) are applied to these credentials.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.Grpc.ClientBuilderBase`1.TokenAccessMethod">
            <summary>
            The token access method to use, or null if credentials are being provided in a different way.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.Grpc.ClientBuilderBase`1.CallInvoker">
            <summary>
            The call invoker to use, or null to create the call invoker when the client is built.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.Grpc.ClientBuilderBase`1.UserAgent">
            <summary>
            A custom user agent to specify in the channel metadata, or null if no custom user agent is required.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.Grpc.ClientBuilderBase`1.GrpcAdapter">
            <summary>
            The gRPC implementation to use, or null to use the default implementation.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.Grpc.ClientBuilderBase`1.EmulatorDetection">
            <summary>
            The emulator detection policy to apply when building a client. Derived classes which support
            emulators should create public properties which delegate to this one. The default value is
            <see cref="F:Google.Api.Gax.EmulatorDetection.None"/>.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.Grpc.ClientBuilderBase`1.ApiKey">
            <summary>
            An API key to use as an alternative to a full credential.
            </summary>
            <remarks>
            This is protected as not all APIs support API keys. APIs which support API keys
            should declare a new public property (also called ApiKey) in the concrete client builder class,
            and ensure they call <see cref="M:Google.Api.Gax.Grpc.ClientBuilderBase`1.GetEffectiveSettings``1(``0)"/> to potentially specify the API key header
            via CallSettings.
            </remarks>
        </member>
        <member name="P:Google.Api.Gax.Grpc.ClientBuilderBase`1.QuotaProject">
            <summary>
            The GCP project ID that should be used for quota and billing purposes.
            May be null.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.Grpc.ClientBuilderBase`1.GrpcChannelOptions">
            <summary>
            Any custom channel options to merge with the default options.
            If an option specified both here and in the default options, the custom option
            will take priority. This property may be null (the default) in which case the default
            options are used.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.Grpc.ClientBuilderBase`1.LastCreatedChannel">
            <summary>
            Returns the channel created last time any of the <see cref="M:Google.Api.Gax.Grpc.ClientBuilderBase`1.Build"/>-related methods
            were called, or <code>null</code> if the last-created client did not require channel creation.
            If a channel is obtained from a channel pool, this does not count as channel creation.
            This property is useful when multiple clients are created and the calling code wishes to clean up
            resources associated with the channel.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.Grpc.ClientBuilderBase`1.EffectiveEndpoint">
            <summary>
            Returns the service endpoint taking into account <see cref="P:Google.Api.Gax.Grpc.ClientBuilderBase`1.Endpoint"/> and <see cref="P:Google.Api.Gax.Grpc.ClientBuilderBase`1.UniverseDomain"/>.
            Override this property in a concrete builder type if an endpoint may be customized further.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Grpc.ClientBuilderBase`1.#ctor(Google.Api.Gax.Grpc.ServiceMetadata)">
            <summary>
            Creates a new instance with no explicit settings.
            This takes the value of <see cref="P:Google.Api.Gax.Grpc.ClientBuilderBase`1.UseJwtAccessWithScopes" /> from <paramref name="serviceMetadata"/>.
            </summary>
            <param name="serviceMetadata">The metadata for the service that the client will be used with. Must not be null.</param>
        </member>
        <member name="M:Google.Api.Gax.Grpc.ClientBuilderBase`1.CopyCommonSettings``1(Google.Api.Gax.Grpc.ClientBuilderBase{``0})">
            <summary>
            Copies common settings from the specified builder into this one. This is a shallow copy.
            </summary>
            <typeparam name="TOther">The other client type</typeparam>
            <param name="source">The builder to copy from.</param>
        </member>
        <member name="M:Google.Api.Gax.Grpc.ClientBuilderBase`1.CopySettingsForEmulator(Google.Api.Gax.Grpc.ClientBuilderBase{`0})">
            <summary>
            Copies common settings from the specified builder, expecting that any settings around
            credentials and endpoints will be set by the caller, along with any client-specific settings.
            Emulator detection is not copied, to avoid infinite recursion when building.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Grpc.ClientBuilderBase`1.GetEffectiveSettings``1(``0)">
            <summary>
            Returns the effective settings for this builder, taking into account API keys and any other properties
            which may require additional settings (typically via <see cref="P:Google.Api.Gax.Grpc.ServiceSettingsBase.CallSettings"/>).
            </summary>
            <remarks>This method only needs to be called if the concrete builder type knows that the settings may
            need to be modified (e.g. if the API supports API keys). It should typically be called as
            <c>GetEffectiveSettings(Settings?.Clone())</c>.</remarks>
            <typeparam name="T">The concrete settings type, derived from <see cref="T:Google.Api.Gax.Grpc.ServiceSettingsBase"/>, with a
            parameterless constructor that can be used to construct a new default instance.</typeparam>
            <param name="settings">A clone of the existing settings specified in the concrete builder type. May be null.</param>
            <returns>The appropriate effective settings for this builder, or null if no settings have been
            provided and no other properties require additional settings. Note that clone operations are provided
            on a per-concrete-type basis, so this method must accept already-cloned settings.</returns>
        </member>
        <member name="M:Google.Api.Gax.Grpc.ClientBuilderBase`1.Validate">
            <summary>
            Validates that the builder is in a consistent state for building. For example, it's invalid to call
            <see cref="M:Google.Api.Gax.Grpc.ClientBuilderBase`1.Build"/> on an instance which has both JSON credentials and a credentials path specified.
            </summary>
            <exception cref="T:System.InvalidOperationException">The builder is in an invalid state.</exception>
        </member>
        <member name="M:Google.Api.Gax.Grpc.ClientBuilderBase`1.GetEmulatorEnvironment(System.Collections.Generic.IEnumerable{System.String},System.Collections.Generic.IEnumerable{System.String},System.Func{System.String,System.String})">
            <summary>
            Performs basic emulator detection and validation based on the given environment variables.
            This method is expected to be called by a derived class that supports emulators, in order to perform the common
            work of checking whether the emulator is configured in the environment.
            </summary>
            <remarks>
            <para>
            If the emulator should not be used, either due to being disabled in <see cref="P:Google.Api.Gax.Grpc.ClientBuilderBase`1.EmulatorDetection"/> or
            the appropriate environment variables not being set, this method returns null.
            </para>
            <para>
            Otherwise, a dictionary is returned mapping every value in <paramref name="allEmulatorEnvironmentVariables"/> to the value in
            the environment. Any missing, empty or whitespace-only values are mapped to a null reference in the returned dictionary, but
            the entry will still be present (so callers can use an indexer with the returned dictionary for every environment variable passed in).
            </para>
            </remarks>
            <exception cref="T:System.InvalidOperationException">The configuration is inconsistent, e.g. due to some environment variables
            being set but not all the required ones, or any environment variables being set in a production-only environment.</exception>
            <param name="requiredEmulatorEnvironmentVariables">Required emulator environment variables.</param>
            <param name="allEmulatorEnvironmentVariables">All emulator environment variables.</param>
            <param name="environmentVariableProvider">The provider used to retrieve environment variables. This is used to faciliate testing, and defaults
            to using <see cref="M:System.Environment.GetEnvironmentVariable(System.String)"/>.</param>
            <returns>A key/value mapping of the emulator environment variables to their values, or null if the emulator should not be used.</returns>
        </member>
        <member name="M:Google.Api.Gax.Grpc.ClientBuilderBase`1.ValidateAtMostOneNotNull(System.String,System.Object[])">
            <summary>
            Validates that at most one of the given values is not null.
            </summary>
            <param name="message">The message if the condition is violated.</param>
            <param name="values">The values to check for nullity.</param>
            <exception cref="T:System.InvalidOperationException">More than one value is null.</exception>
        </member>
        <member name="M:Google.Api.Gax.Grpc.ClientBuilderBase`1.ValidateOptionExcludesOthers(System.String,System.Object,System.Object[])">
            <summary>
            Validates that if <paramref name="controlling"/> is not null, then every value in <paramref name="values"/> is null.
            </summary>
            <param name="message">The message if the condition is violated.</param>
            <param name="controlling">The value controlling whether or not any other value can be non-null.</param>
            <param name="values">The values checked for non-nullity if <paramref name="controlling"/> is non-null.</param>
        </member>
        <member name="M:Google.Api.Gax.Grpc.ClientBuilderBase`1.CreateCallInvoker">
            <summary>
            Creates a call invoker synchronously. Override this method in a concrete builder type if more
            call invoker mechanisms are supported.
            This implementation calls <see cref="M:Google.Api.Gax.Grpc.ClientBuilderBase`1.GetChannelCredentials"/> if no call invoker is specified.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Grpc.ClientBuilderBase`1.CreateCallInvokerAsync(System.Threading.CancellationToken)">
            <summary>
            Creates a call invoker asynchronously. Override this method in a concrete builder type if more
            call invoker mechanisms are supported.
            This implementation calls <see cref="M:Google.Api.Gax.Grpc.ClientBuilderBase`1.GetChannelCredentialsAsync(System.Threading.CancellationToken)"/> if no call
            invoker is specified.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Grpc.ClientBuilderBase`1.GetChannelCredentials">
            <summary>
            Obtains channel credentials synchronously. Override this method in a concrete builder type if more
            credential mechanisms are supported.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Grpc.ClientBuilderBase`1.GetChannelCredentialsAsync(System.Threading.CancellationToken)">
            <summary>
            Obtains channel credentials asynchronously. Override this method in a concrete builder type if more
            credential mechanisms are supported.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Grpc.ClientBuilderBase`1.MaybeGetSimpleChannelCredentials">
            <summary>
            Obtains channel credentials synchronously if they've been supplied in a ready-to-go fashion.
            This avoids code duplication in the sync and async paths.
            Returns null if the credentials aren't available.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Grpc.ClientBuilderBase`1.GetChannelPool">
            <summary>
            Returns the channel pool to use when no other options are specified. This method is not called unless
            <see cref="P:Google.Api.Gax.Grpc.ClientBuilderBase`1.CanUseChannelPool"/> returns true, so if a channel pool is unavailable, override that property
            to return false and throw an exception from this method.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.Grpc.ClientBuilderBase`1.EffectiveGrpcAdapter">
            <summary>
            Returns the effective <see cref="P:Google.Api.Gax.Grpc.ClientBuilderBase`1.GrpcAdapter"/> for this builder,
            using the <see cref="P:Google.Api.Gax.Grpc.ClientBuilderBase`1.GrpcAdapter"/> property if that is set, or the appropriate fallback adapter
            for <see cref="P:Google.Api.Gax.Grpc.ClientBuilderBase`1.ServiceMetadata"/> otherwise.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Grpc.ClientBuilderBase`1.GetChannelOptions">
            <summary>
            Returns the options to use when creating a channel, taking <see cref="P:Google.Api.Gax.Grpc.ClientBuilderBase`1.GrpcChannelOptions"/>
            into account.
            </summary>
            <returns>The options to use when creating a channel.</returns>
        </member>
        <member name="P:Google.Api.Gax.Grpc.ClientBuilderBase`1.CanUseChannelPool">
            <summary>
            Returns whether or not a channel pool can be used if a channel is required. The default behavior is to return
            true if and only if no quota project, scopes, credentials or token access method have been specified and 
            if UseJwtAccessWithScopes flag matches the flag in ChannelPool. 
            Derived classes should override this property if there are other reasons why the channel pool should not be used.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.Grpc.ClientBuilderBase`1.UseJwtAccessWithScopes">
            <summary>
            Returns whether or not self-signed JWTs will be used over OAuth tokens when OAuth scopes are explicitly set.        
            </summary>
            <remarks>
            In the base implementation, this defaults to <c>true</c>. Subclasses may add code in their own constructors
            to make the default effectively <c>false</c>, however.
            </remarks>
        </member>
        <member name="M:Google.Api.Gax.Grpc.ClientBuilderBase`1.Build">
            <summary>
            Builds the resulting client.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Grpc.ClientBuilderBase`1.Configure(System.IServiceProvider)">
            <summary>
            Populates properties based on those set via dependency injection.
            </summary>
            <remarks>
            <para>
            If gRPC adapters are configured in <paramref name="provider"/>, the first one that supports
            the <see cref="P:Google.Api.Gax.Grpc.ClientBuilderBase`1.ServiceMetadata"/> will be used.
            </para>
            <para>
            Credentials are only requested from dependency injection if they are not already set
            via any of <see cref="P:Google.Api.Gax.Grpc.ClientBuilderBase`1.ChannelCredentials"/>, <see cref="P:Google.Api.Gax.Grpc.ClientBuilderBase`1.CredentialsPath"/>,
            <see cref="P:Google.Api.Gax.Grpc.ClientBuilderBase`1.JsonCredentials"/>, <see cref="P:Google.Api.Gax.Grpc.ClientBuilderBase`1.Credential"/>, <see cref="P:Google.Api.Gax.Grpc.ClientBuilderBase`1.GoogleCredential"/>
            or <see cref="P:Google.Api.Gax.Grpc.ClientBuilderBase`1.TokenAccessMethod"/>.
            </para>
            <para>
            If credentials are requested, they are tried in the following order:
            </para>
            <list type="bullet">
            <item>ChannelCredentials</item>
            <item>ICredential</item>
            <item>GoogleCredential</item>
            </list>
            </remarks>
            <param name="provider">The service provider to request dependencies from.</param>
        </member>
        <member name="M:Google.Api.Gax.Grpc.ClientBuilderBase`1.BuildAsync(System.Threading.CancellationToken)">
            <summary>
            Builds the resulting client asynchronously.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Grpc.ClientBuilderBase`1.Build(System.IServiceProvider)">
            <summary>
            Populates properties supplied via dependency injection, then builds a client.
            </summary>
            <param name="provider">The service provider to request dependencies from. Must not be null.</param>
            <returns>An API client configured from this builder.</returns>
        </member>
        <member name="M:Google.Api.Gax.Grpc.ClientBuilderBase`1.BuildAsync(System.IServiceProvider,System.Threading.CancellationToken)">
            <summary>
            Populates properties supplied via dependency injection, then builds a client asynchronously.
            </summary>
            <param name="provider">The service provider to request dependencies from. Must not be null.</param>
            <param name="cancellationToken">A token to cancel the operation.</param>
            <returns>An API client configured from this builder.</returns>
        </member>
        <member name="M:Google.Api.Gax.Grpc.ClientBuilderBase`1.CreateChannel(System.String,Grpc.Core.ChannelCredentials)">
            <summary>
            Returns a <see cref="T:Grpc.Core.ChannelBase"/> as created by <see cref="P:Google.Api.Gax.Grpc.ClientBuilderBase`1.EffectiveGrpcAdapter"/>
            for the specified endpoint and credentials, using the gRPC channel options from
            <see cref="M:Google.Api.Gax.Grpc.ClientBuilderBase`1.GetChannelOptions"/>.
            </summary>
            <remarks>
            This is only useful in very specific situations where a known channel is required;
            <see cref="M:Google.Api.Gax.Grpc.ClientBuilderBase`1.CreateCallInvoker"/> and its async equivalent are more usually useful.
            This implementation sets the <see cref="P:Google.Api.Gax.Grpc.ClientBuilderBase`1.LastCreatedChannel"/> property, and so should
            any overriding implementations.
            </remarks>
            <param name="endpoint">The endpoint of the channel.</param>
            <param name="credentials">The channel credentials.</param>
            <returns>The channel created by the gRPC adapter.</returns>
        </member>
        <member name="T:Google.Api.Gax.Grpc.ClientHelper">
            <summary>
            Common helper code shared by clients. This class is primarily expected to be used from generated code.
            </summary>
        </member>
        <member name="F:Google.Api.Gax.Grpc.ClientHelper._versionCallSettings">
            <summary>
            Call settings specifying headers for the client version (x-goog-api-client) and
            optionally the API version (x-goog-api-version).
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Grpc.ClientHelper.#ctor(Google.Api.Gax.Grpc.ServiceSettingsBase,Microsoft.Extensions.Logging.ILogger)">
            <summary>
            Constructs a helper from the given settings.
            Behavior is undefined if settings are changed after construction.
            </summary>
            <remarks>
            This constructor will be removed in the next major version of GAX.
            </remarks>
            <param name="settings">The service settings.</param>
            <param name="logger">The logger to use for API calls</param>
        </member>
        <member name="M:Google.Api.Gax.Grpc.ClientHelper.#ctor(Google.Api.Gax.Grpc.ClientHelper.Options)">
            <summary>
            Constructs a helper from the given options. See the properties in <see cref="T:Google.Api.Gax.Grpc.ClientHelper.Options"/>
            for validity constraints.
            </summary>
            <param name="options">The options for the helper.</param>
        </member>
        <member name="P:Google.Api.Gax.Grpc.ClientHelper.Clock">
            <summary>
            The clock used for timing of retries and deadlines. This is never
            null; if the clock isn't specified in the settings, this property
            will return the <see cref="T:Google.Api.Gax.SystemClock"/> instance.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.Grpc.ClientHelper.Scheduler">
            <summary>
            The scheduler used for delays of retries. This is never
            null; if the scheduler isn't specified in the settings, this property
            will return the <see cref="T:Google.Api.Gax.SystemScheduler"/> instance.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.Grpc.ClientHelper.Logger">
            <summary>
            The logger used by this instance, or null if it does not perform logging.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Grpc.ClientHelper.BuildApiCall``2(System.String,System.Func{``0,Grpc.Core.CallOptions,Grpc.Core.AsyncUnaryCall{``1}},System.Func{``0,Grpc.Core.CallOptions,``1},Google.Api.Gax.Grpc.CallSettings)">
            <summary>
            Builds an <see cref="T:Google.Api.Gax.Grpc.ApiCall"/> given suitable underlying async and sync calls.
            </summary>
            <typeparam name="TRequest">Request type, which must be a protobuf message.</typeparam>
            <typeparam name="TResponse">Response type, which must be a protobuf message.</typeparam>
            <param name="methodName">The underlying method name, for diagnostic purposes.</param>
            <param name="asyncGrpcCall">The underlying synchronous gRPC call.</param>
            <param name="syncGrpcCall">The underlying asynchronous gRPC call.</param>
            <param name="perMethodCallSettings">The default method call settings.</param>
            <returns>An API call to proxy to the RPC calls</returns>
        </member>
        <member name="M:Google.Api.Gax.Grpc.ClientHelper.BuildApiCall``2(System.String,System.Func{``0,Grpc.Core.CallOptions,Grpc.Core.AsyncServerStreamingCall{``1}},Google.Api.Gax.Grpc.CallSettings)">
            <summary>
            Builds an <see cref="T:Google.Api.Gax.Grpc.ApiServerStreamingCall"/> given a suitable underlying server streaming call.
            </summary>
            <typeparam name="TRequest">Request type, which must be a protobuf message.</typeparam>
            <typeparam name="TResponse">Response type, which must be a protobuf message.</typeparam>
            <param name="methodName">The underlying method name, for diagnostic purposes.</param>
            <param name="grpcCall">The underlying gRPC server streaming call.</param>
            <param name="perMethodCallSettings">The default method call settings.</param>
            <returns>An API call to proxy to the RPC calls</returns>
        </member>
        <member name="M:Google.Api.Gax.Grpc.ClientHelper.BuildApiCall``2(System.String,System.Func{Grpc.Core.CallOptions,Grpc.Core.AsyncDuplexStreamingCall{``0,``1}},Google.Api.Gax.Grpc.CallSettings,Google.Api.Gax.Grpc.BidirectionalStreamingSettings)">
            <summary>
            Builds an <see cref="T:Google.Api.Gax.Grpc.ApiBidirectionalStreamingCall"/> given a suitable underlying duplex call.
            </summary>
            <param name="methodName">The underlying method name, for diagnostic purposes.</param>
            <typeparam name="TRequest">Request type, which must be a protobuf message.</typeparam>
            <typeparam name="TResponse">Response type, which must be a protobuf message.</typeparam>
            <param name="grpcCall">The underlying gRPC duplex streaming call.</param>
            <param name="perMethodCallSettings">The default method call settings.</param>
            <param name="streamingSettings">The default streaming settings.</param>
            <returns>An API call to proxy to the RPC calls</returns>
        </member>
        <member name="M:Google.Api.Gax.Grpc.ClientHelper.BuildApiCall``2(System.String,System.Func{Grpc.Core.CallOptions,Grpc.Core.AsyncClientStreamingCall{``0,``1}},Google.Api.Gax.Grpc.CallSettings,Google.Api.Gax.Grpc.ClientStreamingSettings)">
            <summary>
            Builds an <see cref="T:Google.Api.Gax.Grpc.ApiClientStreamingCall"/> given a suitable underlying client streaming call.
            </summary>
            <typeparam name="TRequest">Request type, which must be a protobuf message.</typeparam>
            <typeparam name="TResponse">Response type, which must be a protobuf message.</typeparam>
            <param name="methodName">The underlying method name, for diagnostic purposes.</param>
            <param name="grpcCall">The underlying gRPC client streaming call.</param>
            <param name="perMethodCallSettings">The default method call settings.</param>
            <param name="streamingSettings">The default streaming settings.</param>
            <returns>An API call to proxy to the RPC calls</returns>
        </member>
        <member name="T:Google.Api.Gax.Grpc.ClientHelper.Options">
            <summary>
            The options used to construct a <see cref="T:Google.Api.Gax.Grpc.ClientHelper"/>.
            </summary>
            <remarks>
            This class is designed to allow additional configuration to be introduced without
            either overloading the ClientHelper constructor or making breaking changes.
            </remarks>
        </member>
        <member name="P:Google.Api.Gax.Grpc.ClientHelper.Options.Settings">
            <summary>
            The service settings. This must not be null when the options
            are passed to the <see cref="T:Google.Api.Gax.Grpc.ClientHelper"/> constructor.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.Grpc.ClientHelper.Options.Logger">
            <summary>
            The logger to use, if any. This may be null.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.Grpc.ClientHelper.Options.ApiVersion">
            <summary>
            The API version to send in the x-goog-api-version header, if any. This may be null.
            </summary>
        </member>
        <member name="T:Google.Api.Gax.Grpc.ClientStreamingBase`2">
            <summary>
            Base class for the client-side streaming RPC methods. This wraps the
            request stream in a buffer, allowing multiple requests to be written without waiting for them
            to be transmitted.
            </summary>
            <remarks>
            To avoid memory leaks, users must dispose of gRPC streams.
            </remarks>
            <typeparam name="TRequest">RPC request type</typeparam>
            <typeparam name="TResponse">RPC response type</typeparam>
        </member>
        <member name="P:Google.Api.Gax.Grpc.ClientStreamingBase`2.GrpcCall">
            <summary>
            The underlying gRPC client streaming call.
            Warning: DO NOT USE <c>GrpcCall.RequestStream</c> at all if using
            <see cref="M:Google.Api.Gax.Grpc.ClientStreamingBase`2.TryWriteAsync(`0)"/>, <see cref="M:Google.Api.Gax.Grpc.ClientStreamingBase`2.WriteAsync(`0)"/>,
            <see cref="M:Google.Api.Gax.Grpc.ClientStreamingBase`2.TryWriteAsync(`0,Grpc.Core.WriteOptions)"/> , or <see cref="M:Google.Api.Gax.Grpc.ClientStreamingBase`2.WriteAsync(`0,Grpc.Core.WriteOptions)"/>.
            Doing so will cause conflict with the write-buffer used within the <c>[Try]WriteAsync</c> methods.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Grpc.ClientStreamingBase`2.TryWriteAsync(`0)">
            <summary>
            Writes a message to the stream, if there is enough space in the buffer and <see cref="M:Google.Api.Gax.Grpc.ClientStreamingBase`2.WriteCompleteAsync"/>
            hasn't already been called. The same write options will be used as for the previous message.
            </summary>
            <param name="message">The message to write.</param>
            <returns><c>null</c> if the message queue is full or the stream has already been completed;
            otherwise, a <see cref="T:System.Threading.Tasks.Task"/> which will complete when the message has been written to the stream.</returns>
        </member>
        <member name="M:Google.Api.Gax.Grpc.ClientStreamingBase`2.WriteAsync(`0)">
            <summary>
            Writes a message to the stream, if there is enough space in the buffer and <see cref="M:Google.Api.Gax.Grpc.ClientStreamingBase`2.WriteCompleteAsync"/>
            hasn't already been called. The same write options will be used as for the previous message.
            </summary>
            <param name="message">The message to write.</param>
            <exception cref="T:System.InvalidOperationException">There isn't enough space left in the buffer,
            or <see cref="M:Google.Api.Gax.Grpc.ClientStreamingBase`2.WriteCompleteAsync"/> has already been called.</exception>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> which will complete when the message has been written to the stream.</returns>
        </member>
        <member name="M:Google.Api.Gax.Grpc.ClientStreamingBase`2.TryWriteAsync(`0,Grpc.Core.WriteOptions)">
            <summary>
            Writes a message to the stream, if there is enough space in the buffer and <see cref="M:Google.Api.Gax.Grpc.ClientStreamingBase`2.WriteCompleteAsync"/>
            hasn't already been called.
            </summary>
            <param name="message">The message to write.</param>
            <param name="options">The write options to use for this message.</param>
            <returns><c>null</c> if the message queue is full or the stream has already been completed.</returns>
        </member>
        <member name="M:Google.Api.Gax.Grpc.ClientStreamingBase`2.WriteAsync(`0,Grpc.Core.WriteOptions)">
            <summary>
            Writes a message to the stream, if there is enough space in the buffer and <see cref="M:Google.Api.Gax.Grpc.ClientStreamingBase`2.WriteCompleteAsync"/>
            hasn't already been called.
            </summary>
            <param name="message">The message to write.</param>
            <param name="options">The write options to use for this message.</param>
            <exception cref="T:System.InvalidOperationException">There isn't enough space left in the buffer,
            or <see cref="M:Google.Api.Gax.Grpc.ClientStreamingBase`2.WriteCompleteAsync"/> has already been called.</exception>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> which will complete when the message has been written to the stream.</returns>
        </member>
        <member name="M:Google.Api.Gax.Grpc.ClientStreamingBase`2.TryWriteCompleteAsync">
            <summary>
            Completes the stream when all buffered messages have been sent.
            Only the first call to this method on any instance will have any effect;
            subsequent calls will return <c>null</c>.
            </summary>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> which will complete when the stream has finished being completed;
            or <c>null</c> if this method has already been called.</returns>
        </member>
        <member name="M:Google.Api.Gax.Grpc.ClientStreamingBase`2.WriteCompleteAsync">
            <summary>
            Completes the stream when all buffered messages have been sent. This method can only be called
            once, and further messages cannot be written after it has been called.
            </summary>
            <exception cref="T:System.InvalidOperationException">This method has already been called.</exception>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> which will complete when the stream has finished being completed.</returns>
        </member>
        <member name="M:Google.Api.Gax.Grpc.ClientStreamingBase`2.Dispose">
            <summary>
            Disposes of the underlying gRPC call. There is no need to dispose of both the wrapper
            and the underlying call; it's typically simpler to dispose of the wrapper with a
            <code>using</code> statement as the wrapper is returned by client libraries.
            </summary>
            <remarks>The default implementation just calls Dispose on the result of <see cref="P:Google.Api.Gax.Grpc.ClientStreamingBase`2.GrpcCall"/>.</remarks>
        </member>
        <member name="P:Google.Api.Gax.Grpc.ClientStreamingBase`2.ResponseAsync">
            <summary>
            Asynchronous call result. This task will only complete after
            <see cref="M:Google.Api.Gax.Grpc.ClientStreamingBase`2.WriteCompleteAsync"/> has already been called.
            </summary>
            <returns>A task representing the asynchronous operation. The result of the completed task
            will be the RPC response.</returns>
        </member>
        <member name="T:Google.Api.Gax.Grpc.ClientStreamingSettings">
            <summary>
            Settings for client streaming.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Grpc.ClientStreamingSettings.#ctor(System.Int32)">
            <summary>
            Configure settings for client streaming.
            </summary>
            <param name="bufferedClientWriterCapacity"></param>
        </member>
        <member name="P:Google.Api.Gax.Grpc.ClientStreamingSettings.BufferedClientWriterCapacity">
            <summary>
            The capacity of the write buffer, that locally buffers streaming requests
            before they are sent to the server.
            </summary>
        </member>
        <member name="T:Google.Api.Gax.Grpc.DefaultChannelCredentialsCache">
            <summary>
            Caches the application default channel credentials for an individual service, applying a specified set of scopes when required.
            </summary>
        </member>
        <member name="F:Google.Api.Gax.Grpc.DefaultChannelCredentialsCache._lazyScopedDefaultChannelCredentials">
            <summary>
            Lazily-created task to retrieve the default application channel credentials. Once completed, this
            task can be used whenever channel credentials are required. The returned task always runs in the
            thread pool, so its result can be used synchronously from synchronous methods without risk of deadlock.
            The same channel credentials are used by all pools. The field is initialized in the constructor, as it uses
            _scopes, and you can't refer to an instance field within an instance field initializer.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Grpc.DefaultChannelCredentialsCache.#ctor(Google.Api.Gax.Grpc.ServiceMetadata)">
            <summary>
            Creates a cache which will apply the specified scopes to the default application credentials
            if they require any.
            </summary>
            <param name="serviceMetadata">The metadata of the service the credentials will be used with. Must not be null.</param>
        </member>
        <member name="T:Google.Api.Gax.Grpc.ForwardingCallInvoker`1">
            <summary>
            Non-generic static class just for generic type inference, to make it easier to construct instances
            of <see cref="T:Google.Api.Gax.Grpc.ForwardingCallInvoker`4"/>.
            </summary>
            <typeparam name="TSourceRequest">The type of the expected source request. Specifying this explicitly
            is usually sufficient to allow type inference to work for generic methods within this class.</typeparam>
        </member>
        <member name="M:Google.Api.Gax.Grpc.ForwardingCallInvoker`1.Create``3(Grpc.Core.CallInvoker,System.String,Grpc.Core.Method{``1,``2},System.Func{`0,``1},System.Func{`0,``2,``0})">
            <summary>
            Creates a <see cref="T:Google.Api.Gax.Grpc.ForwardingCallInvoker`4"/>
            to forward a single unary call to a method in an existing <see cref="T:Grpc.Core.CallInvoker"/>.
            </summary>
            <typeparam name="TSourceResponse">The type of the source response, i.e. the response we expect to return
            to the caller at the end of the method.</typeparam>
            <typeparam name="TTargetRequest">The type of the target request, i.e. the request we'll forward on
            to <paramref name="originalInvoker"/>.</typeparam>
            <typeparam name="TTargetResponse">The type of the target response, i.e. the response we expect to be
            returned by <paramref name="originalInvoker"/>.</typeparam>
            <param name="originalInvoker">The original invoker that will handle the request.</param>
            <param name="sourceMethodFullName">The full name (as reported by <see cref="P:Grpc.Core.Method`2.FullName"/>)
            of the method to forward.</param>
            <param name="targetMethod">The target method to call on <paramref name="originalInvoker"/>.</param>
            <param name="requestConverter">A delegate to convert source requests to target requests.</param>
            <param name="responseConverter">A delegate to convert target responses to source responses, with
            additional context being provided from the original source request.</param>
            <returns>A call invoker forwarding the specified call.</returns>
        </member>
        <member name="T:Google.Api.Gax.Grpc.ForwardingCallInvoker`4">
            <summary>
            A <see cref="T:Grpc.Core.CallInvoker"/> which forwards specific calls to an existing invoker,
            transforming the requests and responses as necessary.
            </summary>
            <remarks>
            <para>
            It would be cleaner to write an interceptor for this functionality, but that doesn't allow for the request/response types to be changed.
            </para>
            <para>
            Currently, only unary methods are supported, and only a single method can be forwarded. Any
            other method results in an <see cref="T:Grpc.Core.RpcException"/> with a status code of <see cref="F:Grpc.Core.StatusCode.Unimplemented"/>.
            The type parameters of this class would make it hard to support multiple calls, which is why the factory
            class method has a return type of <see cref="T:Grpc.Core.CallInvoker"/> rather than the concrete class: we may implement
            multi-method support via composition of multiple call invokers.
            </para>
            </remarks>
        </member>
        <member name="T:Google.Api.Gax.Grpc.Gcp.ChannelRef">
            <summary>
            Keeps record of channel affinity and active streams.
            This class is thread-safe.
            </summary>
        </member>
        <member name="T:Google.Api.Gax.Grpc.Gcp.GcpCallInvoker">
            <summary>
            Call invoker which can fan calls out to multiple underlying channels
            based on request properties.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Grpc.Gcp.GcpCallInvoker.#ctor(Google.Api.Gax.Grpc.ServiceMetadata,System.String,Grpc.Core.ChannelCredentials,Google.Api.Gax.Grpc.GrpcChannelOptions,Google.Api.Gax.Grpc.Gcp.ApiConfig,Google.Api.Gax.Grpc.GrpcAdapter)">
            <summary>
            Initializes a new instance.
            </summary>
            <param name="serviceMetadata">The metadata for the service that this call invoker will be used with. Must not be null.</param>
            <param name="target">Target of the underlying grpc channels. Must not be null.</param>
            <param name="credentials">Credentials to secure the underlying grpc channels. Must not be null.</param>
            <param name="options">Channel options to be used by the underlying grpc channels. Must not be null.</param>
            <param name="apiConfig">The API config to apply. Must not be null.</param>
            <param name="adapter">The adapter to use to create channels. Must not be null.</param>
        </member>
        <member name="M:Google.Api.Gax.Grpc.Gcp.GcpCallInvoker.AsyncClientStreamingCall``2(Grpc.Core.Method{``0,``1},System.String,Grpc.Core.CallOptions)">
            <summary>
            Invokes a client streaming call asynchronously.
            In client streaming scenario, client sends a stream of requests and server responds with a single response.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Grpc.Gcp.GcpCallInvoker.AsyncDuplexStreamingCall``2(Grpc.Core.Method{``0,``1},System.String,Grpc.Core.CallOptions)">
            <summary>
            Invokes a duplex streaming call asynchronously.
            In duplex streaming scenario, client sends a stream of requests and server responds with a stream of responses.
            The response stream is completely independent and both side can be sending messages at the same time.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Grpc.Gcp.GcpCallInvoker.AsyncServerStreamingCall``2(Grpc.Core.Method{``0,``1},System.String,Grpc.Core.CallOptions,``0)">
            <summary>
            Invokes a server streaming call asynchronously.
            In server streaming scenario, client sends on request and server responds with a stream of responses.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Grpc.Gcp.GcpCallInvoker.AsyncUnaryCall``2(Grpc.Core.Method{``0,``1},System.String,Grpc.Core.CallOptions,``0)">
            <summary>
            Invokes a simple remote call asynchronously.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Grpc.Gcp.GcpCallInvoker.BlockingUnaryCall``2(Grpc.Core.Method{``0,``1},System.String,Grpc.Core.CallOptions,``0)">
            <summary>
            Invokes a simple remote call in a blocking fashion.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Grpc.Gcp.GcpCallInvoker.ShutdownAsync">
            <summary>
            Shuts down the all channels in the underlying channel pool cleanly. It is strongly
            recommended to shutdown all previously created channels before exiting from the process.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Grpc.Gcp.GcpCallInvoker.GetChannelRefsForTest">
            <summary>
            Returns a deep clone of the internal list of channel references.
            This method should only be used in tests.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Grpc.Gcp.GcpCallInvoker.GetChannelRefsByAffinityKeyForTest">
            <summary>
            Returns a deep clone of the internal dictionary of channel references by affinity key.
            This method should only be used in tests.
            </summary>
        </member>
        <member name="T:Google.Api.Gax.Grpc.Gcp.GcpCallInvokerPool">
            <summary>
            A pool of GCP call invokers for the same service, but with potentially different endpoints and/or channel options.
            Each endpoint/options pair has a single <see cref="T:Google.Api.Gax.Grpc.Gcp.GcpCallInvoker"/>. All call invokers created by this pool use
            default application credentials. This class is thread-safe.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Grpc.Gcp.GcpCallInvokerPool.#ctor(Google.Api.Gax.Grpc.ServiceMetadata)">
            <summary>
            Creates a call invoker pool which will use the given service metadata to determine scopes
            and self-signed JWT support.
            </summary>
            <param name="serviceMetadata">The metadata for the service that this pool will be used with. Must not be null.</param>
        </member>
        <member name="M:Google.Api.Gax.Grpc.Gcp.GcpCallInvokerPool.ShutdownChannelsAsync">
            <summary>
            Shuts down all the open channels of all currently-allocated call invokers asynchronously. This does not prevent
            the call invoker pool from being used later on, but the currently-allocated call invokers will not be reused.
            </summary>
            <returns>A task which will complete when all the (current) channels have been shut down.</returns>
        </member>
        <member name="M:Google.Api.Gax.Grpc.Gcp.GcpCallInvokerPool.GetCallInvoker(System.String,System.String,Google.Api.Gax.Grpc.GrpcChannelOptions,Google.Api.Gax.Grpc.Gcp.ApiConfig,Google.Api.Gax.Grpc.GrpcAdapter)">
            <summary>
            Returns a call invoker from this pool, creating a new one if there is no call invoker
            already associated with <paramref name="endpoint"/> and <paramref name="options"/>.
            </summary>
            <param name="universeDomain">The universe domain configured for the service client,
            to validate against the one configured for the credential. Must not be null.</param>
            <param name="endpoint">The endpoint to connect to. Must not be null.</param>
            <param name="options">The options to use for each channel created by the call invoker. May be null.</param>
            <param name="apiConfig">The API configuration used to determine channel keys. Must not be null.</param>
            <param name="adapter">The gRPC adapter to use to create call invokers. Must not be null.</param>
            <returns>A call invoker for the specified endpoint.</returns>
        </member>
        <member name="M:Google.Api.Gax.Grpc.Gcp.GcpCallInvokerPool.GetCallInvokerAsync(System.String,System.String,Google.Api.Gax.Grpc.GrpcChannelOptions,Google.Api.Gax.Grpc.Gcp.ApiConfig,Google.Api.Gax.Grpc.GrpcAdapter,System.Threading.CancellationToken)">
            <summary>
            Asynchronously returns a call invoker from this pool, creating a new one if there is no call invoker
            already associated with <paramref name="endpoint"/> and <paramref name="options"/>.
            </summary>
            <param name="universeDomain">The universe domain configured for the service client,
            to validate against the one configured for the credential. Must not be null.</param>
            <param name="endpoint">The endpoint to connect to. Must not be null.</param>
            <param name="options">The options to use for each channel created by the call invoker. May be null.</param>
            <param name="apiConfig">The API configuration used to determine channel keys. Must not be null.</param>
            <param name="adapter">The gRPC adapter to use to create call invokers. Must not be null.</param>
            <param name="cancellationToken">The cancellation token to cancel the operation.</param>
            <returns>A task representing the asynchronous operation. The value of the completed
            task will be a call invoker for the specified endpoint.</returns>
        </member>
        <member name="M:Google.Api.Gax.Grpc.Gcp.GcpCallInvokerPool.GetCallInvoker(System.String,Google.Api.Gax.Grpc.GrpcChannelOptions,Google.Api.Gax.Grpc.Gcp.ApiConfig,Google.Api.Gax.Grpc.GrpcAdapter)">
            <summary>
            Returns a call invoker from this pool, creating a new one if there is no call invoker
            already associated with <paramref name="endpoint"/> and <paramref name="options"/>.
            </summary>
            <param name="endpoint">The endpoint to connect to. Must not be null.</param>
            <param name="options">The options to use for each channel created by the call invoker. May be null.</param>
            <param name="apiConfig">The API configuration used to determine channel keys. Must not be null.</param>
            <param name="adapter">The gRPC adapter to use to create call invokers. Must not be null.</param>
            <returns>A call invoker for the specified endpoint.</returns>
        </member>
        <member name="M:Google.Api.Gax.Grpc.Gcp.GcpCallInvokerPool.GetCallInvokerAsync(System.String,Google.Api.Gax.Grpc.GrpcChannelOptions,Google.Api.Gax.Grpc.Gcp.ApiConfig,Google.Api.Gax.Grpc.GrpcAdapter)">
            <summary>
            Asynchronously returns a call invoker from this pool, creating a new one if there is no call invoker
            already associated with <paramref name="endpoint"/> and <paramref name="options"/>.
            </summary>
            <param name="endpoint">The endpoint to connect to. Must not be null.</param>
            <param name="options">The options to use for each channel created by the call invoker. May be null.</param>
            <param name="apiConfig">The API configuration used to determine channel keys. Must not be null.</param>
            <param name="adapter">The gRPC adapter to use to create call invokers. Must not be null.</param>
            <returns>A task representing the asynchronous operation. The value of the completed
            task will be a call invoker for the specified endpoint.</returns>
        </member>
        <member name="T:Google.Api.Gax.Grpc.Gcp.GcpClientResponseStream`2">
            <summary>
             A wrapper class for handling post process for server streaming responses.
            </summary>
            <typeparam name="TRequest">The type representing the request.</typeparam>
            <typeparam name="TResponse">The type representing the response.</typeparam>
        </member>
        <member name="T:Google.Api.Gax.Grpc.Gcp.GrpcGcpReflection">
            <summary>Holder for reflection information generated from grpc_gcp.proto</summary>
        </member>
        <member name="P:Google.Api.Gax.Grpc.Gcp.GrpcGcpReflection.Descriptor">
            <summary>File descriptor for grpc_gcp.proto</summary>
        </member>
        <member name="F:Google.Api.Gax.Grpc.Gcp.ApiConfig.ChannelPoolFieldNumber">
            <summary>Field number for the "channel_pool" field.</summary>
        </member>
        <member name="P:Google.Api.Gax.Grpc.Gcp.ApiConfig.ChannelPool">
            <summary>
            The channel pool configurations.
            </summary>
        </member>
        <member name="F:Google.Api.Gax.Grpc.Gcp.ApiConfig.MethodFieldNumber">
            <summary>Field number for the "method" field.</summary>
        </member>
        <member name="P:Google.Api.Gax.Grpc.Gcp.ApiConfig.Method">
            <summary>
            The method configurations.
            </summary>
        </member>
        <member name="F:Google.Api.Gax.Grpc.Gcp.ChannelPoolConfig.MaxSizeFieldNumber">
            <summary>Field number for the "max_size" field.</summary>
        </member>
        <member name="P:Google.Api.Gax.Grpc.Gcp.ChannelPoolConfig.MaxSize">
            <summary>
            The max number of channels in the pool.
            </summary>
        </member>
        <member name="F:Google.Api.Gax.Grpc.Gcp.ChannelPoolConfig.IdleTimeoutFieldNumber">
            <summary>Field number for the "idle_timeout" field.</summary>
        </member>
        <member name="P:Google.Api.Gax.Grpc.Gcp.ChannelPoolConfig.IdleTimeout">
            <summary>
            The idle timeout (seconds) of channels without bound affinity sessions.
            </summary>
        </member>
        <member name="F:Google.Api.Gax.Grpc.Gcp.ChannelPoolConfig.MaxConcurrentStreamsLowWatermarkFieldNumber">
            <summary>Field number for the "max_concurrent_streams_low_watermark" field.</summary>
        </member>
        <member name="P:Google.Api.Gax.Grpc.Gcp.ChannelPoolConfig.MaxConcurrentStreamsLowWatermark">
            <summary>
            The low watermark of max number of concurrent streams in a channel.
            New channel will be created once it get hit, until we reach the max size
            of the channel pool.
            </summary>
        </member>
        <member name="F:Google.Api.Gax.Grpc.Gcp.MethodConfig.NameFieldNumber">
            <summary>Field number for the "name" field.</summary>
        </member>
        <member name="P:Google.Api.Gax.Grpc.Gcp.MethodConfig.Name">
            <summary>
            A fully qualified name of a gRPC method, or a wildcard pattern ending
            with .*, such as foo.bar.A, foo.bar.*. Method configs are evaluated
            sequentially, and the first one takes precedence.
            </summary>
        </member>
        <member name="F:Google.Api.Gax.Grpc.Gcp.MethodConfig.AffinityFieldNumber">
            <summary>Field number for the "affinity" field.</summary>
        </member>
        <member name="P:Google.Api.Gax.Grpc.Gcp.MethodConfig.Affinity">
            <summary>
            The channel affinity configurations.
            </summary>
        </member>
        <member name="F:Google.Api.Gax.Grpc.Gcp.AffinityConfig.CommandFieldNumber">
            <summary>Field number for the "command" field.</summary>
        </member>
        <member name="P:Google.Api.Gax.Grpc.Gcp.AffinityConfig.Command">
            <summary>
            The affinity command applies on the selected gRPC methods.
            </summary>
        </member>
        <member name="F:Google.Api.Gax.Grpc.Gcp.AffinityConfig.AffinityKeyFieldNumber">
            <summary>Field number for the "affinity_key" field.</summary>
        </member>
        <member name="P:Google.Api.Gax.Grpc.Gcp.AffinityConfig.AffinityKey">
            <summary>
            The field path of the affinity key in the request/response message.
            For example: "f.a", "f.b.d", etc.
            </summary>
        </member>
        <member name="T:Google.Api.Gax.Grpc.Gcp.AffinityConfig.Types">
            <summary>Container for nested types declared in the AffinityConfig message type.</summary>
        </member>
        <member name="F:Google.Api.Gax.Grpc.Gcp.AffinityConfig.Types.Command.Bound">
            <summary>
            The annotated method will be required to be bound to an existing session
            to execute the RPC. The corresponding &lt;affinity_key_field_path> will be
            used to find the affinity key from the request message.
            </summary>
        </member>
        <member name="F:Google.Api.Gax.Grpc.Gcp.AffinityConfig.Types.Command.Bind">
            <summary>
            The annotated method will establish the channel affinity with the channel
            which is used to execute the RPC. The corresponding
            &lt;affinity_key_field_path> will be used to find the affinity key from the
            response message.
            </summary>
        </member>
        <member name="F:Google.Api.Gax.Grpc.Gcp.AffinityConfig.Types.Command.Unbind">
            <summary>
            The annotated method will remove the channel affinity with the channel
            which is used to execute the RPC. The corresponding
            &lt;affinity_key_field_path> will be used to find the affinity key from the
            request message.
            </summary>
        </member>
        <member name="T:Google.Api.Gax.Grpc.GoogleCredentialExtensions">
            <summary>
            Extension methods for Google credential universe domain validation.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Grpc.GoogleCredentialExtensions.ToChannelCredentials(Google.Apis.Auth.OAuth2.GoogleCredential,System.String)">
            <summary>
            Returns a channel credential based on <paramref name="googleCredential"/>, that will validate its own universe domain
            against <paramref name="universeDomain"/>.
            </summary>
            <param name="googleCredential">The Google credential to build the channel credentials from. Must not be null.</param>
            <param name="universeDomain">The universe domain to validate against. Must not be null.
            <see cref="M:Google.Apis.Auth.OAuth2.GoogleCredential.GetUniverseDomainAsync(System.Threading.CancellationToken)"/> should result in the same value as this.</param>
        </member>
        <member name="T:Google.Api.Gax.Grpc.GrpcAdapter">
            <summary>
            Interoperability layer for different gRPC transports. Concrete subclasses are
            <see cref="T:Google.Api.Gax.Grpc.GrpcCoreAdapter"/>, <see cref="T:Google.Api.Gax.Grpc.GrpcNetClientAdapter"/> and <see cref="T:Google.Api.Gax.Grpc.Rest.RestGrpcAdapter"/>.
            </summary>
            <remarks>
            This is an abstract class with all concrete subclasses internal, and internal abstract methods
            to prevent instantiation elsewhere. (The abstraction itself may change over time.)
            </remarks>
        </member>
        <member name="F:Google.Api.Gax.Grpc.GrpcAdapter.s_defaultGrpcTransportFactory">
            <summary>
            The lazily-evaluated adapter to use for services with ApiTransports.Grpc.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Grpc.GrpcAdapter.SupportsApi(Google.Api.Gax.Grpc.ServiceMetadata)">
            <summary>
            Returns whether or not this adapter supports the specified service.
            </summary>
            <param name="serviceMetadata">The service metadata. Must not be null.</param>
            <returns><c>true</c> if this adapter supports the given service; <c>false</c> otherwise.</returns>
        </member>
        <member name="M:Google.Api.Gax.Grpc.GrpcAdapter.GetFallbackAdapter(Google.Api.Gax.Grpc.ServiceMetadata)">
            <summary>
            Returns a fallback provider suitable for the given API
            </summary>
            <param name="serviceMetadata">The descriptor of the API. Must not be null.</param>
            <returns>A suitable GrpcAdapter for the given API, preferring the use of the binary gRPC transport where available.</returns>
        </member>
        <member name="M:Google.Api.Gax.Grpc.GrpcAdapter.CreateChannel(Google.Api.Gax.Grpc.ServiceMetadata,System.String,Grpc.Core.ChannelCredentials,Google.Api.Gax.Grpc.GrpcChannelOptions)">
            <summary>
            Creates a channel for the given endpoint, using the given credentials and options.
            </summary>
            <param name="serviceMetadata">The metadata for the service. Must not be null.</param>
            <param name="endpoint">The endpoint to connect to. Must not be null.</param>
            <param name="credentials">The channel credentials to use. Must not be null.</param>
            <param name="options">The channel options to use. Must not be null.</param>
            <returns>A channel for the specified settings.</returns>
        </member>
        <member name="M:Google.Api.Gax.Grpc.GrpcAdapter.CreateChannelImpl(Google.Api.Gax.Grpc.ServiceMetadata,System.String,Grpc.Core.ChannelCredentials,Google.Api.Gax.Grpc.GrpcChannelOptions)">
            <summary>
            Creates a channel for the given endpoint, using the given credentials and options. All parameters
            are pre-validated to be non-null.
            </summary>
            <param name="apiMetadata"></param>
            <param name="endpoint">The endpoint to connect to. Will not be null.</param>
            <param name="credentials">The channel credentials to use. Will not be null.</param>
            <param name="options">The channel options to use. Will not be null.</param>
            <returns>A channel for the specified settings.</returns>
        </member>
        <member name="T:Google.Api.Gax.Grpc.GrpcChannelOptions">
            <summary>
            Portable abstraction of channel options
            </summary>
        </member>
        <member name="P:Google.Api.Gax.Grpc.GrpcChannelOptions.Empty">
            <summary>
            An empty set of channel options.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.Grpc.GrpcChannelOptions.EnableServiceConfigResolution">
            <summary>
            If non-null, explicitly enables or disables service configuration resolution.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.Grpc.GrpcChannelOptions.KeepAliveTime">
            <summary>
            If non-null, explicitly specifies the keep-alive period for the channel.
            This specifies how often a keep-alive request is sent.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.Grpc.GrpcChannelOptions.KeepAliveTimeout">
            <summary>
            If non-null, explicitly specifies the keep-alive timeout for the channel.
            This specifies how long gRPC will wait for a keep-alive response before
            assuming the channel is no longer valid, and closing it.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.Grpc.GrpcChannelOptions.PrimaryUserAgent">
            <summary>
            If non-null, explicitly specifies the primary user agent for the channel.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.Grpc.GrpcChannelOptions.MaxSendMessageSize">
            <summary>
            If non-null, explicitly specifies the maximum size in bytes that can be sent from the client, per request.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.Grpc.GrpcChannelOptions.MaxReceiveMessageSize">
            <summary>
            If non-null, explicitly specifies the maximum size in bytes that can be received from the client, per response.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.Grpc.GrpcChannelOptions.CustomOptions">
            <summary>
            Immutable list of custom options. This is never null, but may be empty.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Grpc.GrpcChannelOptions.WithPrimaryUserAgent(System.String)">
            <summary>
            Returns a new instance with the same options as this one, but with <see cref="P:Google.Api.Gax.Grpc.GrpcChannelOptions.PrimaryUserAgent"/> set to
            <paramref name="primaryUserAgent"/>.
            </summary>
            <param name="primaryUserAgent">The new primary user agent. Must not be null.</param>
            <returns>The new options.</returns>
        </member>
        <member name="M:Google.Api.Gax.Grpc.GrpcChannelOptions.WithEnableServiceConfigResolution(System.Boolean)">
            <summary>
            Returns a new instance with the same options as this one, but with <see cref="P:Google.Api.Gax.Grpc.GrpcChannelOptions.EnableServiceConfigResolution"/> set to
            <paramref name="enableServiceConfigResolution"/>.
            </summary>
            <param name="enableServiceConfigResolution">The new option for enabling service config resolution.</param>
            <returns>The new options.</returns>
        </member>
        <member name="M:Google.Api.Gax.Grpc.GrpcChannelOptions.WithKeepAliveTime(System.TimeSpan)">
            <summary>
            Returns a new instance with the same options as this one, but with <see cref="P:Google.Api.Gax.Grpc.GrpcChannelOptions.KeepAliveTime"/> set to
            <paramref name="keepAliveTime"/>.
            </summary>
            <param name="keepAliveTime">The new keep-alive time.</param>
            <returns>The new options.</returns>
        </member>
        <member name="M:Google.Api.Gax.Grpc.GrpcChannelOptions.WithKeepAliveTimeout(System.TimeSpan)">
            <summary>
            Returns a new instance with the same options as this one, but with <see cref="P:Google.Api.Gax.Grpc.GrpcChannelOptions.KeepAliveTimeout"/> set to
            <paramref name="keepAliveTimeout"/>.
            </summary>
            <param name="keepAliveTimeout">The new keep-alive timeout.</param>
            <returns>The new options.</returns>
        </member>
        <member name="M:Google.Api.Gax.Grpc.GrpcChannelOptions.WithMaxSendMessageSize(System.Int32)">
            <summary>
            Returns a new instance with the same options as this one, but with <see cref="P:Google.Api.Gax.Grpc.GrpcChannelOptions.MaxSendMessageSize"/> set to
            <paramref name="maxSendMessageSize"/>.
            </summary>
            <param name="maxSendMessageSize">The new maximum send message size, in bytes.</param>
            <returns>The new options.</returns>
        </member>
        <member name="M:Google.Api.Gax.Grpc.GrpcChannelOptions.WithMaxReceiveMessageSize(System.Int32)">
            <summary>
            Returns a new instance with the same options as this one, but with <see cref="P:Google.Api.Gax.Grpc.GrpcChannelOptions.MaxReceiveMessageSize"/> set to
            <paramref name="maxReceiveMessageSize"/>.
            </summary>
            <param name="maxReceiveMessageSize">The new maximum receive message size, in bytes.</param>
            <returns>The new options.</returns>
        </member>
        <member name="M:Google.Api.Gax.Grpc.GrpcChannelOptions.WithCustomOption(System.String,System.Int32)">
            <summary>
            Returns a new instance with the same options as this one, but with a new integer-valued <see cref="T:Google.Api.Gax.Grpc.GrpcChannelOptions.CustomOption"/>
            at the end of <see cref="P:Google.Api.Gax.Grpc.GrpcChannelOptions.CustomOptions"/>.
            </summary>
            <param name="name">The name of the new custom option. Must not be null.</param>
            <param name="value">The value of the new custom option.</param>
            <returns>The new options.</returns>
        </member>
        <member name="M:Google.Api.Gax.Grpc.GrpcChannelOptions.WithCustomOption(System.String,System.String)">
            <summary>
            Returns a new instance with the same options as this one, but with a new string-valued <see cref="T:Google.Api.Gax.Grpc.GrpcChannelOptions.CustomOption"/>
            at the end of <see cref="P:Google.Api.Gax.Grpc.GrpcChannelOptions.CustomOptions"/>.
            </summary>
            <param name="name">The name of the new custom option. Must not be null.</param>
            <param name="value">The value of the new custom option. Must not be null.</param>
            <returns>The new options.</returns>
        </member>
        <member name="M:Google.Api.Gax.Grpc.GrpcChannelOptions.WithCustomOption(Google.Api.Gax.Grpc.GrpcChannelOptions.CustomOption)">
            <summary>
            Returns a new instance with the same options as this one, but with a new integer-valued <see cref="T:Google.Api.Gax.Grpc.GrpcChannelOptions.CustomOption"/>
            at the end of <see cref="P:Google.Api.Gax.Grpc.GrpcChannelOptions.CustomOptions"/>.
            </summary>
            <param name="option">The additional custom option to include. Must not be null.</param>
            <returns>The new options.</returns>
        </member>
        <member name="M:Google.Api.Gax.Grpc.GrpcChannelOptions.MergedWith(Google.Api.Gax.Grpc.GrpcChannelOptions)">
            <summary>
            Returns a new object, with options from this object merged with <paramref name="overlaidOptions"/>.
            If an option is non-null in both objects, the one from <paramref name="overlaidOptions"/> takes priority.
            </summary>
            <param name="overlaidOptions">The overlaid options. Must not be null.</param>
            <returns>The new merged options.</returns>
        </member>
        <member name="M:Google.Api.Gax.Grpc.GrpcChannelOptions.Equals(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Google.Api.Gax.Grpc.GrpcChannelOptions.GetHashCode">
            <inheritdoc />
        </member>
        <member name="M:Google.Api.Gax.Grpc.GrpcChannelOptions.Equals(Google.Api.Gax.Grpc.GrpcChannelOptions)">
            <inheritdoc />
        </member>
        <member name="T:Google.Api.Gax.Grpc.GrpcChannelOptions.CustomOption">
            <summary>
            A custom option, with a name and a value of either a 32-bit integer or a string.
            </summary>
        </member>
        <member name="T:Google.Api.Gax.Grpc.GrpcChannelOptions.CustomOption.OptionType">
            <summary>
            Possible types of value within a custom option.
            </summary>
        </member>
        <member name="F:Google.Api.Gax.Grpc.GrpcChannelOptions.CustomOption.OptionType.Integer">
            <summary>
            Channel option with an integer value.
            </summary>
        </member>
        <member name="F:Google.Api.Gax.Grpc.GrpcChannelOptions.CustomOption.OptionType.String">
            <summary>
            Channel option with a string value.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.Grpc.GrpcChannelOptions.CustomOption.Name">
            <summary>
            Name of the option. This is never null.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.Grpc.GrpcChannelOptions.CustomOption.StringValue">
            <summary>
            Value of the option, for string options. This is never null for string options, and always
            null for other options.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.Grpc.GrpcChannelOptions.CustomOption.IntegerValue">
            <summary>
            Value of the option, for integer options, or 0 for other options.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.Grpc.GrpcChannelOptions.CustomOption.Type">
            <summary>
            The type of value represented within this option.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Grpc.GrpcChannelOptions.CustomOption.#ctor(System.String,System.Int32)">
            <summary>
            Creates a custom integer option.
            </summary>
            <param name="name">The name of the option. Must not be null.</param>
            <param name="value">Value of the option.</param>
        </member>
        <member name="M:Google.Api.Gax.Grpc.GrpcChannelOptions.CustomOption.#ctor(System.String,System.String)">
            <summary>
            Creates a custom string option.
            </summary>
            <param name="name">The name of the option. Must not be null.</param>
            <param name="value">Value of the option. Must not be null.</param>
        </member>
        <member name="M:Google.Api.Gax.Grpc.GrpcChannelOptions.CustomOption.Equals(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Google.Api.Gax.Grpc.GrpcChannelOptions.CustomOption.GetHashCode">
            <inheritdoc />
        </member>
        <member name="M:Google.Api.Gax.Grpc.GrpcChannelOptions.CustomOption.Equals(Google.Api.Gax.Grpc.GrpcChannelOptions.CustomOption)">
            <inheritdoc />
        </member>
        <member name="T:Google.Api.Gax.Grpc.GrpcCoreAdapter">
            <summary>
            Implementation of <see cref="T:Google.Api.Gax.Grpc.GrpcAdapter"/> for Grpc.Core.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Grpc.GrpcCoreAdapter.#cctor">
            <summary>
            Prevent lazy type initialization.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.Grpc.GrpcCoreAdapter.Instance">
            <summary>
            Returns the singleton instance of this class.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Grpc.GrpcCoreAdapter.CreateChannelImpl(Google.Api.Gax.Grpc.ServiceMetadata,System.String,Grpc.Core.ChannelCredentials,Google.Api.Gax.Grpc.GrpcChannelOptions)">
            <inheritdoc />
        </member>
        <member name="M:Google.Api.Gax.Grpc.GrpcCoreAdapter.CreateChannelFactory">
            <summary>
            Creates a delegate that can be used to create a channel in <see cref="M:Google.Api.Gax.Grpc.GrpcCoreAdapter.CreateChannelImpl(Google.Api.Gax.Grpc.ServiceMetadata,System.String,Grpc.Core.ChannelCredentials,Google.Api.Gax.Grpc.GrpcChannelOptions)"/>. We do this once, via
            expression trees, to avoid having to perform a lot of reflection every time we create a channel.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Grpc.GrpcCoreAdapter.ConvertOptions``1(Google.Api.Gax.Grpc.GrpcChannelOptions,System.Func{System.String,System.Int32,``0},System.Func{System.String,System.String,``0})">
            <summary>
            Converts a <see cref="T:Google.Api.Gax.Grpc.GrpcChannelOptions"/> (defined in Google.Api.Gax.Grpc) into a list
            of ChannelOption (defined in Grpc.Core). This is generic to allow the simple use of delegates
            for option factories. Internal for testing.
            </summary>
            <param name="options">The options to convert. Must not be null.</param>
            <param name="int32OptionFactory">Factory delegate to create an option from an integer value</param>
            <param name="stringOptionFactory">Factory delegate to create an option from an integer value</param>
        </member>
        <member name="T:Google.Api.Gax.Grpc.GrpcNetClientAdapter">
            <summary>
            Implementation of <see cref="T:Google.Api.Gax.Grpc.GrpcAdapter"/> for Grpc.Net.Client.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.Grpc.GrpcNetClientAdapter.Default">
            <summary>
            Returns the default instance of this class.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Grpc.GrpcNetClientAdapter.WithAdditionalOptions(System.Action{Grpc.Net.Client.GrpcChannelOptions})">
            <summary>
            Returns a new instance based on this one, but with the additional options configurer specified.
            The options configurer is called after creating the <see cref="T:Grpc.Net.Client.GrpcChannelOptions"/> from
            other settings, but before creating the <see cref="T:Grpc.Net.Client.GrpcChannel"/>.
            </summary>
            <param name="configure">An optional configuration delegate to apply to instances of <see cref="T:Grpc.Net.Client.GrpcChannelOptions"/>
            before they are provided to a <see cref="T:Grpc.Net.Client.GrpcChannel"/>, after any configuration applied by this adapter. May be null,
            in which case a new instance is returned but with the same option configurer as this one.
            </param>
            <returns>A new adapter based on this one, but with an additional channel options configuration action.</returns>
        </member>
        <member name="M:Google.Api.Gax.Grpc.GrpcNetClientAdapter.CreateChannelImpl(Google.Api.Gax.Grpc.ServiceMetadata,System.String,Grpc.Core.ChannelCredentials,Google.Api.Gax.Grpc.GrpcChannelOptions)">
            <inheritdoc />
        </member>
        <member name="T:Google.Api.Gax.Grpc.GrpcPagedAsyncEnumerable`3">
            <summary>
            An asynchronous sequence of resources, obtained lazily via API operations which retrieve a page at a time.
            </summary>
            <typeparam name="TRequest">The API request type.</typeparam>
            <typeparam name="TResponse">The API response type. Each response contains a page of resources.</typeparam>
            <typeparam name="TResource">The resource type contained within the response.</typeparam>
        </member>
        <member name="M:Google.Api.Gax.Grpc.GrpcPagedAsyncEnumerable`3.#ctor(Google.Api.Gax.Grpc.ApiCall{`0,`1},`0,Google.Api.Gax.Grpc.CallSettings)">
            <summary>
            Creates a new lazily-evaluated asynchronous sequence from the given API call, initial request, and call settings.
            </summary>
            <remarks>The request is cloned each time the sequence is evaluated.</remarks>
            <param name="apiCall">The API call made each time a page is required.</param>
            <param name="request">The initial request.</param>
            <param name="callSettings">The settings to apply to each API call.</param>
        </member>
        <member name="M:Google.Api.Gax.Grpc.GrpcPagedAsyncEnumerable`3.AsRawResponses">
            <inheritdoc/>
        </member>
        <member name="M:Google.Api.Gax.Grpc.GrpcPagedAsyncEnumerable`3.ReadPageAsync(System.Int32,System.Threading.CancellationToken)">
            <inheritdoc/>
        </member>
        <member name="M:Google.Api.Gax.Grpc.GrpcPagedAsyncEnumerable`3.GetAsyncEnumerator(System.Threading.CancellationToken)">
            <inheritdoc/>
        </member>
        <member name="T:Google.Api.Gax.Grpc.ResourceEnumerator`2">
            <summary>
            Class to effectively perform SelectMany on the pages, extracting resources.
            This allows us to avoid taking a dependency on System.Linq.Async.
            </summary>
        </member>
        <member name="T:Google.Api.Gax.Grpc.ResponseAsyncEnumerable`3">
            <summary>
            An asynchronous sequence of API responses, each containing a page of resources.
            </summary>
            <typeparam name="TRequest">The API request type.</typeparam>
            <typeparam name="TResponse">The API response type.</typeparam>
            <typeparam name="TResource">The resource type contained within the response.</typeparam>
        </member>
        <member name="M:Google.Api.Gax.Grpc.ResponseAsyncEnumerable`3.GetAsyncEnumerator(System.Threading.CancellationToken)">
            <inheritdoc />
        </member>
        <member name="T:Google.Api.Gax.Grpc.GrpcPagedEnumerable`3">
            <summary>
            A sequence of resources, obtained lazily via API operations which retrieve a page at a time.
            </summary>
            <typeparam name="TRequest">The API request type.</typeparam>
            <typeparam name="TResponse">The API response type. Each response contains a page of resources.</typeparam>
            <typeparam name="TResource">The resource type contained within the response.</typeparam>
        </member>
        <member name="M:Google.Api.Gax.Grpc.GrpcPagedEnumerable`3.#ctor(Google.Api.Gax.Grpc.ApiCall{`0,`1},`0,Google.Api.Gax.Grpc.CallSettings)">
            <summary>
            Creates a new lazily-evaluated sequence from the given API call, initial request, and call settings.
            </summary>
            <remarks>The request is cloned each time the sequence is evaluated.</remarks>
            <param name="apiCall">The API call made each time a page is required.</param>
            <param name="request">The initial request.</param>
            <param name="callSettings">The settings to apply to each API call.</param>
        </member>
        <member name="M:Google.Api.Gax.Grpc.GrpcPagedEnumerable`3.AsRawResponses">
            <inheritdoc/>
        </member>
        <member name="M:Google.Api.Gax.Grpc.GrpcPagedEnumerable`3.GetEnumerator">
            <inheritdoc/>
        </member>
        <member name="M:Google.Api.Gax.Grpc.GrpcPagedEnumerable`3.ReadPage(System.Int32)">
            <inheritdoc/>
        </member>
        <member name="T:Google.Api.Gax.Grpc.MonitoredResourceBuilder">
            <summary>
            Helper methods to build a <see cref="T:Google.Api.MonitoredResource"/> instance.
            See the <a href="https://cloud.google.com/logging/docs/api/v2/resource-list">
            Monitored Resource List</a> for details.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.Grpc.MonitoredResourceBuilder.GlobalResource">
            <summary>
            An instance of a "global" resource, with <see cref="P:Google.Api.MonitoredResource.Type"/>
            set to "global", and an empty set of <see cref="P:Google.Api.MonitoredResource.Labels"/>.
            </summary>
            <remarks>
            A new instance is returned with each call, as the returned object is mutable.
            </remarks>
        </member>
        <member name="M:Google.Api.Gax.Grpc.MonitoredResourceBuilder.FromPlatform">
            <summary>
            Builds a <see cref="T:Google.Api.MonitoredResource"/> from the auto-detected
            platform, using <see cref="M:Google.Api.Gax.Platform.Instance"/>.
            This call can block for up to 1 second.
            </summary>
            <returns>A <see cref="T:Google.Api.MonitoredResource"/> instance, populated most suitably for the given platform.</returns>
        </member>
        <member name="M:Google.Api.Gax.Grpc.MonitoredResourceBuilder.FromPlatformAsync">
            <summary>
            Builds a <see cref="T:Google.Api.MonitoredResource"/> from the auto-detected
            platform, using <see cref="M:Google.Api.Gax.Platform.Instance"/>.
            </summary>
            <returns>A task, the result of which will be a <see cref="T:Google.Api.MonitoredResource"/> instance,
            populated most suitably for the given platform.</returns>
        </member>
        <member name="M:Google.Api.Gax.Grpc.MonitoredResourceBuilder.FromPlatform(Google.Api.Gax.Platform)">
            <summary>
            Builds a suitable <see cref="T:Google.Api.MonitoredResource"/> instance, given
            <see cref="T:Google.Api.Gax.Platform"/> information.
            Use <see cref="M:Google.Api.Gax.Grpc.MonitoredResourceBuilder.FromPlatform"/> or <see cref="M:Google.Api.Gax.Grpc.MonitoredResourceBuilder.FromPlatformAsync"/> to build a
            <see cref="T:Google.Api.MonitoredResource"/> from auto-detected platform information.
            </summary>
            <param name="platform"><see cref="T:Google.Api.Gax.Platform"/> information, usually auto-detected.</param>
            <returns>A <see cref="T:Google.Api.MonitoredResource"/> instance, populated most suitably for the given platform.</returns>
        </member>
        <member name="T:Google.Api.Gax.Grpc.IPageRequest">
            <summary>
            A request for a page-streaming operation.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.Grpc.IPageRequest.PageToken">
            <summary>
            A token indicating the page to return. This is obtained from an earlier response,
            via <see cref="P:Google.Api.Gax.Grpc.IPageResponse`1.NextPageToken"/>.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.Grpc.IPageRequest.PageSize">
            <summary>
            The maximum number of elements to return in the response.
            </summary>
        </member>
        <member name="T:Google.Api.Gax.Grpc.IPageResponse`1">
            <summary>
            A response in a page-streaming operation.
            </summary>
            <typeparam name="TResource">The type of resource contained in the response.</typeparam>
        </member>
        <member name="P:Google.Api.Gax.Grpc.IPageResponse`1.NextPageToken">
            <summary>
            The token to set in the <see cref="P:Google.Api.Gax.Grpc.IPageRequest.PageToken"/> when requesting
            the next page of results.
            </summary>
        </member>
        <member name="T:Google.Api.Gax.Grpc.ProtobufUtilities">
            <summary>
            Utility methods for protobufs. This is deliberately internal; these methods may be effectively exposed
            in specific public classes, but only in a way that allows different use cases that happen to have the
            same behavior now to be separated later. Code here is also a reasonable candidate to be exposed in Google.Protobuf.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Grpc.ProtobufUtilities.IsDefaultValue(System.Object)">
            <summary>
            Determines whether the given value is a protobuf default value - i.e. if it's
            null, an empty string, a zero value (integer, numeric or enum) or an empty byte string.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Grpc.ProtobufUtilities.FormatValueAsJsonPrimitive(System.Object)">
            <summary>
            Formats a value in a way that is suitable for a header, URL path segment (after URL-encoding), or query parameter. The value
            is effectively formatted the same way it is in the JSON representation. The return value of this
            method is only guaranteed for single primitive values - not repeated fields, maps, or messages.
            </summary>
            <returns>A string representation of the given value, or null if the value is null</returns>
        </member>
        <member name="M:Google.Api.Gax.Grpc.ProtobufUtilities.RemoveWellKnownTypeQuotes(System.Object)">
            <summary>
            Format a well-known type value, and if it has quotes around it, remove them. (For some well-known types, e.g. FloatValue,
            it will sometimes be formatted as a JSON string and sometimes not.)
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Grpc.ProtobufUtilities.IsWellKnownType(Google.Protobuf.Reflection.MessageDescriptor)">
            <summary>
            Determines whether the given message descriptor represents a well-known type.
            This is an internal method in Google.Protobuf; we might consider exposing it at some point.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Grpc.ProtobufUtilities.OriginalEnumValueHelper.GetOriginalName(System.Enum)">
            <summary>
            Returns the original name of the given enum value, or null if the value is unknown.
            </summary>
        </member>
        <member name="T:Google.Api.Gax.Grpc.Rest.CredentialExtensions">
            <summary>
            Methods to convert ChannelCredentials and CallCredentials into AsyncAuthInterceptors,
            so we can ask them to populate auth headers.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Grpc.Rest.CredentialExtensions.ToAsyncAuthInterceptor(Grpc.Core.ChannelCredentials)">
            <summary>
            Returns the async auth interceptor derived from the given channel credentials, or null
            if the channel credentials don't involve an interceptor.
            </summary>
            <param name="credentials">The channel credentials to convert.</param>
        </member>
        <member name="M:Google.Api.Gax.Grpc.Rest.CredentialExtensions.ToAsyncAuthInterceptor(Grpc.Core.CallCredentials)">
            <summary>
            Returns the async auth interceptor derived from the given channel credentials, or null
            if the channel credentials don't involve an interceptor.
            </summary>
            <param name="credentials">The channel credentials to convert.</param>
        </member>
        <member name="T:Google.Api.Gax.Grpc.Rest.HttpRulePathPattern">
            <summary>
            Representation of a pattern within a google.api.http option, such as "/v4/{parent=projects/*/tenants/*}/jobs".
            The pattern is parsed once, and placeholders (such as "parent" in the above) are interpreted as fields within
            a protobuf request message. The pattern can then be formatted later within the context of a request.
            </summary>
        </member>
        <member name="F:Google.Api.Gax.Grpc.Rest.HttpRulePathPattern._segments">
            <summary>
            The path segments - these are *not* slash-separated segments, but instead they're based on fields. So
            /xyz/{a}/abc/{b} would contain four segments, "/xyz/", "{a}", "/abc/", "{b}".
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Grpc.Rest.HttpRulePathPattern.TryFormat(Google.Protobuf.IMessage)">
            <summary>
            Attempts to format the path with respect to the given request,
            returning the formatted segment or null if formatting failed due to the request data.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.Grpc.Rest.HttpRulePathPattern.FieldPaths">
            <summary>
            Names of the fields of the top-level message that are bound by this pattern.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Grpc.Rest.HttpRulePathPattern.IPathSegment.TryFormat(Google.Protobuf.IMessage)">
            <summary>
            Formats this segment in the context of the given request,
            returning null if the data in the request means that the path doesn't match.
            </summary>
        </member>
        <member name="T:Google.Api.Gax.Grpc.Rest.HttpRulePathPattern.FieldSegment">
            <summary>
            A path segment that matches a field in the request.
            </summary>
        </member>
        <member name="F:Google.Api.Gax.Grpc.Rest.HttpRulePathPattern.FieldSegment.s_fieldPathPatternSeparator">
            <summary>
            The separator between the field path and the pattern.
            </summary>
        </member>
        <member name="F:Google.Api.Gax.Grpc.Rest.HttpRulePathPattern.FieldSegment.s_fieldPathSeparator">
            <summary>
            The separator between fields within the field path.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.Grpc.Rest.HttpRulePathPattern.FieldSegment.JsonFieldPath">
            <summary>
            The field path, used to determine which fields should be populated as query parameters.
            Each element of the field path is the JSON name of the field, as it would be used
            in a query parameter.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Grpc.Rest.HttpRulePathPattern.FieldSegment.#ctor(System.String,Google.Protobuf.Reflection.MessageDescriptor)">
            <summary>
            Creates a segment representing the given field text, with respect to
            the given request descriptor.
            </summary>
            <param name="fieldText">The text of the field segment, e.g. "{foo=*}"</param>
            <param name="descriptor">The descriptor within which to find the field.</param>
        </member>
        <member name="T:Google.Api.Gax.Grpc.Rest.HttpRulePathPattern.LiteralSegment">
            <summary>
            A path segment that is just based on a literal string. This always
            succeeds, producing the same result every time.
            </summary>
        </member>
        <member name="T:Google.Api.Gax.Grpc.Rest.HttpRuleTranscoder">
            <summary>
            A transcoder for an HttpRule, including any additional bindings, which are
            applied in turn until a match is found with a <see cref="T:Google.Api.Gax.Grpc.Rest.TranscodingOutput"/> result.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Grpc.Rest.HttpRuleTranscoder.#ctor(System.String,Google.Protobuf.Reflection.MessageDescriptor,Google.Api.HttpRule)">
            <summary>
            Creates a transcoder for the given method (named only for error messages) with the specified
            request message descriptor and HttpRule. See AIP-127 (https://google.aip.dev/127) and the proto comments
            for google.api.HttpRule (https://github.com/googleapis/googleapis/blob/master/google/api/http.proto#L44-L312)
            </summary>
            <param name="methodName">Name of the method, used only for diagnostic purposes.</param>
            <param name="requestMessage">The descriptor for the message request type</param>
            <param name="rule">The HttpRule that the new transcoder should represent, excluding any additional bindings.</param>
        </member>
        <member name="M:Google.Api.Gax.Grpc.Rest.HttpRuleTranscoder.Transcode(Google.Protobuf.IMessage)">
            <summary>
            Returns the transcoding result from the first matching HttpRule, or
            null if no rules match.
            </summary>
        </member>
        <member name="T:Google.Api.Gax.Grpc.Rest.HttpRuleTranscoder.SingleRuleTranscoder">
            <summary>
            A transcoder for a single rule, ignoring additional bindings.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Grpc.Rest.HttpRuleTranscoder.SingleRuleTranscoder.Transcode(Google.Protobuf.IMessage)">
            <summary>
            Attempts to use this rule to transcode the given request.
            </summary>
            <param name="request">The request to transcode. Must not be null.</param>
            <returns>The result of transcoding, or null if the rule did not match.</returns>
        </member>
        <member name="T:Google.Api.Gax.Grpc.Rest.HttpRuleTranscoder.QueryParameterField">
            <summary>
            A field that might be transcoded as a query parameter.
            </summary>
        </member>
        <member name="F:Google.Api.Gax.Grpc.Rest.HttpRuleTranscoder.QueryParameterField._parentSelector">
            <summary>
            A delegate which accepts the original request message, and returns the parent of _field,
            or null if the field isn't present in the request.
            </summary>
        </member>
        <member name="T:Google.Api.Gax.Grpc.Rest.JsonStateTracker">
             <summary>
             Simple state tracker to indicate when a server-streamed response is "done".
             </summary>
             <remarks>
             We expect that:
             - The overall response is an array
             - The later JSON parser will perform fuller validation (e.g. this code
               won't spot that [{[{]}}] is broken.
            
             Note that this is mutable rather than us creating a new instance on each "push"
             of a character, because:
             - It's not *just* a simple state machine: we count open/close object/arrays
             - We could make it a struct containing those counters and a detailed state enum,
               it's not obvious that would be easier to use.
             </remarks>
        </member>
        <member name="M:Google.Api.Gax.Grpc.Rest.JsonStateTracker.Push(System.Char)">
            <summary>
            Pushes a single character, returning the appropriate action to be taken next.
            </summary>
        </member>
        <member name="F:Google.Api.Gax.Grpc.Rest.JsonStateTracker.State.BeforeTopArray">
            <summary>
            Before we first see [
            We stay in this state, only accepting whitespace or [ until we see the first [
            </summary>
        </member>
        <member name="F:Google.Api.Gax.Grpc.Rest.JsonStateTracker.State.JustInsideTopArray">
            <summary>
            Between response objects.
            In this state, we only accept:
            - Whitespace (stay in this state)
            - Comma (stay in this state)
            - { (move to Normal state)
            - ] (move to AfterTopArray state)
            </summary>
        </member>
        <member name="F:Google.Api.Gax.Grpc.Rest.JsonStateTracker.State.WithinObject">
            <summary>
            Not in a string token, but somewhere within the top-level array.
            </summary>
        </member>
        <member name="F:Google.Api.Gax.Grpc.Rest.JsonStateTracker.State.StringLiteralNoEscape">
            <summary>
            In a string token, but not directly after a backslash.
            </summary>
        </member>
        <member name="F:Google.Api.Gax.Grpc.Rest.JsonStateTracker.State.StringLiteralEscape">
            <summary>
            In a string token, directly after a backslash.
            (The only permitted characters to follow this are double-quote, backslash, slash, b, f, n, r, t or u.
            Although u should then be followed by four hex digits, we don't enforce that.)
            </summary>
        </member>
        <member name="F:Google.Api.Gax.Grpc.Rest.JsonStateTracker.State.Error">
            <summary>
            We have detected an error. This is unrecoverable.
            </summary>
        </member>
        <member name="F:Google.Api.Gax.Grpc.Rest.JsonStateTracker.State.AfterTopArray">
            <summary>
            After the final ]
            We can only accept whitespace after this.
            </summary>
        </member>
        <member name="T:Google.Api.Gax.Grpc.Rest.JsonStateTracker.NextAction">
            <summary>
            The action that should be taken by the caller immediately after a call to <see cref="M:Google.Api.Gax.Grpc.Rest.JsonStateTracker.Push(System.Char)"/>.
            </summary>
        </member>
        <member name="F:Google.Api.Gax.Grpc.Rest.JsonStateTracker.NextAction.SignalEndOfResponses">
            <summary>
            We've received the closing ] at the end of the top level array.
            There should be no further non-whitespace characters.
            </summary>
        </member>
        <member name="F:Google.Api.Gax.Grpc.Rest.JsonStateTracker.NextAction.ParseResponse">
            <summary>
            We've received the closing } at the end of an object directly
            within the top level array. Remember that pushed } and parse
            everything remembered since the last response was parsed.
            </summary>
        </member>
        <member name="F:Google.Api.Gax.Grpc.Rest.JsonStateTracker.NextAction.SignalError">
            <summary>
            We've detected an error. Signal this to the higher-level caller.
            We don't have details of the failure. 
            </summary>
        </member>
        <member name="F:Google.Api.Gax.Grpc.Rest.JsonStateTracker.NextAction.IgnoreAndContinue">
            <summary>
            Continue reading data and pushing it with the <see cref="M:Google.Api.Gax.Grpc.Rest.JsonStateTracker.Push(System.Char)"/> method.
            The character that has been pushed does not need to be retained.
            </summary>
        </member>
        <member name="F:Google.Api.Gax.Grpc.Rest.JsonStateTracker.NextAction.BufferAndContinue">
            <summary>
            Remember the pushed character (because it's part of a top-level object).
            Continue reading data and pushing it with the <see cref="M:Google.Api.Gax.Grpc.Rest.JsonStateTracker.Push(System.Char)"/> method.
            </summary>
        </member>
        <member name="T:Google.Api.Gax.Grpc.Rest.PartialDecodingStreamReader`1">
            <summary>
            An IAsyncStreamReader implementation that reads an array of messages
            from HTTP stream as they arrive in (partial) JSON chunks. 
            </summary>
            <typeparam name="TResponse">Type of proto messages in the stream</typeparam>
        </member>
        <member name="F:Google.Api.Gax.Grpc.Rest.PartialDecodingStreamReader`1._textReaderTask">
            <summary>
            Task which will return a reader containing the data.
            </summary>
        </member>
        <member name="F:Google.Api.Gax.Grpc.Rest.PartialDecodingStreamReader`1._responseConverter">
            <summary>
            Converter to parse each individual JSON object in the response stream.
            </summary>
        </member>
        <member name="F:Google.Api.Gax.Grpc.Rest.PartialDecodingStreamReader`1._cancellationContext">
            <summary>
            Cancellation context used to observe deadlines, original cancellation tokens from call options,
            and gRPC-method-based cancellation.
            </summary>
        </member>
        <member name="F:Google.Api.Gax.Grpc.Rest.PartialDecodingStreamReader`1._queuedResponses">
            <summary>
            Responses which have already been parsed, and are ready to return to the caller.
            </summary>
        </member>
        <member name="F:Google.Api.Gax.Grpc.Rest.PartialDecodingStreamReader`1._currentResponseBuffer">
            <summary>
            The current response that we're building up.
            </summary>
        </member>
        <member name="F:Google.Api.Gax.Grpc.Rest.PartialDecodingStreamReader`1._readBuffer">
            <summary>
            The buffer we use when reading from the text reader.
            We don't need to actually preserve state between calls,
            as we process the whole read buffer on each call, but
            this avoids allocating multiple times.
            (As an alternative, we could allocate a smaller amount on the stack
            each time.)
            </summary>
        </member>
        <member name="F:Google.Api.Gax.Grpc.Rest.PartialDecodingStreamReader`1._jsonState">
            <summary>
            Keeps track of our state within the stream of JSON responses.
            This is not a full JSON tokenizer, but has just enough logic to
            recognize "we've reached the end of one response," or "we've reached
            the end of all responses," or "something's gone wrong". (It doesn't
            try to perform complete validation, but spots unexpected data between
            elements etc.)
            </summary>
        </member>
        <member name="F:Google.Api.Gax.Grpc.Rest.PartialDecodingStreamReader`1._completed">
            <summary>
            Will be set to true when all responses have been read from the stream.
            (Any responses queued in <see cref="F:Google.Api.Gax.Grpc.Rest.PartialDecodingStreamReader`1._queuedResponses"/> should still be returned.)
            This does *not* mean we've reached the end of the data, however.
            </summary>
        </member>
        <member name="F:Google.Api.Gax.Grpc.Rest.PartialDecodingStreamReader`1._endOfData">
            <summary>
            Set to true when we've reached the end of the data. If this happens without
            <see cref="F:Google.Api.Gax.Grpc.Rest.PartialDecodingStreamReader`1._completed"/> being true, that causes an error.
            </summary>
        </member>
        <member name="F:Google.Api.Gax.Grpc.Rest.PartialDecodingStreamReader`1._exceptionInfo">
            <summary>
            Set to non-null on any failure.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.Grpc.Rest.PartialDecodingStreamReader`1.Current">
            <inheritdoc />
        </member>
        <member name="M:Google.Api.Gax.Grpc.Rest.PartialDecodingStreamReader`1.#ctor(System.Threading.Tasks.Task{System.IO.TextReader},System.Func{System.String,`0},Google.Api.Gax.Grpc.Rest.RpcCancellationContext)">
            <summary>
            Creates a new instance which will read data from the TextReader provided by
            <paramref name="textReaderTask"/>, and convert each object within a top-level JSON array
            into a response using <paramref name="responseConverter"/>.
            </summary>
            <param name="textReaderTask">A task to provide text reader returning partial JSON chunks</param>
            <param name="responseConverter">A function to transform a well-formed JSON object into the proto message.</param>
            <param name="cancellationContext">The cancellation context for the RPC.</param>
        </member>
        <member name="M:Google.Api.Gax.Grpc.Rest.PartialDecodingStreamReader`1.MoveNext(System.Threading.CancellationToken)">
            <inheritdoc />
        </member>
        <member name="M:Google.Api.Gax.Grpc.Rest.PartialDecodingStreamReader`1.ReadAndProcessData(System.Threading.CancellationToken)">
            <summary>
            Called when we don't have a queued response and haven't reached a terminal state (error or completed).
            </summary>
            <returns></returns>
        </member>
        <member name="M:Google.Api.Gax.Grpc.Rest.PartialDecodingStreamReader`1.AwaitWithCancellation``1(System.Threading.Tasks.Task{``0},System.Threading.CancellationToken)">
            <summary>
            Returns the value from the original task provided in <paramref name="task"/>, or throws
            if <paramref name="cancellationToken"/> was cancelled before the task completed.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Grpc.Rest.PartialDecodingStreamReader`1.DisposeAsync">
            <summary>
            Disposes of the underlying reader, in a fire-and-forget manner.
            The reader may not yet be available, so a task is attached to the reader-providing
            task to dispose of the reader when it becomes available.
            The <see cref="T:System.Threading.Tasks.ValueTask"/> returned by this implementation will already be completed.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Grpc.Rest.PartialDecodingStreamReader`1.Dispose">
            <summary>
            Disposes of the underlying reader, in a fire-and-forget manner, as well as the
            cancellation context.
            The reader may not yet be available, so a task is attached to the reader-providing
            task to dispose of the reader when it becomes available.
            </summary>
        </member>
        <member name="T:Google.Api.Gax.Grpc.Rest.ReadHttpResponseMessage">
            <summary>
            In <see cref="T:Grpc.Core.AsyncUnaryCall`1"/> the functions to obtain the TResponse
            and the <see cref="T:Grpc.Core.Status"/> of the call are two different functions.
            The function to obtain the response is async, but the function to obtain the
            <see cref="T:Grpc.Core.Status"/> is not.
            For being able to surface error details in <see cref="T:Grpc.Core.Status"/> we need to be
            able to call <see cref="M:System.Net.Http.HttpContent.ReadAsStringAsync"/> which is an async method,
            and thus cannot be done, without blocking, on the sync function that obtains the 
            <see cref="T:Grpc.Core.Status"/> in the <see cref="T:Grpc.Core.AsyncUnaryCall`1"/>.
            So we need to make async content reading part of sending the call and not part of
            building the TResponse.
            This class is just a convenient wrapper for passing together the <see cref="T:System.Net.Http.HttpResponseMessage"/>
            and its read response.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Grpc.Rest.ReadHttpResponseMessage.CreateRpcStatus(System.Net.HttpStatusCode,System.String)">
            <summary>
            Create an RPC status from the HTTP status code, attempting to parse the
            content as an Rpc.Status if the HTTP status indicates a failure.
            </summary>
        </member>
        <member name="T:Google.Api.Gax.Grpc.Rest.ResponseMetadataReflection">
            <summary>Holder for reflection information generated from response_metadata.proto</summary>
        </member>
        <member name="P:Google.Api.Gax.Grpc.Rest.ResponseMetadataReflection.Descriptor">
            <summary>File descriptor for response_metadata.proto</summary>
        </member>
        <member name="T:Google.Api.Gax.Grpc.Rest.Error">
            <summary>
            This message defines the error schema for Google's JSON HTTP APIs.
            </summary>
        </member>
        <member name="F:Google.Api.Gax.Grpc.Rest.Error.Error_FieldNumber">
            <summary>Field number for the "error" field.</summary>
        </member>
        <member name="P:Google.Api.Gax.Grpc.Rest.Error.Error_">
            <summary>
            The actual error payload. The nested message structure is for backward
            compatibility with [Google API Client
            Libraries](https://developers.google.com/api-client-library). It also
            makes the error more readable to developers.
            </summary>
        </member>
        <member name="T:Google.Api.Gax.Grpc.Rest.Error.Types">
            <summary>Container for nested types declared in the Error message type.</summary>
        </member>
        <member name="T:Google.Api.Gax.Grpc.Rest.Error.Types.ErrorProto">
            <summary>
            Deprecated. This message is only used by error format v1.
            </summary>
        </member>
        <member name="T:Google.Api.Gax.Grpc.Rest.Error.Types.Status">
            <summary>
            This message has the same semantics as `google.rpc.Status`. It uses HTTP
            status code instead of gRPC status code. It has extra fields `status` and
            `errors` for backward compatibility with [Google API Client
            Libraries](https://developers.google.com/api-client-library).
            </summary>
        </member>
        <member name="F:Google.Api.Gax.Grpc.Rest.Error.Types.Status.CodeFieldNumber">
            <summary>Field number for the "code" field.</summary>
        </member>
        <member name="P:Google.Api.Gax.Grpc.Rest.Error.Types.Status.Code">
            <summary>
            The HTTP status code that corresponds to `google.rpc.Status.code`.
            </summary>
        </member>
        <member name="F:Google.Api.Gax.Grpc.Rest.Error.Types.Status.MessageFieldNumber">
            <summary>Field number for the "message" field.</summary>
        </member>
        <member name="P:Google.Api.Gax.Grpc.Rest.Error.Types.Status.Message">
            <summary>
            This corresponds to `google.rpc.Status.message`.
            </summary>
        </member>
        <member name="F:Google.Api.Gax.Grpc.Rest.Error.Types.Status.ErrorsFieldNumber">
            <summary>Field number for the "errors" field.</summary>
        </member>
        <member name="P:Google.Api.Gax.Grpc.Rest.Error.Types.Status.Errors">
            <summary>
            Deprecated. This field is only used by error format v1.
            </summary>
        </member>
        <member name="F:Google.Api.Gax.Grpc.Rest.Error.Types.Status.Status_FieldNumber">
            <summary>Field number for the "status" field.</summary>
        </member>
        <member name="P:Google.Api.Gax.Grpc.Rest.Error.Types.Status.Status_">
            <summary>
            This is the enum version for `google.rpc.Status.code`.
            </summary>
        </member>
        <member name="F:Google.Api.Gax.Grpc.Rest.Error.Types.Status.DetailsFieldNumber">
            <summary>Field number for the "details" field.</summary>
        </member>
        <member name="P:Google.Api.Gax.Grpc.Rest.Error.Types.Status.Details">
            <summary>
            This corresponds to `google.rpc.Status.details`, but
            we represent each element as a Struct instead of an Any so that
            we can definitely parse the outer JSON. We then convert each struct back into
            JSON and attempt to parse it as an Any, ignoring values that have type URLs
            corresponding to messages we're not aware of.
            </summary>
        </member>
        <member name="T:Google.Api.Gax.Grpc.Rest.RestCallInvoker">
            <summary>
            CallInvoker implementation which uses regular HTTP requests with JSON payloads.
            This just delegates back to the <see cref="T:Google.Api.Gax.Grpc.Rest.RestChannel"/> that it wraps.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Grpc.Rest.RestCallInvoker.AsyncClientStreamingCall``2(Grpc.Core.Method{``0,``1},System.String,Grpc.Core.CallOptions)">
            <inheritdoc />
        </member>
        <member name="M:Google.Api.Gax.Grpc.Rest.RestCallInvoker.AsyncDuplexStreamingCall``2(Grpc.Core.Method{``0,``1},System.String,Grpc.Core.CallOptions)">
            <inheritdoc />
        </member>
        <member name="M:Google.Api.Gax.Grpc.Rest.RestCallInvoker.AsyncServerStreamingCall``2(Grpc.Core.Method{``0,``1},System.String,Grpc.Core.CallOptions,``0)">
            <inheritdoc />
        </member>
        <member name="M:Google.Api.Gax.Grpc.Rest.RestCallInvoker.AsyncUnaryCall``2(Grpc.Core.Method{``0,``1},System.String,Grpc.Core.CallOptions,``0)">
            <inheritdoc />
        </member>
        <member name="M:Google.Api.Gax.Grpc.Rest.RestCallInvoker.BlockingUnaryCall``2(Grpc.Core.Method{``0,``1},System.String,Grpc.Core.CallOptions,``0)">
            <inheritdoc />
        </member>
        <member name="T:Google.Api.Gax.Grpc.Rest.RestChannel">
            <summary>
            gRPC "channel" that really uses REST/JSON over HTTP to make RPCs.
            The channel is aware of which APIs it supports, so that it's able to perform the
            appropriate request translation.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Grpc.Rest.RestChannel.AsyncUnaryCall``2(Grpc.Core.Method{``0,``1},System.String,Grpc.Core.CallOptions,``0)">
            <summary>
            Equivalent to <see cref="M:Grpc.Core.CallInvoker.AsyncUnaryCall``2(Grpc.Core.Method{``0,``1},System.String,Grpc.Core.CallOptions,``0)"/>.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Grpc.Rest.RestChannel.AsyncServerStreamingCall``2(Grpc.Core.Method{``1,``0},System.String,Grpc.Core.CallOptions,``1)">
            <summary>
            Equivalent to <see cref="M:Grpc.Core.CallInvoker.AsyncServerStreamingCall``2(Grpc.Core.Method{``0,``1},System.String,Grpc.Core.CallOptions,``0)"/>.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Grpc.Rest.RestChannel.SendAsync``1(Google.Api.Gax.Grpc.Rest.RestMethod,System.String,Grpc.Core.CallOptions,``0,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
            <summary>
            Creates an HTTP request, adds headers from CallOptions, and sends the request.
            </summary>
            <typeparam name="TRequest">The type of request</typeparam>
            <param name="restMethod">The RPC being called; used to convert the request</param>
            <param name="host">Override for the endpoint, if any</param>
            <param name="options">The gRPC call options, used for headers and cancellation</param>
            <param name="request">The RPC request</param>
            <param name="httpCompletionOption">The option indicating at what point the method should complete,
            <param name="cancellationToken">The cancellation token for the RPC.</param>
            within HTTP response processing</param>
        </member>
        <member name="T:Google.Api.Gax.Grpc.Rest.RestGrpcAdapter">
            <summary>
            Implementation of <see cref="T:Google.Api.Gax.Grpc.GrpcAdapter"/> that uses HTTP/1.1 and JSON,
            but via a gRPC <see cref="T:Grpc.Core.CallInvoker"/>.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.Grpc.Rest.RestGrpcAdapter.Default">
            <summary>
            Returns the default gRPC adapter for the REST transport.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Grpc.Rest.RestGrpcAdapter.CreateChannelImpl(Google.Api.Gax.Grpc.ServiceMetadata,System.String,Grpc.Core.ChannelCredentials,Google.Api.Gax.Grpc.GrpcChannelOptions)">
            <inheritdoc />
        </member>
        <member name="M:Google.Api.Gax.Grpc.Rest.RestGrpcAdapter.ConvertHttpStatusCode(System.Int32)">
            <summary>
            Converts an HTTP status code into the corresponding gRPC status code.
            Note that there is not a 1:1 correspondence between status code; multiple
            HTTP status codes can map to the same gRPC status code.
            </summary>
            <param name="httpStatusCode">The HTTP status code to convert</param>
            <returns>The converted gRPC status code.</returns>
        </member>
        <member name="T:Google.Api.Gax.Grpc.Rest.RestMethod">
            <summary>
            Class to convert between proto request/response messages and HTTP request/response messages.
            (Details of request transcoding are mostly in <see cref="T:Google.Api.Gax.Grpc.Rest.HttpRuleTranscoder"/>,
            but they are abstracted by this class.)
            </summary>
        </member>
        <member name="P:Google.Api.Gax.Grpc.Rest.RestMethod.FullName">
            <summary>
            The service-qualified method name, as used by gRPC, e.g. "/google.somepackage.SomeService/SomeMethod"
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Grpc.Rest.RestMethod.GetGrpcFullName(Google.Protobuf.Reflection.MethodDescriptor)">
            <summary>
            Returns the name by which gRPC will refer to the given proto method,
            e.g. "/google.somepackage.SomeService/SomeMethod".
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Grpc.Rest.RestMethod.Create(Google.Api.Gax.Grpc.ApiMetadata,Google.Protobuf.Reflection.MethodDescriptor,Google.Protobuf.JsonParser)">
            <summary>
            Creates a <see cref="T:Google.Api.Gax.Grpc.Rest.RestMethod"/> representation from the given protobuf method representation.
            </summary>
            <param name="apiMetadata">The metadata for the API that this method is part of.</param>
            <param name="method">The protobuf method to represent.</param>
            <param name="parser">The JSON parser to use when parsing requests.</param>
            <returns>A representation of the method that can be used to handle HTTP requests/responses,
            or null if the method is currently not supported in REGAPIC.</returns>
        </member>
        <member name="M:Google.Api.Gax.Grpc.Rest.RestMethod.ReadResponseAsync``1(System.Threading.Tasks.Task{Google.Api.Gax.Grpc.Rest.ReadHttpResponseMessage})">
            <summary>
            Parses the response and converts it into the protobuf response type.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Grpc.Rest.RestMethod.ParseJson``1(System.String)">
            <summary>
            Parses a single JSON object as a <typeparamref name="TResponse"/>.
            </summary>
            <typeparam name="TResponse">The response type to parse; this is expected to match the method output type.</typeparam>
        </member>
        <member name="T:Google.Api.Gax.Grpc.Rest.RestServiceCollection">
            <summary>
            Represents a set of methods all expected to be accessible via the same host.
            (The host itself is not part of the state of this class.)
            TODO: Do we need this class, or could we keep the dictionary directly in RestChannel?
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Grpc.Rest.RestServiceCollection.GetRestMethod(Grpc.Core.IMethod)">
            <summary>
            Returns the <see cref="T:Google.Api.Gax.Grpc.Rest.RestMethod"/> corresponding to <paramref name="method"/>.
            </summary>
            <exception cref="T:Grpc.Core.RpcException">The method is not supported by REGAPIC, or is not in the service at all.</exception>
        </member>
        <member name="T:Google.Api.Gax.Grpc.Rest.RpcCancellationContext">
            <summary>
            Centralized handling of cancellation differentiation between time-outs
            and explicit cancellation, as well as disposal of any CancellationTokenSource objects.
            This also allows the RPC to be cancelled explicitly (separate from any cancellation tokens
            previously created); this is expected to be used via <see cref="M:Grpc.Core.AsyncUnaryCall`1.Dispose"/>
            etc.
            </summary>
            <remarks>
            While it would be nice for this to be fully testable via IClock, the
            CancellationTokenSource constructor that starts a timer isn't really testable anyway,
            so everything is done with the system clock instead.
            </remarks>
        </member>
        <member name="M:Google.Api.Gax.Grpc.Rest.RpcCancellationContext.ForTesting(System.String,System.Threading.CancellationTokenSource,System.Threading.CancellationToken)">
            <summary>
            Creates an instance using the given cancellation token source for deadlines and the given
            call cancellation token. This is only present for test purposes.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Grpc.Rest.RpcCancellationContext.FromOptions(System.String,Grpc.Core.CallOptions)">
            <summary>
            Creates a cancellation context from the given call options.
            </summary>
            <param name="rpcName">The name of the RPC, used to report exceptions.</param>
            <param name="options">The call options used for this RPC call, optionally including
            a deadline and cancellation token.</param>
            <exception cref="T:Grpc.Core.RpcException">The options contain a deadline that has already elapsed</exception>
        </member>
        <member name="M:Google.Api.Gax.Grpc.Rest.RpcCancellationContext.RunAsync(System.Func{System.Threading.CancellationToken,System.Threading.Tasks.Task},System.Threading.CancellationToken)">
            <summary>
            Combines <paramref name="cancellationToken"/> with the RPC's cancellation tokens, and waits for
            the task provided by <paramref name="taskProvider"/> to complete, converting any <see cref="T:System.OperationCanceledException"/>
            into an <see cref="T:Grpc.Core.RpcException"/> with an appropriate status (DeadlineExceeded or Cancelled depending
            on which token was responsible for the original exception).
            </summary>
            <param name="taskProvider">A function which returns a task to await.</param>
            <param name="cancellationToken">An additional cancellation token, defaulting to "no extra cancellation token,
            just use the RPC's cancellation tokens".</param>
            <returns>A task which will complete when the provided task completes.</returns>
        </member>
        <member name="M:Google.Api.Gax.Grpc.Rest.RpcCancellationContext.RunAsync``1(System.Func{System.Threading.CancellationToken,System.Threading.Tasks.Task{``0}},System.Threading.CancellationToken)">
            <summary>
            Combines <paramref name="cancellationToken"/> with the RPC's cancellation tokens, and waits for
            the task provided by <paramref name="taskProvider"/> to complete, converting any <see cref="T:System.OperationCanceledException"/>
            into an <see cref="T:Grpc.Core.RpcException"/> with an appropriate status (DeadlineExceeded or Cancelled depending
            on which token was responsible for the original exception).
            </summary>
            <typeparam name="T">The type of the task provided by <paramref name="taskProvider"/>.</typeparam>
            <param name="taskProvider">A function which returns a task to await.</param>
            <param name="cancellationToken">An additional cancellation token, defaulting to "no extra cancellation token,
            just use the RPC's cancellation tokens".</param>
            <returns>A task which will complete when the provided task completes.</returns>
        </member>
        <member name="M:Google.Api.Gax.Grpc.Rest.RpcCancellationContext.Cancel">
            <summary>
            Cancels the overall RPC. This call is ignored if the context has already been disposed.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Grpc.Rest.RpcCancellationContext.Dispose">
            <summary>
            Disposes of the resources used by this context. After this method returns,
            the context cannot be used: further calls, including <see cref="M:Google.Api.Gax.Grpc.Rest.RpcCancellationContext.Cancel"/>,
            will throw <see cref="T:System.ObjectDisposedException"/>.
            </summary>
        </member>
        <member name="T:Google.Api.Gax.Grpc.Rest.TranscodingOutput">
            <summary>
            The result of transcoding a protobuf request using an HttpRule.
            This is produced by <see cref="T:Google.Api.Gax.Grpc.Rest.HttpRuleTranscoder"/>.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Grpc.Rest.TranscodingOutput.GetRelativeUri">
            <summary>
            Merges the uri path and the query string parameters, escaping them.
            Ignores the possibility that the path can already have parameters or contain an anchor (`#`).
            This method is visible for testing; production code should generally call <see cref="M:Google.Api.Gax.Grpc.Rest.TranscodingOutput.CreateRequest(System.String)"/>
            instead.
            </summary>
            <returns>The URI path merged with the encoded query string parameters</returns>
        </member>
        <member name="T:Google.Api.Gax.Grpc.RetryAttempt">
            <summary>
            An attempt at a retriable operation. Use <see cref="M:Google.Api.Gax.Grpc.RetryAttempt.CreateRetrySequence(Google.Api.Gax.Grpc.RetrySettings,Google.Api.Gax.IScheduler,System.Nullable{System.DateTime},Google.Api.Gax.IClock,System.Nullable{System.TimeSpan})"/>
            or <see cref="M:Google.Api.Gax.Grpc.RetryAttempt.CreateRetrySequence(Google.Api.Gax.Grpc.RetrySettings,Google.Api.Gax.IScheduler,System.Nullable{System.TimeSpan})"/> to create a sequence of attempts that follow the specified settings.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.Grpc.RetryAttempt.AttemptNumber">
            <summary>
            The 1-based number of this attempt. If this is equal to <see cref="P:Google.Api.Gax.Grpc.RetrySettings.MaxAttempts"/> for the settings
            used to create this attempt, <see cref="M:Google.Api.Gax.Grpc.RetryAttempt.ShouldRetry(System.Exception)"/> will always return false.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.Grpc.RetryAttempt.JitteredBackoff">
            <summary>
            The time that will be used to sleep or delay in <see cref="M:Google.Api.Gax.Grpc.RetryAttempt.Backoff(System.Threading.CancellationToken)"/> and <see cref="M:Google.Api.Gax.Grpc.RetryAttempt.BackoffAsync(System.Threading.CancellationToken)"/>.
            This has already had jitter applied to it.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Grpc.RetryAttempt.CreateRetrySequence(Google.Api.Gax.Grpc.RetrySettings,Google.Api.Gax.IScheduler,System.Nullable{System.TimeSpan})">
            <summary>
            Returns a sequence of retry attempts. The sequence has <see cref="P:Google.Api.Gax.Grpc.RetrySettings.MaxAttempts"/> elements, and calling
            <see cref="M:Google.Api.Gax.Grpc.RetryAttempt.ShouldRetry(System.Exception)"/> on the last attempt will always return false. This overload assumes no deadline,
            and so does not require a clock.
            </summary>
            <param name="settings">The retry settings to create a sequence for. Must not be null.</param>
            <param name="scheduler">The scheduler to use for delays.</param>
            <param name="initialBackoffOverride">An override value to allow an initial backoff which is not the same
            as <see cref="P:Google.Api.Gax.Grpc.RetrySettings.InitialBackoff"/>. This is typically to allow an "immediate first retry".</param>
            <returns></returns>
        </member>
        <member name="M:Google.Api.Gax.Grpc.RetryAttempt.CreateRetrySequence(Google.Api.Gax.Grpc.RetrySettings,Google.Api.Gax.IScheduler,System.Nullable{System.DateTime},Google.Api.Gax.IClock,System.Nullable{System.TimeSpan})">
            <summary>
            Returns a sequence of retry attempts. The sequence has <see cref="P:Google.Api.Gax.Grpc.RetrySettings.MaxAttempts"/> elements, and calling
            <see cref="M:Google.Api.Gax.Grpc.RetryAttempt.ShouldRetry(System.Exception)"/> on the last attempt will always return false.
            </summary>
            <param name="settings">The retry settings to create a sequence for. Must not be null.</param>
            <param name="scheduler">The scheduler to use for delays.</param>
            <param name="deadline">The overall deadline for the operation.</param>
            <param name="clock">The clock to use to compare the current time with the deadline.</param>
            <param name="initialBackoffOverride">An override value to allow an initial backoff which is not the same
            as <see cref="P:Google.Api.Gax.Grpc.RetrySettings.InitialBackoff"/>. This is typically to allow an "immediate first retry".</param>
            <returns></returns>
        </member>
        <member name="M:Google.Api.Gax.Grpc.RetryAttempt.ShouldRetry(System.Exception)">
            <summary>
            Indicates whether the operation should be retried when the given exception has been thrown.
            This will return false if the exception indicates that the operation shouldn't be retried,
            or the maximum number of attempts has been reached, or the next backoff would exceed the overall
            deadline. (It is assumed that <see cref="M:Google.Api.Gax.Grpc.RetryAttempt.Backoff(System.Threading.CancellationToken)"/> or <see cref="M:Google.Api.Gax.Grpc.RetryAttempt.BackoffAsync(System.Threading.CancellationToken)"/>
            will be called immediately afterwards.)
            </summary>
            <param name="exception">The exception thrown by the retriable operation.</param>
            <returns><c>true</c> if the operation should be retried; <c>false</c> otherwise.</returns>
        </member>
        <member name="M:Google.Api.Gax.Grpc.RetryAttempt.Backoff(System.Threading.CancellationToken)">
            <summary>
            Synchronously sleeps for a period of <see cref="P:Google.Api.Gax.Grpc.RetryAttempt.JitteredBackoff"/>.
            </summary>
            <param name="cancellationToken">The cancellation token to apply to the sleep operation.</param>
        </member>
        <member name="M:Google.Api.Gax.Grpc.RetryAttempt.BackoffAsync(System.Threading.CancellationToken)">
            <summary>
            Asynchronously delays for a period of <see cref="P:Google.Api.Gax.Grpc.RetryAttempt.JitteredBackoff"/>.
            </summary>
            <param name="cancellationToken">The cancellation token to apply to the delay operation.</param>
        </member>
        <member name="T:Google.Api.Gax.Grpc.RetrySettings">
            <summary>
            Settings for retrying RPCs.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.Grpc.RetrySettings.MaxAttempts">
            <summary>
            The maximum number of attempts to make. Always greater than or equal to 1.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.Grpc.RetrySettings.InitialBackoff">
            <summary>
            The backoff time between the first attempt and the first retry. Always non-negative.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.Grpc.RetrySettings.MaxBackoff">
            <summary>
            The maximum backoff time between retries. Always non-negative.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.Grpc.RetrySettings.BackoffMultiplier">
            <summary>
            The multiplier to apply to the backoff on each iteration; always greater than or equal to 1.0.
            </summary>
            <remarks>
            <para>
            As an example, a multiplier of 2.0 with an initial backoff of 0.1s on an RPC would then apply
            a backoff of 0.2s, then 0.4s until it is capped by <see cref="P:Google.Api.Gax.Grpc.RetrySettings.MaxBackoff"/>.
            </para>
            </remarks>
        </member>
        <member name="P:Google.Api.Gax.Grpc.RetrySettings.RetryFilter">
            <summary>
            A predicate to determine whether or not a particular exception should cause the operation to be retried.
            Usually this is simply a matter of checking the status codes. This is never null.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.Grpc.RetrySettings.BackoffJitter">
            <summary>
            The delay jitter to apply for delays, defaulting to <see cref="P:Google.Api.Gax.Grpc.RetrySettings.RandomJitter"/>. This is never null.
            </summary>
            <remarks>
            "Jitter" is used to introduce randomness into the pattern of delays. This is to avoid multiple
            clients performing the same delay pattern starting at the same point in time,
            leading to higher-than-necessary contention. The default jitter simply takes each maximum delay
            and returns an actual delay which is a uniformly random value between 0 and the maximum. This
            is good enough for most applications, but makes precise testing difficult.
            </remarks>
        </member>
        <member name="M:Google.Api.Gax.Grpc.RetrySettings.#ctor(System.Int32,System.TimeSpan,System.TimeSpan,System.Double,System.Predicate{System.Exception},Google.Api.Gax.Grpc.RetrySettings.IJitter)">
            <summary>
            Creates a new instance with the given settings.
            </summary>
            <param name="maxAttempts">The maximum number of attempts to make. Must be positive.</param>
            <param name="initialBackoff">The backoff after the initial failure. Must be non-negative.</param>
            <param name="maxBackoff">The maximum backoff. Must be at least <paramref name="initialBackoff"/>.</param>
            <param name="backoffMultiplier">The multiplier to apply to backoff times. Must be at least 1.0.</param>
            <param name="retryFilter">The predicate to use to check whether an error should be retried. Must not be null.</param>
            <param name="backoffJitter">The jitter to use on each backoff. Must not be null.</param>
        </member>
        <member name="M:Google.Api.Gax.Grpc.RetrySettings.FromConstantBackoff(System.Int32,System.TimeSpan,System.Predicate{System.Exception},Google.Api.Gax.Grpc.RetrySettings.IJitter)">
            <summary>
            Returns a <see cref="T:Google.Api.Gax.Grpc.RetrySettings"/> using the specified maximum number of attempts and a constant backoff.
            Jitter is still applied to each backoff, but the "base" value of the backoff is always <paramref name="backoff"/>.
            </summary>
            <param name="maxAttempts">The maximum number of attempts to make. Must be positive.</param>
            <param name="backoff">The backoff after each failure. Must be non-negative.</param>
            <param name="retryFilter">The predicate to use to check whether an error should be retried. Must not be null.</param>
            <param name="backoffJitter">The jitter to use on each backoff. May be null, in which case <see cref="P:Google.Api.Gax.Grpc.RetrySettings.RandomJitter"/> is used.</param>
            <returns>A retry with constant backoff.</returns>
        </member>
        <member name="M:Google.Api.Gax.Grpc.RetrySettings.FromExponentialBackoff(System.Int32,System.TimeSpan,System.TimeSpan,System.Double,System.Predicate{System.Exception},Google.Api.Gax.Grpc.RetrySettings.IJitter)">
            <summary>
            Returns a <see cref="T:Google.Api.Gax.Grpc.RetrySettings"/> using the specified maximum number of attempts and an exponential backoff.
            </summary>
            <param name="maxAttempts">The maximum number of attempts to make. Must be positive.</param>
            <param name="initialBackoff">The backoff after the initial failure. Must be non-negative.</param>
            <param name="maxBackoff">The maximum backoff. Must be at least <paramref name="initialBackoff"/>.</param>
            <param name="backoffMultiplier">The multiplier to apply to backoff times. Must be at least 1.0.</param>
            <param name="retryFilter">The predicate to use to check whether an error should be retried. Must not be null.</param>
            <param name="backoffJitter">The jitter to use on each backoff. May be null, in which case <see cref="P:Google.Api.Gax.Grpc.RetrySettings.RandomJitter"/> is used.</param>
            <returns>A retry with exponential backoff.</returns>
        </member>
        <member name="T:Google.Api.Gax.Grpc.RetrySettings.IJitter">
            <summary>
            Provides a mechanism for applying jitter to delays between retries.
            See the <see cref="P:Google.Api.Gax.Grpc.RetrySettings.BackoffJitter"/> property for more information.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Grpc.RetrySettings.IJitter.GetDelay(System.TimeSpan)">
            <summary>
            Returns the actual delay to use given a maximum available delay.
            </summary>
            <param name="maxDelay">The maximum delay provided by the backoff settings</param>
            <returns>The delay to use before retrying.</returns>
        </member>
        <member name="P:Google.Api.Gax.Grpc.RetrySettings.RandomJitter">
            <summary>
            The default jitter which returns a uniformly distributed random delay between 0 and
            the specified maximum.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.Grpc.RetrySettings.NoJitter">
            <summary>
            A jitter which simply returns the specified maximum delay.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Grpc.RetrySettings.FilterForStatusCodes(Grpc.Core.StatusCode[])">
            <summary>
            Creates a retry filter based on status codes.
            </summary>
            <param name="statusCodes">The status codes to retry. Must not be null.</param>
            <returns>A retry filter based on status codes.</returns>
        </member>
        <member name="M:Google.Api.Gax.Grpc.RetrySettings.FilterForStatusCodes(System.Collections.Generic.IEnumerable{Grpc.Core.StatusCode})">
            <summary>
            Creates a retry filter based on status codes.
            </summary>
            <param name="statusCodes">The status codes to retry. Must not be null.</param>
            <returns>A retry filter based on status codes.</returns>
        </member>
        <member name="M:Google.Api.Gax.Grpc.RetrySettings.NextBackoff(System.TimeSpan)">
            <summary>
            Works out the next backoff from the current one, based on the multiplier and maximum.
            </summary>
            <param name="currentBackoff">The current backoff to use as a basis for the next one.</param>
            <returns>The next backoff to use, which is always at least <see cref="P:Google.Api.Gax.Grpc.RetrySettings.InitialBackoff"/> and at most <see cref="P:Google.Api.Gax.Grpc.RetrySettings.MaxBackoff"/>.</returns>
        </member>
        <member name="T:Google.Api.Gax.Grpc.RoutingHeaderExtractor`1">
            <summary>
            Collects the explicit routing header extraction instructions and
            extracts the routing header value from a specific request
            using these instructions.
            This class is immutable.
            </summary>
        </member>
        <member name="F:Google.Api.Gax.Grpc.RoutingHeaderExtractor`1._patternExtractors">
            <summary>
            The individual pattern extractors present in this extractor. These
            are only present for chaining purposes while building up the full
            extractor. (If we ever move to a builder pattern, we could remove these.)
            These are retained in declaration order.
            </summary>
        </member>
        <member name="F:Google.Api.Gax.Grpc.RoutingHeaderExtractor`1._parameterExtractors">
            <summary>
            The parameter extractors, created from <see cref="F:Google.Api.Gax.Grpc.RoutingHeaderExtractor`1._patternExtractors"/>.
            These are used at execution time to extract the values for parameters.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Grpc.RoutingHeaderExtractor`1.#ctor">
            <summary>
            Create a new RoutingHeaderExtractor with no patterns. (This cannot be used
            to extract headers; new instances must be created with <see cref="M:Google.Api.Gax.Grpc.RoutingHeaderExtractor`1.WithExtractedParameter(System.String,System.String,System.Func{`0,System.String})"/>
            which provides patterns to use to extract values.)
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Grpc.RoutingHeaderExtractor`1.FormatRoutingHeaderValue(System.Object)">
            <summary>
            Returns a string representation of the given value, suitable for including in a routing header.
            (This method does not perform URI encoding; it is expected that the result of this method will either be
            used in <see cref="M:Google.Api.Gax.Grpc.RoutingHeaderExtractor`1.WithExtractedParameter(System.String,System.String,System.Func{`0,System.String})"/>, or as an argument to
            <see cref="M:Google.Api.Gax.Grpc.ApiCall`2.WithGoogleRequestParam(System.String,System.Func{`0,System.String})" />.)
            </summary>
            <param name="value">The value to format. May be null.</param>
            <returns>The formatted value, or null if <paramref name="value"/> is null.</returns>
        </member>
        <member name="M:Google.Api.Gax.Grpc.RoutingHeaderExtractor`1.WithExtractedParameter(System.String,System.String,System.Func{`0,System.String})">
            <summary>
            Returns a new instance with the same parameter extractors as this one, with an additional one specified as arguments.
            The extractions follow the "last successfully matched wins" rule for
            conflict resolution when there are multiple extractions for the same parameter name.
            (See `google/api/routing.proto` for further details.) If multiple parameters
            with different names are present, the extracted header will contain them in the order
            in which they have been added with this method, based on the first occurrence of
            each parameter name.
            </summary>
            <param name="paramName">The name of the parameter in the routing header.</param>
            <param name="extractionRegex">The regular expression (in string form) used to extract the value of the parameter.
            Must have exactly one capturing group (in addition to the implicit "group 0" which captures the whole match).</param>
            <param name="selector">A function to call on each request, to determine the string to extract the header value from.
            The parameter must not be null, but may return null.</param>
            <returns></returns>
        </member>
        <member name="M:Google.Api.Gax.Grpc.RoutingHeaderExtractor`1.ExtractHeader(`0)">
            <summary>
            Extracts the routing header value to apply based on a request.
            </summary>
            <param name="request">A request to extract the routing header parameters and values from</param>
            <returns>The value to use for the routing header. This may contain multiple &amp;-separated parameters.</returns>
        </member>
        <member name="T:Google.Api.Gax.Grpc.RoutingHeaderExtractor`1.SingleParameterExtractor">
            <summary>
            An extractor for a single parameter, which may check multiple patterns to extract a value.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Grpc.RoutingHeaderExtractor`1.SingleParameterExtractor.#ctor(System.Collections.Generic.IEnumerable{Google.Api.Gax.Grpc.RoutingHeaderExtractor{`0}.SinglePatternExtractor})">
            <summary>
            Creates an instance based on the single-pattern extractors, in the order in which they are
            declared. They will be *applied* in reverse order. It is assumed that all pattern extractors
            are for the same parameter name.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Grpc.RoutingHeaderExtractor`1.SingleParameterExtractor.ExtractKeyValuePair(`0)">
            <summary>
            Extracts the value from the request and returns it in a form ready to be included in the
            header
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="T:Google.Api.Gax.Grpc.RoutingHeaderExtractor`1.SinglePatternExtractor">
            <summary>
            An extractor for a single pattern, used one option within a <see cref="T:Google.Api.Gax.Grpc.RoutingHeaderExtractor`1.SingleParameterExtractor"/>.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Grpc.RoutingHeaderExtractor`1.SinglePatternExtractor.ExtractKeyValuePair(`0)">
            <summary>
            Extracts the value from the request by matching it against the pattern,
            returning the the key/value pair in the form key=value, after URI-escaping the value.
            </summary>
        </member>
        <member name="T:Google.Api.Gax.Grpc.RpcExceptionExtensions">
            <summary>
            Utility extension methods to make it easier to retrieve extended error information from an <see cref="T:Grpc.Core.RpcException"/>.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Grpc.RpcExceptionExtensions.GetRpcStatus(Grpc.Core.RpcException)">
            <summary>
            Retrieves the <see cref="T:Google.Rpc.Status"/> message containing extended error information
            from the trailers in an <see cref="T:Grpc.Core.RpcException"/>, if present.
            </summary>
            <param name="ex">The RPC exception to retrieve details from. Must not be null.</param>
            <returns>The <see cref="T:Google.Rpc.Status"/> message specified in the exception, or null
            if there is no such information.</returns>
        </member>
        <member name="M:Google.Api.Gax.Grpc.RpcExceptionExtensions.GetBadRequest(Grpc.Core.RpcException)">
            <summary>
            Retrieves the <see cref="T:Google.Rpc.BadRequest"/> message containing extended error information
            from the trailers in an <see cref="T:Grpc.Core.RpcException"/>, if present.
            </summary>
            <param name="ex">The RPC exception to retrieve details from. Must not be null.</param>
            <returns>The <see cref="T:Google.Rpc.BadRequest"/> message specified in the exception, or null
            if there is no such information.</returns>
        </member>
        <member name="M:Google.Api.Gax.Grpc.RpcExceptionExtensions.GetErrorInfo(Grpc.Core.RpcException)">
            <summary>
            Retrieves the <see cref="T:Google.Rpc.ErrorInfo"/> message containing extended error information
            from the trailers in an <see cref="T:Grpc.Core.RpcException"/>, if present.
            </summary>
            <param name="ex">The RPC exception to retrieve details from. Must not be null.</param>
            <returns>The <see cref="T:Google.Rpc.ErrorInfo"/> message specified in the exception, or null
            if there is no such information.</returns>
        </member>
        <member name="M:Google.Api.Gax.Grpc.RpcExceptionExtensions.GetStatusDetail``1(Grpc.Core.RpcException)">
            <summary>
            Retrieves the error details of type <typeparamref name="T"/> from the <see cref="T:Google.Rpc.Status"/>
            message associated with an <see cref="T:Grpc.Core.RpcException"/>, if any.
            </summary>
            <typeparam name="T">The message type to decode from within the error details.</typeparam>
            <param name="ex">The RPC exception to retrieve details from. Must not be null.</param>
            <returns></returns>
        </member>
        <member name="T:Google.Api.Gax.Grpc.ServerStreamingBase`1">
            <summary>
            Base class for server streaming RPC methods. This wraps an underlying call returned by gRPC,
            in order to provide a wrapper for the async response stream, allowing users to take advantage
            of <code>await foreach</code> support from C# 8 onwards.
            </summary>
            <remarks>
            To avoid memory leaks, users must dispose of gRPC streams.
            Additionally, you are strongly advised to read the whole response stream, even if the data
            is not required - this avoids effectively cancelling the call.
            </remarks>
            <typeparam name="TResponse">RPC streaming response type</typeparam>
        </member>
        <member name="P:Google.Api.Gax.Grpc.ServerStreamingBase`1.GrpcCall">
            <summary>
            The underlying gRPC duplex streaming call.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Grpc.ServerStreamingBase`1.GetResponseStream">
            <summary>
            Async stream to read streaming responses, exposed as an async sequence.
            The default implementation will use <see cref="P:Google.Api.Gax.Grpc.ServerStreamingBase`1.GrpcCall"/> to extract a response
            stream, and adapt it to <see cref="T:Google.Api.Gax.Grpc.AsyncResponseStream`1"/>.
            </summary>
            <remarks>
            If this method is called more than once, all the returned enumerators will be enumerating over the
            same underlying response stream, which may cause confusion. Additionally, the sequence returned by
            this method can only be iterated over a single time. Attempting to iterate more than once will cause
            an <see cref="T:System.InvalidOperationException"/>.
            </remarks>
        </member>
        <member name="M:Google.Api.Gax.Grpc.ServerStreamingBase`1.Dispose">
            <summary>
            Disposes of the underlying gRPC call. There is no need to dispose of both the wrapper
            and the underlying call; it's typically simpler to dispose of the wrapper with a
            <code>using</code> statement as the wrapper is returned by client libraries.
            </summary>
            <remarks>The default implementation just calls Dispose on the result of <see cref="P:Google.Api.Gax.Grpc.ServerStreamingBase`1.GrpcCall"/>.</remarks>
        </member>
        <member name="T:Google.Api.Gax.Grpc.ServiceMetadata">
            <summary>
            Provides metadata about a single service within an API.
            Often most of these aspects will be the same across multiple services,
            but they can be specified with different values in the original proto, so
            they are specified individually here. This class is expected to be constructed
            with a single instance per service; equality is by simple identity.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.Grpc.ServiceMetadata.ServiceDescriptor">
            <summary>
            The protobuf service descriptor for this service. This is never null.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.Grpc.ServiceMetadata.Name">
            <summary>
            The name of the service within the API, e.g. "Subscriber". This is never null or empty.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.Grpc.ServiceMetadata.DefaultEndpoint">
            <summary>
            The default endpoint for the service. This may be null, if a service has no default endpoint.
            </summary>
            <remarks>
            The default endpoint is an endpoint in the default universe domain.
            </remarks>
        </member>
        <member name="P:Google.Api.Gax.Grpc.ServiceMetadata.EndpointTemplate">
            <summary>
            The template to build and endpoint for the service taking into account a custom universe domain,
            for instance "storage.{0}".
            May be null, in which case no universe domain dependent endpoint may be built for the service.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.Grpc.ServiceMetadata.DefaultScopes">
            <summary>
            The default scopes for the service. This will never be null, but may be empty.
            This will never contain any null references.
            This will never change after construction.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.Grpc.ServiceMetadata.SupportsScopedJwts">
            <summary>
            Whether this service supports scoped JWT access (in which case
            this is preferred by default over OAuth tokens).
            </summary>
        </member>
        <member name="P:Google.Api.Gax.Grpc.ServiceMetadata.ApiMetadata">
            <summary>
            The metadata for the API this is part of. This is never null.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.Grpc.ServiceMetadata.Transports">
            <summary>
            The transports supported by this service.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Grpc.ServiceMetadata.#ctor(Google.Protobuf.Reflection.ServiceDescriptor,System.String,System.Collections.Generic.IEnumerable{System.String},System.Boolean,Google.Api.Gax.ApiTransports,Google.Api.Gax.Grpc.ApiMetadata)">
            <summary>
            Constructs a new instance for a given service.
            </summary>
            <param name="serviceDescriptor">The protobuf descriptor for the service.</param>
            <param name="defaultEndpoint">The default endpoint to connect to.</param>
            <param name="defaultScopes">The default scopes for the service. Must not be null, and must not contain any null elements. May be empty.</param>
            <param name="supportsScopedJwts">Whether the service supports scoped JWTs as credentials.</param>
            <param name="transports">The transports supported by this service.</param>
            <param name="apiMetadata">The metadata for this API, including all of the services expected to be available at the same endpoint, and all associated protos.</param>
        </member>
        <member name="T:Google.Api.Gax.Grpc.ServiceSettingsBase">
            <summary>
            Common settings for all services.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Grpc.ServiceSettingsBase.#ctor">
            <summary>
            Constructs a new service settings base object with a default version header, unset call settings and
            unset clock.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Grpc.ServiceSettingsBase.#ctor(Google.Api.Gax.Grpc.ServiceSettingsBase)">
            <summary>
            Constructs a new service settings base object by cloning the settings from an existing one.
            </summary>
            <param name="existing">The existing settings object to clone settings from. Must not be null.</param>
        </member>
        <member name="P:Google.Api.Gax.Grpc.ServiceSettingsBase.VersionHeaderBuilder">
            <summary>
            A builder for x-goog-api-client version headers. Additional library versions can be appended via this property.
            End-users should almost never need to use this property; it is primarily for use in Google libraries which provide
            a higher level abstraction over the generated client libraries.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.Grpc.ServiceSettingsBase.CallSettings">
            <summary>
            If not null, <see cref="P:Google.Api.Gax.Grpc.ServiceSettingsBase.CallSettings"/> that are applied to every RPC performed by the client.
            If null or unset, RPC default settings will be used for all settings.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.Grpc.ServiceSettingsBase.Clock">
            <summary>
            If not null, the clock used to calculate RPC deadlines. If null or unset, the <see cref="T:Google.Api.Gax.SystemClock"/> is used.
            </summary>
            <remarks>
            This is primarily only to be set for testing.
            In production code generally leave this unset to use the <see cref="T:Google.Api.Gax.SystemClock"/>.
            </remarks>
        </member>
        <member name="P:Google.Api.Gax.Grpc.ServiceSettingsBase.Scheduler">
            <summary>
            If not null, the scheduler used for delays between operations (e.g. for retry).
            If null or unset, the <see cref="T:Google.Api.Gax.SystemScheduler"/> is used.
            </summary>
            <remarks>
            This is primarily only to be set for testing.
            In production code generally leave this unset to use the <see cref="T:Google.Api.Gax.SystemScheduler"/>.
            </remarks>
        </member>
        <member name="P:Google.Api.Gax.Grpc.ServiceSettingsBase.Interceptor">
            <summary>
            An optional gRPC interceptor to perform arbitrary interception tasks (such as logging) on gRPC calls.
            Note that this property is not used by code generated before August 2nd 2018: only packages created
            on or after that date are aware of this property.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Grpc.TaskExtensions.WithCancellationToken``1(System.Threading.Tasks.Task{``0},System.Threading.CancellationToken)">
            <summary>
            Returns a task which can be cancelled by the given cancellation token, but otherwise observes the original
            task's state. This does *not* cancel any work that the original task was doing, and should be used carefully.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Grpc.TaskExtensions.WithCancellationToken(System.Threading.Tasks.Task,System.Threading.CancellationToken)">
            <summary>
            Returns a task which can be cancelled by the given cancellation token, but otherwise observes the original
            task's state. This does *not* cancel any work that the original task was doing, and should be used carefully.
            </summary>
        </member>
        <member name="T:Microsoft.Extensions.DependencyInjection.GaxGrpcServiceCollectionExtensions">
            <summary>
            Extension methods for dependency injection.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.GaxGrpcServiceCollectionExtensions.AddGrpcNetClientAdapter(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.Action{System.IServiceProvider,Grpc.Net.Client.GrpcChannelOptions})">
            <summary>
            Adds a singleton <see cref="T:Google.Api.Gax.Grpc.GrpcNetClientAdapter"/> to the given service collection
            as the preferred <see cref="T:Google.Api.Gax.Grpc.GrpcAdapter"/> implementation,
            using the default instance with any additional options configured via <paramref name="optionsConfigurer"/>.
            Before executing the specified action, the <see cref="P:Grpc.Net.Client.GrpcChannelOptions.ServiceProvider"/>
            is set to the provider. This enables logging, for example.
            </summary>
            <param name="services">The service collection to add the adapter to.</param>
            <param name="optionsConfigurer">The configuration action to perform on each <see cref="T:Grpc.Net.Client.GrpcChannelOptions"/>
            when it is used by the adapter to construct a channel. May be null, in which case this method only sets the
            service provider.</param>
            <returns>The same service collection reference, for method chaining.</returns>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.GaxGrpcServiceCollectionExtensions.AddGrpcNetClientAdapter(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            Adds a singleton <see cref="T:Google.Api.Gax.Grpc.GrpcNetClientAdapter"/> to the given service collection
            as the preferred <see cref="T:Google.Api.Gax.Grpc.GrpcAdapter"/> implementation,
            such that any <see cref="T:Grpc.Net.Client.GrpcChannel"/> created uses the service provider from
            created this service collection. This enables logging, for example.
            </summary>
            <param name="services">The service collection to add the adapter to.</param>
            <returns>The same service collection reference, for method chaining.</returns>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.GaxGrpcServiceCollectionExtensions.AddGrpcCoreAdapter(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            Adds a singleton <see cref="T:Google.Api.Gax.Grpc.GrpcCoreAdapter"/> to the given service collection
            as the preferred <see cref="T:Google.Api.Gax.Grpc.GrpcAdapter"/> implementation.
            </summary>
            <param name="services">The service collection to add the adapter to.</param>
            <returns>The same service collection reference, for method chaining.</returns>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.GaxGrpcServiceCollectionExtensions.AddRestGrpcAdapter(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            Adds a singleton <see cref="T:Google.Api.Gax.Grpc.Rest.RestGrpcAdapter"/> to the given service collection
            as the preferred <see cref="T:Google.Api.Gax.Grpc.GrpcAdapter"/> implementation.
            </summary>
            <param name="services">The service collection to add the adapter to.</param>
            <returns>The same service collection reference, for method chaining.</returns>
        </member>
        <member name="M:GrpcWindowsDetection.RtlGetVersion(GrpcWindowsDetection.OSVERSIONINFOEX@)">
            <summary>
            Types for calling RtlGetVersion. See https://www.pinvoke.net/default.aspx/ntdll/RtlGetVersion.html
            </summary>
        </member>
        <member name="F:GrpcWindowsDetection.NTSTATUS.STATUS_SUCCESS">
            <summary>
            The operation completed successfully. 
            </summary>
        </member>
    </members>
</doc>
