<?xml version="1.0"?>
<doc>
    <assembly>
        <name>CloudNative.CloudEvents.AspNetCore</name>
    </assembly>
    <members>
        <member name="T:CloudNative.CloudEvents.AspNetCore.HttpRequestExtensions">
            <summary>
            Extension methods to convert between HTTP requests and CloudEvents.
            </summary>
        </member>
        <member name="M:CloudNative.CloudEvents.AspNetCore.HttpRequestExtensions.IsCloudEvent(Microsoft.AspNetCore.Http.HttpRequest)">
            <summary>
            Indicates whether this <see cref="T:Microsoft.AspNetCore.Http.HttpRequest"/> holds a single CloudEvent.
            </summary>
            <remarks>
            This method returns false for batch requests, as they need to be parsed differently.
            </remarks>
            <param name="httpRequest">The request to check for the presence of a CloudEvent. Must not be null.</param>
            <returns>true, if the request is a CloudEvent</returns>
        </member>
        <member name="M:CloudNative.CloudEvents.AspNetCore.HttpRequestExtensions.IsCloudEventBatch(Microsoft.AspNetCore.Http.HttpRequest)">
            <summary>
            Indicates whether this <see cref="T:Microsoft.AspNetCore.Http.HttpRequest"/> holds a batch of CloudEvents.
            </summary>
            <param name="httpRequest">The request to check for the presence of a CloudEvent batch. Must not be null.</param>
            <returns>true, if the request is a CloudEvent batch</returns>
        </member>
        <member name="M:CloudNative.CloudEvents.AspNetCore.HttpRequestExtensions.ToCloudEventAsync(Microsoft.AspNetCore.Http.HttpRequest,CloudNative.CloudEvents.CloudEventFormatter,CloudNative.CloudEvents.CloudEventAttribute[])">
            <summary>
            Converts this HTTP request into a CloudEvent object.
            </summary>
            <param name="httpRequest">The HTTP request to decode. Must not be null.</param>
            <param name="formatter">The event formatter to use to process the request body. Must not be null.</param>
            <param name="extensionAttributes">The extension attributes to use when populating the CloudEvent. May be null.</param>
            <returns>The decoded CloudEvent.</returns>
            <exception cref="T:System.ArgumentException">The request does not contain a CloudEvent.</exception>
        </member>
        <member name="M:CloudNative.CloudEvents.AspNetCore.HttpRequestExtensions.ToCloudEventAsync(Microsoft.AspNetCore.Http.HttpRequest,CloudNative.CloudEvents.CloudEventFormatter,System.Collections.Generic.IEnumerable{CloudNative.CloudEvents.CloudEventAttribute})">
            <summary>
            Converts this HTTP request into a CloudEvent object.
            </summary>
            <param name="httpRequest">The HTTP request to decode. Must not be null.</param>
            <param name="formatter">The event formatter to use to process the request body. Must not be null.</param>
            <param name="extensionAttributes">The extension attributes to use when populating the CloudEvent. May be null.</param>
            <returns>The decoded CloudEvent.</returns>
            <exception cref="T:System.ArgumentException">The request does not contain a CloudEvent.</exception>
        </member>
        <member name="M:CloudNative.CloudEvents.AspNetCore.HttpRequestExtensions.ToCloudEventBatchAsync(Microsoft.AspNetCore.Http.HttpRequest,CloudNative.CloudEvents.CloudEventFormatter,CloudNative.CloudEvents.CloudEventAttribute[])">
            <summary>
            Converts this HTTP request into a batch of CloudEvents.
            </summary>
            <param name="httpRequest">The HTTP request to decode. Must not be null.</param>
            <param name="formatter">The event formatter to use to process the request body. Must not be null.</param>
            <param name="extensionAttributes">The extension attributes to use when populating the CloudEvents. May be null.</param>
            <returns>The decoded batch of CloudEvents.</returns>
            <exception cref="T:System.ArgumentException">The request does not contain a CloudEvent batch.</exception>
        </member>
        <member name="M:CloudNative.CloudEvents.AspNetCore.HttpRequestExtensions.ToCloudEventBatchAsync(Microsoft.AspNetCore.Http.HttpRequest,CloudNative.CloudEvents.CloudEventFormatter,System.Collections.Generic.IEnumerable{CloudNative.CloudEvents.CloudEventAttribute})">
            <summary>
            Converts this HTTP request into a batch of CloudEvents.
            </summary>
            <param name="httpRequest">The HTTP request to decode. Must not be null.</param>
            <param name="formatter">The event formatter to use to process the request body. Must not be null.</param>
            <param name="extensionAttributes">The extension attributes to use when populating the CloudEvents. May be null.</param>
            <returns>The decoded batch of CloudEvents.</returns>
            <exception cref="T:System.ArgumentException">The request does not contain a CloudEvent batch.</exception>
        </member>
        <member name="T:CloudNative.CloudEvents.AspNetCore.HttpResponseExtensions">
            <summary>
            Extension methods to convert between HTTP responses and CloudEvents.
            </summary>
        </member>
        <member name="M:CloudNative.CloudEvents.AspNetCore.HttpResponseExtensions.CopyToHttpResponseAsync(CloudNative.CloudEvents.CloudEvent,Microsoft.AspNetCore.Http.HttpResponse,CloudNative.CloudEvents.ContentMode,CloudNative.CloudEvents.CloudEventFormatter)">
            <summary>
            Copies a <see cref="T:CloudNative.CloudEvents.CloudEvent"/> into an <see cref="T:Microsoft.AspNetCore.Http.HttpResponse" />.
            </summary>
            <param name="cloudEvent">The CloudEvent to copy. Must not be null, and must be a valid CloudEvent.</param>
            <param name="destination">The response to copy the CloudEvent to. Must not be null.</param>
            <param name="contentMode">Content mode (structured or binary)</param>
            <param name="formatter">The formatter to use within the conversion. Must not be null.</param>
            <returns>A task representing the asynchronous operation.</returns>
        </member>
        <member name="M:CloudNative.CloudEvents.AspNetCore.HttpResponseExtensions.CopyToHttpResponseAsync(System.Collections.Generic.IReadOnlyList{CloudNative.CloudEvents.CloudEvent},Microsoft.AspNetCore.Http.HttpResponse,CloudNative.CloudEvents.CloudEventFormatter)">
            <summary>
            Copies a <see cref="T:CloudNative.CloudEvents.CloudEvent"/> batch into an <see cref="T:Microsoft.AspNetCore.Http.HttpResponse" />.
            </summary>
            <param name="cloudEvents">The CloudEvent batch to copy. Must not be null, and must be a valid CloudEvent.</param>
            <param name="destination">The response to copy the CloudEvent to. Must not be null.</param>
            <param name="formatter">The formatter to use within the conversion. Must not be null.</param>
            <returns>A task representing the asynchronous operation.</returns>
        </member>
    </members>
</doc>
