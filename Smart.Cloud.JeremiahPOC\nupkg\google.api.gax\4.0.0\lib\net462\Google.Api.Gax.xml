<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Google.Api.Gax</name>
    </assembly>
    <members>
        <member name="T:Google.Api.Gax.ApiTransports">
            <summary>
            The transports that can be used to make calls to an API.
            </summary>
        </member>
        <member name="F:Google.Api.Gax.ApiTransports.None">
            <summary>
            No transports specified.
            </summary>
        </member>
        <member name="F:Google.Api.Gax.ApiTransports.Grpc">
            <summary>
            Native gRPC support via binary protobuf serialization.
            </summary>
        </member>
        <member name="F:Google.Api.Gax.ApiTransports.Rest">
            <summary>
            "REST"-like support via JSON.
            </summary>
        </member>
        <member name="T:Google.Api.Gax.BatchingSettings">
            <summary>
            Batching settings used to specify the conditions under which a batch of data
            will be further processed.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.BatchingSettings.#ctor(System.Nullable{System.Int64},System.Nullable{System.Int64},System.Nullable{System.TimeSpan})">
            <summary>
            Creates a new instance with the specified settings.
            </summary>
            <param name="elementCountThreshold">The element count above which further processing of a batch will occur.</param>
            <param name="byteCountThreshold">The byte count above which further processing of a batch will occur.</param>
            <param name="delayThreshold">The batch lifetime above which further processing of a batch will occur.</param>
        </member>
        <member name="P:Google.Api.Gax.BatchingSettings.ElementCountThreshold">
            <summary>
            The element count above which further processing of a batch will occur.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.BatchingSettings.ByteCountThreshold">
            <summary>
            The byte count above which further processing of a batch will occur.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.BatchingSettings.DelayThreshold">
            <summary>
            The batch lifetime above which further processing of a batch will occur.
            </summary>
        </member>
        <member name="T:Google.Api.Gax.CloudRunPlatformDetails">
            <summary>
            Google Cloud Run details.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.CloudRunPlatformDetails.TryLoad(System.String)">
            <summary>
            Builds a <see cref="T:Google.Api.Gax.CloudRunPlatformDetails"/> from the given metadata
            and Cloud Run environment variables.
            The metadata is normally retrieved from the GCE metadata server.
            </summary>
            <param name="metadataJson">JSON metadata, normally retrieved from the GCE metadata server.
            Must not be <c>null</c>.</param>
            <returns>A populated <see cref="T:Google.Api.Gax.CloudRunPlatformDetails"/> if the metadata represents and GCE instance;
            <c>null</c> otherwise.</returns>
        </member>
        <member name="M:Google.Api.Gax.CloudRunPlatformDetails.#ctor(System.String,System.String,System.String,System.String,System.String,System.String)">
            <summary>
            Constructs details of a Google Cloud Run service revision.
            </summary>
            <param name="metadataJson">JSON metadata, normally retrieved from the GCE metadata server.
            Must not be <c>null</c>.</param>
            <param name="projectId">The project ID. Must not be null.</param>
            <param name="zone">The zone in which the service code is running. Must not be null.</param>
            <param name="serviceName">The name of the service. Must not be null.</param>
            <param name="revisionName">The name of the revision. Must not be null.</param>
            <param name="configurationName">The name of the configuration. Must not be null.</param>
        </member>
        <member name="P:Google.Api.Gax.CloudRunPlatformDetails.MetadataJson">
            <summary>
            The full JSON string retrieved from the metadata server. This is never null.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.CloudRunPlatformDetails.ProjectId">
            <summary>
            The Project ID under which this service is running. This is never null.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.CloudRunPlatformDetails.Zone">
            <summary>
            The zone of the service, e.g. "us-central1-1". This is never null.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.CloudRunPlatformDetails.Region">
            <summary>
            The region part of the zone. For example, a zone of "us-central1-1" has a region 
            of "us-central1".
            </summary>
        </member>
        <member name="P:Google.Api.Gax.CloudRunPlatformDetails.ServiceName">
            <summary>
            The name of the Cloud Run service being run. This is never null.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.CloudRunPlatformDetails.RevisionName">
            <summary>
            The name of the Cloud Run revision being run. This is never null.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.CloudRunPlatformDetails.ConfigurationName">
            <summary>
            The name of the Cloud Run configuration being run. This is never null.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.CloudRunPlatformDetails.ToString">
            <inheritdoc/>
        </member>
        <member name="T:Google.Api.Gax.EmptyDictionary`2">
            <summary>
            Provides cached instances of empty dictionaries.
            </summary>
            <typeparam name="TKey">The type of the keys in the dictionary.</typeparam>
            <typeparam name="TValue">The type of the values in the dictionary.</typeparam>
        </member>
        <member name="P:Google.Api.Gax.EmptyDictionary`2.Instance">
            <summary>
            Gets a cached empty <see cref="T:System.Collections.Generic.IDictionary`2"/>. The returned dictionary is read-only.
            </summary>
        </member>
        <member name="T:Google.Api.Gax.EmulatorDetection">
            <summary>
            Specifies whether or not an emulator configuration should be present and 
            whether or not it should be used. Emulator configuration is usually specified
            through environment variables.
            </summary>
        </member>
        <member name="F:Google.Api.Gax.EmulatorDetection.None">
            <summary>
            Ignores the presence or absence of emulator configuration.
            </summary>
        </member>
        <member name="F:Google.Api.Gax.EmulatorDetection.ProductionOnly">
            <summary>
            Always connect to the production servers, but throw an exception if
            an emulator configuration is detected that would suggest connecting to
            an emulator is expected.
            </summary>
        </member>
        <member name="F:Google.Api.Gax.EmulatorDetection.EmulatorOnly">
            <summary>
            Always connect to the emulator, throwing an exception if no emulator
            configuration is detected.
            </summary>
        </member>
        <member name="F:Google.Api.Gax.EmulatorDetection.EmulatorOrProduction">
            <summary>
            Connect to the emulator if an emulator configuration is detected,
            or production otherwise. This is a convenient option, but risks damage to
            production databases or running up unexpected bills if tests are accidentally
            run in production due to the emulator configuration being absent unexpectedly.
            (Using separate projects for production and testing is a best practice for
            preventing the first issue, but may be unrealistic for small or hobby projects.)
            </summary>
        </member>
        <member name="T:Google.Api.Gax.ExpirationType">
            <summary>
            The type of <see cref="T:Google.Api.Gax.Expiration"/>; none, timeout or deadline.
            </summary>
        </member>
        <member name="F:Google.Api.Gax.ExpirationType.None">
            <summary>
            No expiration; an infinite timeout.
            </summary>
        </member>
        <member name="F:Google.Api.Gax.ExpirationType.Timeout">
            <summary>
            Expiration is a relative timeout, represented by a <see cref="T:System.TimeSpan"/>.
            </summary>
        </member>
        <member name="F:Google.Api.Gax.ExpirationType.Deadline">
            <summary>
            Expiration is an absolute deadline, represented by a <see cref="T:System.DateTime"/>.
            </summary>
        </member>
        <member name="T:Google.Api.Gax.Expiration">
            <summary>
            Expiration specified by relative timeout or absolute deadline.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Expiration.FromTimeout(System.TimeSpan)">
            <summary>
            Create an <see cref="T:Google.Api.Gax.Expiration"/> with a relative timeout.
            </summary>
            <param name="timeout">The relative timeout.</param>
            <returns>An <see cref="T:Google.Api.Gax.Expiration"/> with the specified relative timeout.</returns>
            <remarks>
            Zero or negative timeouts are valid, and will cause immediate failure of the operation being performed.
            </remarks>
        </member>
        <member name="M:Google.Api.Gax.Expiration.FromDeadline(System.DateTime)">
            <summary>
            Create an <see cref="T:Google.Api.Gax.Expiration"/> with an absolute deadline.
            </summary>
            <param name="deadline">The absolute deadline. Should be a UTC datetime.</param>
            <returns>An <see cref="T:Google.Api.Gax.Expiration"/> with the specified absolute deadline.</returns>
            <remarks>
            Deadlines in the past are valid, and will cause immediate failure of the operation being performed.
            </remarks>
        </member>
        <member name="P:Google.Api.Gax.Expiration.None">
            <summary>
            An <see cref="T:Google.Api.Gax.Expiration"/> with no timeout or deadline.
            </summary>
            <remarks>
            Indicates that no expiration is required.
            </remarks>
        </member>
        <member name="P:Google.Api.Gax.Expiration.Timeout">
            <summary>
            If not null, the relative timeout of this expiration.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.Expiration.Deadline">
            <summary>
            If not null, the absolute deadline of this expiration.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.Expiration.Type">
            <summary>
            What <see cref="T:Google.Api.Gax.ExpirationType"/> is contained in this <see cref="T:Google.Api.Gax.Expiration"/>.
            </summary>
        </member>
        <member name="T:Google.Api.Gax.ExpirationExtensions">
            <summary>
            Extension methods for <see cref="T:Google.Api.Gax.Expiration"/>.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.ExpirationExtensions.CalculateDeadline(Google.Api.Gax.Expiration,Google.Api.Gax.IClock)">
            <summary>
            Calculate a deadline from an <see cref="T:Google.Api.Gax.Expiration"/> and a <see cref="T:Google.Api.Gax.IClock"/>.
            </summary>
            <param name="expiration"><see cref="T:Google.Api.Gax.Expiration"/>, may be null.</param>
            <param name="clock"><see cref="T:Google.Api.Gax.IClock"/> to use for deadline calculation.</param>
            <returns>The calculated absolute deadline, or null if no deadline should be used.</returns>
        </member>
        <member name="T:Google.Api.Gax.FlowControlSettings">
            <summary>
            Settings used to control data flow.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.FlowControlSettings.#ctor(System.Nullable{System.Int64},System.Nullable{System.Int64})">
            <summary>
            Creates a new instance with the specified settings.
            </summary>
            <param name="maxOutstandingElementCount">The maximum number of elements that can be outstanding before data flow is restricted, or
            null if there is no specified limit.</param>
            <param name="maxOutstandingByteCount">The maximum number of bytes that can be outstanding before data flow is restricted, or
            null if there is no specified limit.</param>
        </member>
        <member name="P:Google.Api.Gax.FlowControlSettings.MaxOutstandingElementCount">
            <summary>
            The maximum number of elements that can be outstanding before data flow is restricted, or
            null if there is no specified limit.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.FlowControlSettings.MaxOutstandingByteCount">
            <summary>
            The maximum number of bytes that can be outstanding before data flow is restricted, or
            null if there is no specified limit.
            </summary>
        </member>
        <member name="T:Google.Api.Gax.GaePlatformDetails">
            <summary>
            Google App Engine details.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.GaePlatformDetails.#ctor(System.String,System.String,System.String,System.String)">
            <summary>
            Construct details of Google App Engine
            </summary>
            <param name="gcloudProject">The Project ID associated with your application,
            which is visible in the Google Cloud Platform Console. Must not be <c>null</c>.</param>
            <param name="gaeInstance">The name of the current instance. Must not be <c>null</c>.</param>
            <param name="gaeService">The service name specified in your application's app.yaml file,
            or if no service name is specified, it is set to default. Must not be <c>null</c>.</param>
            <param name="gaeVersion">The version label of the current application. Must not be <c>null</c>.</param>
        </member>
        <member name="P:Google.Api.Gax.GaePlatformDetails.ProjectId">
            <summary>
            The Project ID associated with your application, which is visible in the Google Cloud Platform Console.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.GaePlatformDetails.InstanceId">
            <summary>
            The name of the current instance.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.GaePlatformDetails.ServiceId">
            <summary>
            The service name specified in your application's app.yaml file, or if no service name is specified, it is set to default.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.GaePlatformDetails.VersionId">
            <summary>
            The version label of the current application.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.GaePlatformDetails.ToString">
            <inheritdoc/>
        </member>
        <member name="T:Google.Api.Gax.GaxEqualityHelpers">
            <summary>
            Convenience methods to simplify implementing equality comparisons and hash codes.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.GaxEqualityHelpers.ListsEqual``1(System.Collections.Generic.IReadOnlyList{``0},System.Collections.Generic.IReadOnlyList{``0})">
            <summary>
            Checks if two lists are equal, in an ordering-sensitive manner, using the default equality comparer for the type.
            </summary>
            <param name="left">The left list to compare. May be null.</param>
            <param name="right">The right list to compare. May be null.</param>
            <returns>Whether or not the lists are equal</returns>
        </member>
        <member name="M:Google.Api.Gax.GaxEqualityHelpers.GetListHashCode``1(System.Collections.Generic.IReadOnlyList{``0})">
            <summary>
            Computes an ordering-sensitive hash code for a list, using the default equality comparer for the type.
            </summary>
            <param name="list">The list to compute a hash code for. May be null.</param>
            <returns>The computed hash code.</returns>
        </member>
        <member name="M:Google.Api.Gax.GaxEqualityHelpers.CombineHashCodes(System.Int32,System.Int32)">
            <summary>
            Combines two hash codes.
            </summary>
            <param name="hash1">The first hash code.</param>
            <param name="hash2">The second hash code.</param>
            <returns>The combined hash code.</returns>
        </member>
        <member name="M:Google.Api.Gax.GaxEqualityHelpers.CombineHashCodes(System.Int32,System.Int32,System.Int32)">
            <summary>
            Combines three hash codes.
            </summary>
            <param name="hash1">The first hash code.</param>
            <param name="hash2">The second hash code.</param>
            <param name="hash3">The third hash code.</param>
            <returns>The combined hash code.</returns>
        </member>
        <member name="M:Google.Api.Gax.GaxEqualityHelpers.CombineHashCodes(System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>
            Combines four hash codes.
            </summary>
            <param name="hash1">The first hash code.</param>
            <param name="hash2">The second hash code.</param>
            <param name="hash3">The third hash code.</param>
            <param name="hash4">The fourth hash code.</param>
            <returns>The combined hash code.</returns>
        </member>
        <member name="M:Google.Api.Gax.GaxEqualityHelpers.CombineHashCodes(System.Int32,System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>
            Combines five hash codes.
            </summary>
            <param name="hash1">The first hash code.</param>
            <param name="hash2">The second hash code.</param>
            <param name="hash3">The third hash code.</param>
            <param name="hash4">The fourth hash code.</param>
            <param name="hash5">The fifth hash code.</param>
            <returns>The combined hash code.</returns>
        </member>
        <member name="M:Google.Api.Gax.GaxEqualityHelpers.CombineHashCodes(System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>
            Combines six hash codes.
            </summary>
            <param name="hash1">The first hash code.</param>
            <param name="hash2">The second hash code.</param>
            <param name="hash3">The third hash code.</param>
            <param name="hash4">The fourth hash code.</param>
            <param name="hash5">The fifth hash code.</param>
            <param name="hash6">The sixth hash code.</param>
            <returns>The combined hash code.</returns>
        </member>
        <member name="M:Google.Api.Gax.GaxEqualityHelpers.CombineHashCodes(System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>
            Combines seven hash codes.
            </summary>
            <param name="hash1">The first hash code.</param>
            <param name="hash2">The second hash code.</param>
            <param name="hash3">The third hash code.</param>
            <param name="hash4">The fourth hash code.</param>
            <param name="hash5">The fifth hash code.</param>
            <param name="hash6">The sixth hash code.</param>
            <param name="hash7">The seventh hash code.</param>
            <returns>The combined hash code.</returns>
        </member>
        <member name="M:Google.Api.Gax.GaxEqualityHelpers.CombineHashCodes(System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>
            Combines eight hash codes.
            </summary>
            <param name="hash1">The first hash code.</param>
            <param name="hash2">The second hash code.</param>
            <param name="hash3">The third hash code.</param>
            <param name="hash4">The fourth hash code.</param>
            <param name="hash5">The fifth hash code.</param>
            <param name="hash6">The sixth hash code.</param>
            <param name="hash7">The seventh hash code.</param>
            <param name="hash8">The eight hash code.</param>
            <returns>The combined hash code.</returns>
        </member>
        <member name="T:Google.Api.Gax.GaxPreconditions">
            <summary>
            Preconditions for checking method arguments, state etc.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.GaxPreconditions.CheckNotNull``1(``0,System.String)">
            <summary>
            Checks that the given argument (to the calling method) is non-null.
            </summary>
            <typeparam name="T">The type of the parameter.</typeparam>
            <param name="argument">The argument provided for the parameter.</param>
            <param name="paramName">The name of the parameter in the calling method.</param>
            <exception cref="T:System.ArgumentNullException"><paramref name="argument"/> is null</exception>
            <returns><paramref name="argument"/> if it is not null</returns>
        </member>
        <member name="M:Google.Api.Gax.GaxPreconditions.CheckNotNullOrEmpty(System.String,System.String)">
            <summary>
            Checks that a string argument is neither null, nor an empty string.
            </summary>
            <param name="argument">The argument provided for the parameter.</param>
            <param name="paramName">The name of the parameter in the calling method.</param>
            <exception cref="T:System.ArgumentNullException"><paramref name="argument"/> is null</exception>
            <exception cref="T:System.ArgumentException"><paramref name="argument"/> is empty</exception>
            <returns><paramref name="argument"/> if it is not null or empty</returns>
        </member>
        <member name="M:Google.Api.Gax.GaxPreconditions.CheckArgumentRange(System.Int32,System.String,System.Int32,System.Int32)">
            <summary>
            Checks that the given argument value is valid.
            </summary>
            <remarks>
            Note that the upper bound (<paramref name="maxInclusive"/>) is inclusive,
            not exclusive. This is deliberate, to allow the specification of ranges which include
            <see cref="F:System.Int32.MaxValue"/>.
            </remarks>
            <param name="argument">The value of the argument passed to the calling method.</param>
            <param name="paramName">The name of the parameter in the calling method.</param>
            <param name="minInclusive">The smallest valid value.</param>
            <param name="maxInclusive">The largest valid value.</param>
            <returns><paramref name="argument"/> if it was in range</returns>
            <exception cref="T:System.ArgumentOutOfRangeException">The argument was outside the specified range.</exception>
        </member>
        <member name="M:Google.Api.Gax.GaxPreconditions.CheckArgumentRange``1(``0,System.String,``0,``0)">
            <summary>
            Checks that the given argument value is valid.
            </summary>
            <remarks>
            Note that the upper bound (<paramref name="maxInclusive"/>) is inclusive,
            not exclusive. This is deliberate, to allow the specification of ranges which include
            <see cref="F:System.Int64.MaxValue"/>.
            </remarks>
            <param name="argument">The value of the argument passed to the calling method.</param>
            <param name="paramName">The name of the parameter in the calling method.</param>
            <param name="minInclusive">The smallest valid value.</param>
            <param name="maxInclusive">The largest valid value.</param>
            <returns><paramref name="argument"/> if it was in range</returns>
            <exception cref="T:System.ArgumentOutOfRangeException">The argument was outside the specified range.</exception>
        </member>
        <member name="M:Google.Api.Gax.GaxPreconditions.CheckArgumentRange``1(System.Nullable{``0},System.String,``0,``0)">
            <summary>
            Checks that the given argument value, if not <c>null</c>, is valid.
            </summary>
            <remarks>
            Note that the upper bound (<paramref name="maxInclusive"/>) is inclusive,
            not exclusive. This is deliberate, to allow the specification of ranges which include
            <see cref="F:System.Int32.MaxValue"/>.
            </remarks>
            <param name="argument">The value of the argument passed to the calling method.</param>
            <param name="paramName">The name of the parameter in the calling method.</param>
            <param name="minInclusive">The smallest valid value.</param>
            <param name="maxInclusive">The largest valid value.</param>
            <returns><paramref name="argument"/> if it was in range, or <c>null</c>.</returns>
            <exception cref="T:System.ArgumentOutOfRangeException">The argument was outside the specified range.</exception>
        </member>
        <member name="M:Google.Api.Gax.GaxPreconditions.CheckNonNegative(System.Int32,System.String)">
            <summary>
            Checks that the given argument value is not negative.
            </summary>
            <param name="argument">The value of the argument passed to the calling method.</param>
            <param name="paramName">The name of the parameter in the calling method.</param>
            <returns><paramref name="argument"/> if it was non-negative.</returns>
            <exception cref="T:System.ArgumentOutOfRangeException">The argument was negative.</exception>
        </member>
        <member name="M:Google.Api.Gax.GaxPreconditions.CheckNonNegative(System.Nullable{System.Int32},System.String)">
            <summary>
            Checks that the given argument value, if not <c>null</c>, is not negative.
            </summary>
            <param name="argument">The value of the argument passed to the calling method.</param>
            <param name="paramName">The name of the parameter in the calling method.</param>
            <returns><paramref name="argument"/> if it was non-negative, or <c>null</c>.</returns>
            <exception cref="T:System.ArgumentOutOfRangeException">The argument was negative.</exception>
        </member>
        <member name="M:Google.Api.Gax.GaxPreconditions.CheckNonNegative(System.Int64,System.String)">
            <summary>
            Checks that the given argument value is not negative.
            </summary>
            <param name="argument">The value of the argument passed to the calling method.</param>
            <param name="paramName">The name of the parameter in the calling method.</param>
            <returns><paramref name="argument"/> if it was non-negative.</returns>
            <exception cref="T:System.ArgumentOutOfRangeException">The argument was negative.</exception>
        </member>
        <member name="M:Google.Api.Gax.GaxPreconditions.CheckNonNegative(System.Nullable{System.Int64},System.String)">
            <summary>
            Checks that the given argument value, if not <c>null</c>, is not negative.
            </summary>
            <param name="argument">The value of the argument passed to the calling method.</param>
            <param name="paramName">The name of the parameter in the calling method.</param>
            <returns><paramref name="argument"/> if it was non-negative, or <c>null</c>.</returns>
            <exception cref="T:System.ArgumentOutOfRangeException">The argument was negative.</exception>
        </member>
        <member name="M:Google.Api.Gax.GaxPreconditions.CheckNonNegative(System.Double,System.String)">
            <summary>
            Checks that the given argument value is not negative.
            </summary>
            <param name="argument">The value of the argument passed to the calling method.</param>
            <param name="paramName">The name of the parameter in the calling method.</param>
            <returns><paramref name="argument"/> if it was non-negative.</returns>
            <exception cref="T:System.ArgumentOutOfRangeException">The argument was negative.</exception>
        </member>
        <member name="M:Google.Api.Gax.GaxPreconditions.CheckNonNegative(System.Nullable{System.Double},System.String)">
            <summary>
            Checks that the given argument value, if not <c>null</c>, is not negative.
            </summary>
            <param name="argument">The value of the argument passed to the calling method.</param>
            <param name="paramName">The name of the parameter in the calling method.</param>
            <returns><paramref name="argument"/> if it was non-negative, or <c>null</c>.</returns>
            <exception cref="T:System.ArgumentOutOfRangeException">The argument was negative.</exception>
        </member>
        <member name="M:Google.Api.Gax.GaxPreconditions.CheckState(System.Boolean,System.String)">
            <summary>
            Checks that given condition is met, throwing an <see cref="T:System.InvalidOperationException"/> otherwise.
            </summary>
            <param name="condition">The (already evaluated) condition to check.</param>
            <param name="message">The message to include in the exception, if generated. This should not
            use interpolation, as the interpolation would be performed regardless of whether or
            not an exception is thrown.</param>
        </member>
        <member name="M:Google.Api.Gax.GaxPreconditions.CheckState``1(System.Boolean,System.String,``0)">
            <summary>
            Checks that given condition is met, throwing an <see cref="T:System.InvalidOperationException"/> otherwise.
            </summary>
            <param name="condition">The (already evaluated) condition to check.</param>
            <param name="format">The format string to use to create the exception message if the
            condition is not met.</param>
            <param name="arg0">The argument to the format string.</param>
        </member>
        <member name="M:Google.Api.Gax.GaxPreconditions.CheckState``2(System.Boolean,System.String,``0,``1)">
            <summary>
            Checks that given condition is met, throwing an <see cref="T:System.InvalidOperationException"/> otherwise.
            </summary>
            <param name="condition">The (already evaluated) condition to check.</param>
            <param name="format">The format string to use to create the exception message if the
            condition is not met.</param>
            <param name="arg0">The first argument to the format string.</param>
            <param name="arg1">The second argument to the format string.</param>
        </member>
        <member name="M:Google.Api.Gax.GaxPreconditions.CheckState``3(System.Boolean,System.String,``0,``1,``2)">
            <summary>
            Checks that given condition is met, throwing an <see cref="T:System.InvalidOperationException"/> otherwise.
            </summary>
            <param name="condition">The (already evaluated) condition to check.</param>
            <param name="format">The format string to use to create the exception message if the
            condition is not met.</param>
            <param name="arg0">The first argument to the format string.</param>
            <param name="arg1">The second argument to the format string.</param>
            <param name="arg2">The third argument to the format string.</param>
        </member>
        <member name="M:Google.Api.Gax.GaxPreconditions.CheckArgument(System.Boolean,System.String,System.String)">
            <summary>
            Checks that given argument-based condition is met, throwing an <see cref="T:System.ArgumentException"/> otherwise.
            </summary>
            <param name="condition">The (already evaluated) condition to check.</param>
            <param name="paramName">The name of the parameter whose value is being tested.</param>
            <param name="message">The message to include in the exception, if generated. This should not
            use interpolation, as the interpolation would be performed regardless of whether or not an exception
            is thrown.</param>
        </member>
        <member name="M:Google.Api.Gax.GaxPreconditions.CheckArgument``1(System.Boolean,System.String,System.String,``0)">
            <summary>
            Checks that given argument-based condition is met, throwing an <see cref="T:System.ArgumentException"/> otherwise.
            </summary>
            <param name="condition">The (already evaluated) condition to check.</param>
            <param name="paramName">The name of the parameter whose value is being tested.</param>
            <param name="format">The format string to use to create the exception message if the
            condition is not met.</param>
            <param name="arg0">The argument to the format string.</param>
        </member>
        <member name="M:Google.Api.Gax.GaxPreconditions.CheckArgument``2(System.Boolean,System.String,System.String,``0,``1)">
            <summary>
            Checks that given argument-based condition is met, throwing an <see cref="T:System.ArgumentException"/> otherwise.
            </summary>
            <param name="condition">The (already evaluated) condition to check.</param>
            <param name="paramName">The name of the parameter whose value is being tested.</param>
            <param name="format">The format string to use to create the exception message if the
            condition is not met.</param>
            <param name="arg0">The first argument to the format string.</param>
            <param name="arg1">The second argument to the format string.</param>
        </member>
        <member name="M:Google.Api.Gax.GaxPreconditions.CheckEnumValue``1(``0,System.String)">
            <summary>
            Checks that the given value is in fact defined in the enum used as the type argument of the method.
            </summary>
            <typeparam name="T">The enum type to check the value within.</typeparam>
            <param name="value">The value to check.</param>
            <param name="paramName">The name of the parameter whose value is being tested.</param>
            <returns><paramref name="value"/> if it was a defined value</returns>
        </member>
        <member name="M:Google.Api.Gax.GaxPreconditions.CheckNonNegativeDelay(System.TimeSpan,System.String)">
            <summary>
            Checks that the given <see cref="T:System.TimeSpan"/> used as a delay is non-negative. This is a very specific
            call; most users won't need it.
            </summary>
            <param name="value">The value to check.</param>
            <param name="paramName">The name of the parameter whose value is being tested.</param>
        </member>
        <member name="T:Google.Api.Gax.GcePlatformDetails">
            <summary>
            Google Compute Engine details.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.GcePlatformDetails.TryLoad(System.String)">
            <summary>
            Builds a <see cref="T:Google.Api.Gax.GcePlatformDetails"/> from the given metadata.
            This metadata is normally retrieved from the GCE metadata server.
            </summary>
            <param name="metadataJson">JSON metadata, normally retrieved from the GCE metadata server.
            Must not be <c>null</c>.</param>
            <returns>A populated <see cref="T:Google.Api.Gax.GcePlatformDetails"/> if the metadata represents and GCE instance;
            <c>null</c> otherwise.</returns>
        </member>
        <member name="M:Google.Api.Gax.GcePlatformDetails.#ctor(System.String,System.String,System.String,System.String)">
            <summary>
            Construct details of Google Compute Engine
            </summary>
            <param name="metadataJson">The full JSON string retrieved from the metadata server. Must not be <c>null</c>.</param>
            <param name="projectId">The project ID. Must not be <c>null</c>.</param>
            <param name="instanceId">The instance ID. Must not be <c>null</c>.</param>
            <param name="zoneName">The zone name. Must not be <c>null</c>.
            If this value is in the format <code>projects/&lt;project-number&gt;/zones/&lt;zone-name&gt;</code>
            then <see cref="P:Google.Api.Gax.GcePlatformDetails.Location"/> will return the <code>&lt;zone-name&gt;</code> part of this value.
            If not, <see cref="P:Google.Api.Gax.GcePlatformDetails.Location"/> will throw <see cref="T:System.InvalidOperationException"/>.
            If this value has been retrived from Google Compute Engine, the it's format will be the one
            described above.</param>
        </member>
        <member name="P:Google.Api.Gax.GcePlatformDetails.MetadataJson">
            <summary>
            The full JSON string retrieved from the metadata server. This is never null.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.GcePlatformDetails.ProjectId">
            <summary>
            The Project ID under which this GCE instance is running. This is never null.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.GcePlatformDetails.InstanceId">
            <summary>
             The Instance ID of the GCE instance on which this is running. This is never null.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.GcePlatformDetails.ZoneName">
            <summary>
            The zone where this GCE instance is running. This is never null.
            This will be in the format <code>projects/&lt;project-number&gt;/zones/&lt;zone-name&gt;</code>
            id the value has been retrieved from Google Compute Engine.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.GcePlatformDetails.Location">
            <summary>
            The zone name where this GCE instance is running.
            If <see cref="P:Google.Api.Gax.GcePlatformDetails.ZoneName"/> is in the format <code>projects/&lt;project-number&gt;/zones/&lt;zone-name&gt;</code>
            this value will be the <code>&lt;zone-name&gt;</code> part in <see cref="P:Google.Api.Gax.GcePlatformDetails.ZoneName"/>.
            If <see cref="P:Google.Api.Gax.GcePlatformDetails.ZoneName"/> is in a different format then this getting the value of this property will
            throw <see cref="T:System.InvalidOperationException"/>.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.GcePlatformDetails.ToString">
            <inheritdoc/>
        </member>
        <member name="T:Google.Api.Gax.GkePlatformDetails">
            <summary>
            Google Container (Kubernetes) Engine details.
            </summary>
        </member>
        <member name="T:Google.Api.Gax.GkePlatformDetails.KubernetesData">
            <summary>
            Data from the Kubernetes API
            </summary>
        </member>
        <member name="P:Google.Api.Gax.GkePlatformDetails.KubernetesData.PodName">
            <summary>
            The Kubernetes pod name
            </summary>
        </member>
        <member name="P:Google.Api.Gax.GkePlatformDetails.KubernetesData.NamespaceName">
            <summary>
            The Kubernetes namespace name
            </summary>
        </member>
        <member name="P:Google.Api.Gax.GkePlatformDetails.KubernetesData.NamespaceJson">
            <summary>
            JSON from https://kubernetes/api/v1/namespaces/{namespace}
            </summary>
        </member>
        <member name="P:Google.Api.Gax.GkePlatformDetails.KubernetesData.PodJson">
            <summary>
            JSON from https://kubernetes/api/v1/namespaces/{namespace}/pods/{pod-name}
            </summary>
        </member>
        <member name="P:Google.Api.Gax.GkePlatformDetails.KubernetesData.MountInfo">
            <summary>
            Lines from /proc/self/mountinfo
            </summary>
        </member>
        <member name="M:Google.Api.Gax.GkePlatformDetails.TryLoad(System.String,Google.Api.Gax.GkePlatformDetails.KubernetesData)">
            <summary>
            Builds a <see cref="T:Google.Api.Gax.GkePlatformDetails"/> from the given metadata and kubernetes data.
            The metadata is normally retrieved from the GCE metadata server.
            The kubernetes data is normally retrieved using the kubernetes API.
            This method attempts to return as much information as it is present on <paramref name="metadataJson"/>
            and <paramref name="kubernetesData"/> but will return <see cref="F:System.String.Empty"/> for <see cref="T:Google.Api.Gax.GkePlatformDetails"/>
            properties whose corresponding information is corrupt or missing in <paramref name="metadataJson"/>
            or <paramref name="kubernetesData"/>.
            </summary>
            <param name="metadataJson">JSON metadata, normally retrieved from the GCE metadata server.
            Must not be <c>null</c>.</param>
            <param name="kubernetesData">Kubernetes data, normally retrieved using the kubernetes API.
            Must not be <c>null</c>.</param>
            <returns>A populated <see cref="T:Google.Api.Gax.GkePlatformDetails"/> if the metadata represents and GKE instance;
            <c>null</c> otherwise.</returns>
        </member>
        <member name="M:Google.Api.Gax.GkePlatformDetails.#ctor(System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String)">
            <summary>
            Construct details of Google Container (Kubernetes) Engine
            </summary>
            <param name="metadataJson">The full JSON string retrieved from the metadata server. Must not be <c>null</c>.</param>
            <param name="projectId">The project ID. Must not be <c>null</c>.</param>
            <param name="clusterName">The cluster name. Must not be <c>null</c>.</param>
            <param name="location">The location. Must not be <c>null</c>.</param>
            <param name="hostName">The instance host name. Must not be <c>null</c>.</param>
            <param name="instanceId">The GCE instance ID. Must not be <c>null</c>.</param>
            <param name="zone">The zone. Must not be <c>null</c>.</param>
            <param name="namespaceId">The kubernetes namespace ID. Must not be <c>null</c>.</param>
            <param name="podId">The kubernetes pod ID. Must not be <c>null</c>.</param>
            <param name="containerName">The container name. Must not be <c>null</c>.</param>
            <param name="clusterLocation">The location of the cluster. Must not be null.</param>
        </member>
        <member name="P:Google.Api.Gax.GkePlatformDetails.MetadataJson">
            <summary>
            The full JSON string retrieved from the metadata server.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.GkePlatformDetails.ProjectId">
            <summary>
            The Project ID associated with your application, which is visible in the Google Cloud Platform Console.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.GkePlatformDetails.ClusterName">
            <summary>
            The cluster name, which is visible in the Google Cloud Platform Console.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.GkePlatformDetails.Location">
            <summary>
            The cluster location, which is visible in the Google Cloud Platform Console.
            This is equivalent to the value of the <code>&lt;zone-name&gt;</code> part in
            <see cref="P:Google.Api.Gax.GkePlatformDetails.Zone"/>
            </summary>
        </member>
        <member name="P:Google.Api.Gax.GkePlatformDetails.HostName">
            <summary>
            The hostname of this instance.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.GkePlatformDetails.InstanceId">
            <summary>
            The GCE instance this container is running in.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.GkePlatformDetails.Zone">
            <summary>
            The GCE zone in which the instance is running.
            This is in the format <code>projects/&lt;project-number&gt;/zones/&lt;zone-name&gt;</code>.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.GkePlatformDetails.NamespaceId">
            <summary>
            The cluster namespace the container is running in.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.GkePlatformDetails.PodId">
            <summary>
            The pos the container is running in.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.GkePlatformDetails.ContainerName">
            <summary>
            The name of the container.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.GkePlatformDetails.ClusterLocation">
            <summary>
            The location of the cluster.
            May be different from node / pod location.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.GkePlatformDetails.ToString">
            <inheritdoc/>
        </member>
        <member name="T:Google.Api.Gax.IClock">
            <summary>
            An abstraction of the ability to determine the current date and time.
            </summary>
            <remarks>
            This interface primarily exists for testing purposes, allowing test code to
            isolate itself from the system clock. In production, the <see cref="T:Google.Api.Gax.SystemClock"/>
            implementation is by far the most likely one to be used, and the only one provided
            within this library. Code that uses a clock should generally be designed to allow it
            to be optionally specified, defaulting to <see cref="P:Google.Api.Gax.SystemClock.Instance"/>.
            </remarks>
        </member>
        <member name="M:Google.Api.Gax.IClock.GetCurrentDateTimeUtc">
            <summary>
            Returns the current date and time in UTC, with a kind of <see cref="F:System.DateTimeKind.Utc"/>.
            </summary>
            <returns>A <see cref="T:System.DateTime"/> representing the current date and time in UTC.</returns>
        </member>
        <member name="T:Google.Api.Gax.IResourceName">
            <summary>
            A resource name.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.IResourceName.IsKnownPattern">
            <summary>
            Whether this instance contains a resource name with a known pattern.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.IResourceName.ToString">
            <summary>
            The string representation of the resource name.
            </summary>
            <returns>The string representation of the resource name.</returns>
        </member>
        <member name="T:Google.Api.Gax.IScheduler">
            <summary>
            Abstraction of scheduler-like operations, used for testability.
            </summary>
            <remarks>
            Note that this is different to <see cref="T:System.Threading.Tasks.TaskScheduler"/>, which is really involved
            with assigning tasks to threads rather than any sort of delay.
            </remarks>
        </member>
        <member name="M:Google.Api.Gax.IScheduler.Delay(System.TimeSpan,System.Threading.CancellationToken)">
            <summary>
            Returns a task which will complete after the given delay. Whether the returned
            awaitable is configured to capture the current context or not is implementation-specific.
            (A test implementation may capture the current context to enable reliable testing.)
            </summary>
            <param name="delay">Time to delay for. Must not be negative.</param>
            <param name="cancellationToken">The cancellation token that will be checked prior to completing the returned task.</param>
            <returns>A task which will complete after the given delay.</returns>
        </member>
        <member name="T:Google.Api.Gax.SchedulerExtensions">
            <summary>
            Extension methods for <see cref="T:Google.Api.Gax.IScheduler"/>.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.SchedulerExtensions.Sleep(Google.Api.Gax.IScheduler,System.TimeSpan,System.Threading.CancellationToken)">
            <summary>
            Simulates a synchronous delay by calling <see cref="M:Google.Api.Gax.IScheduler.Delay(System.TimeSpan,System.Threading.CancellationToken)"/> on
            <paramref name="scheduler"/>, and unwrapping any exceptions generated (typically cancellation).
            </summary>
            <param name="scheduler">The scheduler to use for the sleep operation.</param>
            <param name="delay">Time to sleep for. Must not be negative.</param>
            <param name="cancellationToken">The cancellation token that will be watched during the sleep operation.</param>
            <exception cref="T:System.OperationCanceledException">The cancellation token was cancelled during the sleep.</exception>
        </member>
        <member name="T:Google.Api.Gax.Json.InvalidJsonException">
            <summary>
            Thrown when an attempt is made to parse invalid JSON, e.g. using
            a non-string property key, or including a redundant comma.
            </summary>
        </member>
        <member name="T:Google.Api.Gax.Json.JsonBuilder">
            <summary>
            Utility class for generating JSON. This class doesn't perform much in the way of validity
            checks - that's left as the responsibility of the caller. However, it allows values and properties
            to be written in a simple, chainable way, without the caller needing to worry about adding commas.
            </summary>
        </member>
        <member name="F:Google.Api.Gax.Json.JsonBuilder.CommonRepresentations">
            <summary>
            The JSON representation of the first 160 characters of Unicode.
            Empty strings are replaced by the static constructor.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Json.JsonBuilder.#ctor(System.Text.StringBuilder)">
            <summary>
            Constructs a new instance using the specified <see cref="T:System.Text.StringBuilder"/>.
            </summary>
            <param name="builder">The <see cref="T:System.Text.StringBuilder"/> to append to. Must not be null.</param>
        </member>
        <member name="M:Google.Api.Gax.Json.JsonBuilder.#ctor">
            <summary>
            Constructs a new instance with a new <see cref="T:System.Text.StringBuilder"/>.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Json.JsonBuilder.StartObject">
            <summary>
            Writes the start of an object to the builder.
            </summary>
            <returns>A reference to the same <see cref="T:Google.Api.Gax.Json.JsonBuilder"/> the method was called on, for chaining.</returns>
        </member>
        <member name="M:Google.Api.Gax.Json.JsonBuilder.StartArray">
            <summary>
            Writes the start of an array to the builder.
            </summary>
            <returns>A reference to the same <see cref="T:Google.Api.Gax.Json.JsonBuilder"/> the method was called on, for chaining.</returns>
        </member>
        <member name="M:Google.Api.Gax.Json.JsonBuilder.EndArray">
            <summary>
            Writes the end of an array to the builder.
            </summary>
            <returns>A reference to the same <see cref="T:Google.Api.Gax.Json.JsonBuilder"/> the method was called on, for chaining.</returns>
        </member>
        <member name="M:Google.Api.Gax.Json.JsonBuilder.EndObject">
            <summary>
            Writes the end of an object to the builder.
            </summary>
            <returns>A reference to the same <see cref="T:Google.Api.Gax.Json.JsonBuilder"/> the method was called on, for chaining.</returns>
        </member>
        <member name="M:Google.Api.Gax.Json.JsonBuilder.Value(System.String)">
            <summary>
            Writes a string value to the builder, escaping it if necessary.
            </summary>
            <param name="value">The value to write. May be null.</param>
            <returns>A reference to the same <see cref="T:Google.Api.Gax.Json.JsonBuilder"/> the method was called on, for chaining.</returns>
        </member>
        <member name="M:Google.Api.Gax.Json.JsonBuilder.Value(System.Boolean)">
            <summary>
            Writes a Boolean value to the builder.
            </summary>
            <param name="value">The value to write.</param>
            <returns>A reference to the same <see cref="T:Google.Api.Gax.Json.JsonBuilder"/> the method was called on, for chaining.</returns>
        </member>
        <member name="M:Google.Api.Gax.Json.JsonBuilder.Value(System.Double)">
            <summary>
            Writes a numeric value to the builder.
            </summary>
            <param name="value">The value to write.</param>
            <returns>A reference to the same <see cref="T:Google.Api.Gax.Json.JsonBuilder"/> the method was called on, for chaining.</returns>
        </member>
        <member name="M:Google.Api.Gax.Json.JsonBuilder.Property(System.String,System.String)">
            <summary>
            Writes a name/value property pair to the builder for a string value.
            </summary>
            <param name="name">The name of the property. Must not be null.</param>
            <param name="value">The value of the property. May be null.</param>
            <returns>A reference to the same <see cref="T:Google.Api.Gax.Json.JsonBuilder"/> the method was called on, for chaining.</returns>
        </member>
        <member name="M:Google.Api.Gax.Json.JsonBuilder.Property(System.String,System.Boolean)">
            <summary>
            Writes a name/value property pair to the builder for a Boolean value.
            </summary>
            <param name="name">The name of the property. Must not be null.</param>
            <param name="value">The value of the property.</param>
            <returns>A reference to the same <see cref="T:Google.Api.Gax.Json.JsonBuilder"/> the method was called on, for chaining.</returns>
        </member>
        <member name="M:Google.Api.Gax.Json.JsonBuilder.Property(System.String,System.Double)">
            <summary>
            Writes a name/value property pair to the builder for a numeric value.
            </summary>
            <param name="name">The name of the property. Must not be null.</param>
            <param name="value">The value of the property.</param>
            <returns>A reference to the same <see cref="T:Google.Api.Gax.Json.JsonBuilder"/> the method was called on, for chaining.</returns>
        </member>
        <member name="M:Google.Api.Gax.Json.JsonBuilder.PropertyName(System.String)">
            <summary>
            Writes a property name to the builder, so that an array or object value may then be written.
            </summary>
            <param name="name">The name of the property. Must not be null.</param>
            <returns>A reference to the same <see cref="T:Google.Api.Gax.Json.JsonBuilder"/> the method was called on, for chaining.</returns>
        </member>
        <member name="M:Google.Api.Gax.Json.JsonBuilder.ToString">
            <summary>
            Returns the string representation of the underlying <see cref="T:System.Text.StringBuilder"/>.
            If this builder was created with an existing StringBuilder instance, any data written earlier
            will be included in the result.
            </summary>
            <returns>The string representation of the builder.</returns>
        </member>
        <member name="T:Google.Api.Gax.Json.JsonParser">
            <summary>
            Crude JSON parser. This is only capable of parsing documents that are fully compliant with RFC 7159.
            Each JSON value is represented as:
            
            - JSON string: System.String
            - JSON number: System.Double
            - JSON array: System.Collections.Generic.List`1{System.Object}
            - JSON object: System.Collections.Generic.Dictionary`1{System.String, System.Object}
            - JSON Boolean: System.Boolean
            - JSON null: null reference
            </summary>
        </member>
        <member name="T:Google.Api.Gax.Json.JsonTokenizer">
            <summary>
            Simple but strict JSON tokenizer, rigidly following RFC 7159.
            </summary>
            <remarks>
            <para>
            This tokenizer is stateful, and only returns "useful" tokens - names, values etc.
            It does not create tokens for the separator between names and values, or for the comma
            between values. It validates the token stream as it goes - so callers can assume that the
            tokens it produces are appropriate. For example, it would never produce "start object, end array."
            </para>
            <para>Not thread-safe.</para>
            </remarks>
        </member>
        <member name="M:Google.Api.Gax.Json.JsonTokenizer.FromTextReader(System.IO.TextReader)">
            <summary>
             Creates a tokenizer that reads from the given text reader.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Json.JsonTokenizer.PushContainer(Google.Api.Gax.Json.JsonTokenizer.ContainerType)">
            <summary>
            Pushes a new container type onto the stack and validates that we don't violate the maximum depth.
            </summary>
            <param name="type"></param>
        </member>
        <member name="M:Google.Api.Gax.Json.JsonTokenizer.Peek">
            <summary>
            Peeks at the next token in the stream, without changing the visible state.
            </summary>
            <returns>The next token in the stream.</returns>
        </member>
        <member name="M:Google.Api.Gax.Json.JsonTokenizer.SkipValue">
            <summary>
            Skips the value we're about to read. This must only be called immediately after reading a property name.
            If the value is an object or an array, the complete object/array is skipped.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Json.JsonTokenizer.Next">
            <summary>
            Returns the next JSON token in the stream.
            </summary>
            <exception cref="T:System.InvalidOperationException">This method is called after an EndDocument token has been returned</exception>
            <exception cref="T:Google.Api.Gax.Json.InvalidJsonException">The input text does not comply with RFC 7159</exception>
            <returns>The next token in the stream.</returns>
        </member>
        <member name="M:Google.Api.Gax.Json.JsonTokenizer.ReadString">
            <summary>
            Reads a string token. It is assumed that the opening " has already been read.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Json.JsonTokenizer.ReadEscapedCharacter">
            <summary>
            Reads an escaped character. It is assumed that the leading backslash has already been read.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Json.JsonTokenizer.ReadUnicodeEscape">
            <summary>
            Reads an escaped Unicode 4-nybble hex sequence. It is assumed that the leading \u has already been read.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Json.JsonTokenizer.ConsumeLiteral(System.String)">
            <summary>
            Consumes a text-only literal, throwing an exception if the read text doesn't match it.
            It is assumed that the first letter of the literal has already been read.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Json.JsonTokenizer.ValidateAndModifyStateForValue(System.String)">
            <summary>
            Validates that we're in a valid state to read a value (using the given error prefix if necessary)
            and changes the state to the appropriate one, e.g. ObjectAfterColon to ObjectAfterProperty.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Json.JsonTokenizer.PopContainer">
            <summary>
            Pops the top-most container, and sets the state to the appropriate one for the end of a value
            in the parent container.
            </summary>
        </member>
        <member name="T:Google.Api.Gax.Json.JsonTokenizer.State">
            <summary>
            Possible states of the tokenizer.
            </summary>
            <remarks>
            <para>This is a flags enum purely so we can simply and efficiently represent a set of valid states
            for checking.</para>
            <para>
            Each is documented with an example,
            where ^ represents the current position within the text stream. The examples all use string values,
            but could be any value, including nested objects/arrays.
            The complete state of the tokenizer also includes a stack to indicate the contexts (arrays/objects).
            Any additional notional state of "AfterValue" indicates that a value has been completed, at which 
            point there's an immediate transition to ExpectedEndOfDocument,  ObjectAfterProperty or ArrayAfterValue.
            </para>
            <para>
            These states were derived manually by reading RFC 7159 carefully.
            </para>
            </remarks>
        </member>
        <member name="F:Google.Api.Gax.Json.JsonTokenizer.State.StartOfDocument">
            <summary>
            ^ { "foo": "bar" }
            Before the value in a document. Next states: ObjectStart, ArrayStart, "AfterValue"
            </summary>
        </member>
        <member name="F:Google.Api.Gax.Json.JsonTokenizer.State.ExpectedEndOfDocument">
            <summary>
            { "foo": "bar" } ^
            After the value in a document. Next states: ReaderExhausted
            </summary>
        </member>
        <member name="F:Google.Api.Gax.Json.JsonTokenizer.State.ReaderExhausted">
            <summary>
            { "foo": "bar" } ^ (and already read to the end of the reader)
            Terminal state.
            </summary>
        </member>
        <member name="F:Google.Api.Gax.Json.JsonTokenizer.State.ObjectStart">
            <summary>
            { ^ "foo": "bar" }
            Before the *first* property in an object.
            Next states:
            "AfterValue" (empty object)
            ObjectBeforeColon (read a name)
            </summary>
        </member>
        <member name="F:Google.Api.Gax.Json.JsonTokenizer.State.ObjectBeforeColon">
            <summary>
            { "foo" ^ : "bar", "x": "y" }
            Next state: ObjectAfterColon
            </summary>
        </member>
        <member name="F:Google.Api.Gax.Json.JsonTokenizer.State.ObjectAfterColon">
            <summary>
            { "foo" : ^ "bar", "x": "y" }
            Before any property other than the first in an object.
            (Equivalently: after any property in an object) 
            Next states:
            "AfterValue" (value is simple)
            ObjectStart (value is object)
            ArrayStart (value is array)
            </summary>
        </member>
        <member name="F:Google.Api.Gax.Json.JsonTokenizer.State.ObjectAfterProperty">
            <summary>
            { "foo" : "bar" ^ , "x" : "y" }
            At the end of a property, so expecting either a comma or end-of-object
            Next states: ObjectAfterComma or "AfterValue"
            </summary>
        </member>
        <member name="F:Google.Api.Gax.Json.JsonTokenizer.State.ObjectAfterComma">
            <summary>
            { "foo":"bar", ^ "x":"y" }
            Read the comma after the previous property, so expecting another property.
            This is like ObjectStart, but closing brace isn't valid here
            Next state: ObjectBeforeColon.
            </summary>
        </member>
        <member name="F:Google.Api.Gax.Json.JsonTokenizer.State.ArrayStart">
            <summary>
            [ ^ "foo", "bar" ]
            Before the *first* value in an array.
            Next states:
            "AfterValue" (read a value)
            "AfterValue" (end of array; will pop stack)
            </summary>
        </member>
        <member name="F:Google.Api.Gax.Json.JsonTokenizer.State.ArrayAfterValue">
            <summary>
            [ "foo" ^ , "bar" ]
            After any value in an array, so expecting either a comma or end-of-array
            Next states: ArrayAfterComma or "AfterValue"
            </summary>
        </member>
        <member name="F:Google.Api.Gax.Json.JsonTokenizer.State.ArrayAfterComma">
            <summary>
            [ "foo", ^ "bar" ]
            After a comma in an array, so there *must* be another value (simple or complex).
            Next states: "AfterValue" (simple value), StartObject, StartArray
            </summary>
        </member>
        <member name="T:Google.Api.Gax.Json.JsonTokenizer.PushBackReader">
            <summary>
            Wrapper around a text reader allowing small amounts of buffering and location handling.
            </summary>
        </member>
        <member name="F:Google.Api.Gax.Json.JsonTokenizer.PushBackReader.nextChar">
            <summary>
            The buffered next character, if we have one.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Json.JsonTokenizer.PushBackReader.Read">
            <summary>
            Returns the next character in the stream, or null if we have reached the end.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Google.Api.Gax.Json.JsonTokenizer.PushBackReader.CreateException(System.String)">
            <summary>
            Creates a new exception appropriate for the current state of the reader.
            </summary>
        </member>
        <member name="T:Google.Api.Gax.Page`1">
            <summary>
            A page of resources which will only have fewer results than requested if
            there is no more data to fetch.
            </summary>
            <typeparam name="TResource">The type of resource within the page.</typeparam>
        </member>
        <member name="P:Google.Api.Gax.Page`1.NextPageToken">
            <summary>
            The page token to use to fetch the next set of resources.
            </summary>
            <remarks>
            gRPC-based APIs use an empty string as a "no page token", whereas REST-based APIs
            use a null reference instead. The value here will be consistent with the value returned
            by the API itself.
            </remarks>
        </member>
        <member name="M:Google.Api.Gax.Page`1.#ctor(System.Collections.Generic.IEnumerable{`0},System.String)">
            <summary>
            Constructs a fixed-size page from the given resource sequence and page token.
            </summary>
            <param name="resources">The resources in the page.</param>
            <param name="nextPageToken">The next page token.</param>
        </member>
        <member name="M:Google.Api.Gax.Page`1.GetEnumerator">
            <inheritdoc />
        </member>
        <member name="M:Google.Api.Gax.Page`1.System#Collections#IEnumerable#GetEnumerator">
            <inheritdoc />
        </member>
        <member name="T:Google.Api.Gax.PagedAsyncEnumerable`2">
            <summary>
            An asynchronous sequence of resources obtained via API responses. Application code
            can treat this as a simple sequence (with API calls automatically being made
            lazily as more results are required), or call <see cref="M:Google.Api.Gax.PagedAsyncEnumerable`2.AsRawResponses"/> to retrieve
            one API response at a time, potentially with additional information.
            </summary>
            <typeparam name="TResponse">The API response type. Each response contains a page of resources.</typeparam>
            <typeparam name="TResource">The resource type contained within the response.</typeparam>
        </member>
        <member name="M:Google.Api.Gax.PagedAsyncEnumerable`2.AsRawResponses">
            <summary>
            Returns the sequence of raw API responses, each of which contributes a page of
            resources to this sequence.
            </summary>
            <returns>An asynchronous sequence of raw API responses, each containing a page of resources.</returns>
        </member>
        <member name="M:Google.Api.Gax.PagedAsyncEnumerable`2.ReadPageAsync(System.Int32,System.Threading.CancellationToken)">
            <summary>
            Eagerly (but asynchronously) reads a single page of results with a fixed maximum size. The returned page is guaranteed
            to have that many results, unless there is no more data available.
            </summary>
            <remarks>
            "Natural" pages returned by the API may contain a smaller number of resources than requested.
            For example, a request for a page with 100 resources may return a page with 80 resources but
            a next page token for more to be retrieved. This is suitable for batch-processing, but not
            for user-visible paging such as in a web application, where fixed-size pages are expected.
            This method may make more than one API call in order to fill the page, but after the page has been
            returned, all the data will have been loaded. (In particular, iterating over the items in the page
            multiple times will not make any further requests.)
            </remarks>
            <param name="pageSize">The page size. Must be greater than 0.</param>
            <param name="cancellationToken">A token to cancel the operation.</param>
            <returns>An asynchronous operation, the result of which is a page of resources.</returns>
        </member>
        <member name="M:Google.Api.Gax.PagedAsyncEnumerable`2.GetAsyncEnumerator(System.Threading.CancellationToken)">
            <inheritdoc />
        </member>
        <member name="T:Google.Api.Gax.PagedEnumerable`2">
            <summary>
            A sequence of resources obtained via API responses, each of which contributes a page of resources.
            Application code can treat this as a simple sequence (with API calls automatically being made
            lazily as more results are required), or call <see cref="M:Google.Api.Gax.PagedEnumerable`2.AsRawResponses"/> to retrieve
            a page at a time, potentially with additional information.
            </summary>
            <typeparam name="TResponse">The API response type. Each response contains a page of resources.</typeparam>
            <typeparam name="TResource">The resource type contained within the response.</typeparam>
        </member>
        <member name="M:Google.Api.Gax.PagedEnumerable`2.AsRawResponses">
            <summary>
            Returns the sequence of raw API responses, each of which contributes a page of
            resources to this sequence.
            </summary>
            <returns>A sequence of raw API responses, each containing a page of resources.</returns>
        </member>
        <member name="M:Google.Api.Gax.PagedEnumerable`2.ReadPage(System.Int32)">
            <summary>
            Eagerly reads a single page of results with a fixed maximum size. The returned page is guaranteed
            to have that many results, unless there is no more data available.
            </summary>
            <remarks>
            "Natural" pages returned by the API may contain a smaller number of resources than requested.
            For example, a request for a page with 100 resources may return a page with 80 resources but
            a next page token for more to be retrieved. This is suitable for batch-processing, but not
            for user-visible paging such as in a web application, where fixed-size pages are expected.
            This method may make more than one API call in order to fill the page, but after the page has been
            returned, all the data will have been loaded. (In particular, iterating over the items in the page
            multiple times will not make any further requests.)
            </remarks>
            <param name="pageSize">The page size. Must be greater than 0.</param>
            <returns>An asynchronous operation, the result of which is a page of resources.</returns>
        </member>
        <member name="M:Google.Api.Gax.PagedEnumerable`2.GetEnumerator">
            <inheritdoc />
        </member>
        <member name="M:Google.Api.Gax.PagedEnumerable`2.System#Collections#IEnumerable#GetEnumerator">
            <inheritdoc />
        </member>
        <member name="T:Google.Api.Gax.PathTemplate">
            <summary>
            Represents a path template used for resource names which may be composed of multiple IDs.
            </summary>
            <remarks>
            <para>
            Templates use a subset of the syntax of the API platform. See
            https://github.com/googleapis/googleapis/blob/master/google/api/http.proto
            for details of the API platform.
            </para>
            <para>
            This class performs no URL escaping or unescaping. It is designed for use within GRPC, where no
            URL encoding is required.
            </para>
            </remarks>
        </member>
        <member name="F:Google.Api.Gax.PathTemplate.s_slashSplit">
            <summary>
            Just an array containing a single slash, to avoid constructing a new array every time we need
            to split.
            </summary>
        </member>
        <member name="F:Google.Api.Gax.PathTemplate._segments">
            <summary>
            List of segments in this template. Never modified after construction.
            </summary>
        </member>
        <member name="F:Google.Api.Gax.PathTemplate._parameterSegments">
            <summary>
            List of the segments in this template which are wildcards. Never modified after construction.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.PathTemplate.ParameterNames">
            <summary>
            The names of the parameters within the template. This collection has one element per parameter,
            but unnamed parameters have a name of <c>null</c>.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.PathTemplate.#ctor(System.String)">
            <summary>
            Constructs a template from its textual representation, such as <c>shelves/*/books/**</c>.
            </summary>
            <param name="template">The textual representation of the template. Must not be null.</param>
        </member>
        <member name="P:Google.Api.Gax.PathTemplate.ParameterCount">
            <summary>
            The number of parameter segments (regular wildcards or path wildcards, named or unnamed) in the template.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.PathTemplate.ValidateServiceName(System.String,System.String)">
            <summary>
            Validates a service name, ensuring it is not empty and doesn't contain any slashes.
            (In the future, we may want to make this stricter, e.g. that it's a valid DNS-like name.)
            </summary>
            <param name="serviceName">The name to validate</param>
            <param name="parameterName">The name of the parameter</param>
        </member>
        <member name="M:Google.Api.Gax.PathTemplate.ValidateResourceId(System.Int32,System.String)">
            <summary>
            Validate a single value from a sequence. This is used in both parsing and instantiating.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.PathTemplate.ValidateResourceIds(System.String[])">
            <summary>
            Validates a whole array of resource IDs, including that the count matches.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.PathTemplate.Expand(System.String[])">
            <summary>
            Validates that the given resource IDs are valid for this template, and returns a string representation
            </summary>
            <remarks>
            <para>
            This is equivalent to calling <c>new ResourceName(template, resourceIds).ToString()</c>, but simpler in
            calling code and more efficient in terms of memory allocation.
            </para>
            <para>
            This method assumes no service name is required. Call <see cref="M:Google.Api.Gax.PathTemplate.ExpandWithService(System.String,System.String[])"/> to specify a service name.
            </para>
            </remarks>
            <param name="resourceIds">The resource IDs to use to populate the parameters in this template. Must not be null.</param>
            <returns>The string representation of the resource name.</returns>
        </member>
        <member name="M:Google.Api.Gax.PathTemplate.ExpandWithService(System.String,System.String[])">
            <summary>
            Validates that the given resource IDs are valid for this template, and returns a string representation
            </summary>
            <remarks>
            </remarks>
            <param name="resourceIds">The resource IDs to use to populate the parameters in this template. Must not be null.</param>
            <param name="serviceName">The service name, which may be null.</param>
            <returns>The string representation of the resource name.</returns>
        </member>
        <member name="M:Google.Api.Gax.PathTemplate.ReplaceParameters(System.String,System.String[])">
            <summary>
            Returns a string representation of the template with parameters replaced by resource IDs.
            </summary>
            <param name="serviceName">The name of the service, for full resource names. May be null, to produce a relative resource name.</param>
            <param name="resourceIds">Resource IDs to interpolate the template with. Expected to have been validated already.</param>
        </member>
        <member name="M:Google.Api.Gax.PathTemplate.TryParseName(System.String,Google.Api.Gax.TemplatedResourceName@)">
            <summary>
            Attempts to parse the given resource name against this template, returning <c>null</c> on failure.
            </summary>
            <remarks>
            Although this method returns <c>null</c> if a name is passed in which doesn't match the template,
            it still throws <see cref="T:System.ArgumentNullException"/> if <paramref name="name"/> is null, as this would
            usually indicate a programming error rather than a data error.
            </remarks>
            <param name="name">The resource name to parse against this template. Must not be null.</param>
            <param name="result">When this method returns, the parsed resource name or <c>null</c> if parsing fails.</param>
            <returns><c>true</c> if the name was parsed successfully; <c>false</c> otherwise.</returns>
        </member>
        <member name="M:Google.Api.Gax.PathTemplate.ParseName(System.String)">
            <summary>
            Attempts to parse the given resource name against this template, throwing <see cref="T:System.ArgumentException" /> on failure.
            </summary>
            <param name="name">The resource name to parse against this template. Must not be null.</param>
            <returns>The parsed name as a <see cref="T:Google.Api.Gax.TemplatedResourceName"/>.</returns>
        </member>
        <member name="M:Google.Api.Gax.PathTemplate.TryParseNameInternal(System.String,Google.Api.Gax.TemplatedResourceName@)">
            <summary>
            Implementation of parsing, returning the error message for a FormatException if parsing fails.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.PathTemplate.ToString">
            <summary>
            Returns the textual representation of this template.
            </summary>
            <returns>The same textual representation that this template was initially constructed with.</returns>
        </member>
        <member name="F:Google.Api.Gax.PathTemplate.SegmentKind.Literal">
            <summary>
            A literal path segment.
            </summary>
        </member>
        <member name="F:Google.Api.Gax.PathTemplate.SegmentKind.Wildcard">
            <summary>
            A simple wildcard ('*').
            </summary>
        </member>
        <member name="F:Google.Api.Gax.PathTemplate.SegmentKind.PathWildcard">
            <summary>
            A path wildcard ('**').
            </summary>
        </member>
        <member name="T:Google.Api.Gax.PathTemplate.Segment">
            <summary>
            A segment of a path.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.PathTemplate.Segment.Value">
            <summary>
            The literal value or the name of a wildcard.
            null for unnamed wildcards.
            </summary>
        </member>
        <member name="T:Google.Api.Gax.Platform">
            <summary>
            Information about the current execution platform.
            Supported execution platforms are Google App Engine (GAE), Google Container Engine (GKE), and Google Compute Engine (GCE).
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Platform.InstanceAsync">
            <summary>
            Asyncrhonously get execution platform information.
            </summary>
            <returns>A task containing the execution platform information.</returns>
        </member>
        <member name="M:Google.Api.Gax.Platform.Instance">
            <summary>
            Get execution platform information. This may block briefly while network operations are in progress.
            </summary>
            <returns>Execution platform information.</returns>
        </member>
        <member name="M:Google.Api.Gax.Platform.GetEffectiveMetadataHost">
            <summary>
            Determine the metadata host to use. In order of priority, this will use:
            - GCE_METADATA_HOST environment variable, if set and non-empty
            - METADATA_EMULATOR_HOST environment variable, if set and non-empty.
              This is the undocumented but the de-facto mechanism for using an emulator.
            - The hard-coded IP address of "***************". We use the IP address rather
              than the IP name to avoid a DNS lookup, which can cause intermittent failures.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Platform.#ctor">
            <summary>
            Construct with no details.
            This leads to a platform <see cref="P:Google.Api.Gax.Platform.Type"/> of <see cref="F:Google.Api.Gax.PlatformType.Unknown"/>.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Platform.#ctor(Google.Api.Gax.GcePlatformDetails)">
            <summary>
            Construct with details of Google Compute Engine.
            </summary>
            <param name="gceDetails">Details of Google Compute Engine.</param>
        </member>
        <member name="M:Google.Api.Gax.Platform.#ctor(Google.Api.Gax.GaePlatformDetails)">
            <summary>
            Construct with details of Google App Engine.
            </summary>
            <param name="gaeDetails">Details of Google App Engine.</param>
        </member>
        <member name="M:Google.Api.Gax.Platform.#ctor(Google.Api.Gax.GkePlatformDetails)">
            <summary>
            Construct with details of Google Container (Kubernetes) Engine.
            </summary>
            <param name="gkeDetails">Details of Google Container (Kubernetes) Engine.</param>
        </member>
        <member name="M:Google.Api.Gax.Platform.#ctor(Google.Api.Gax.CloudRunPlatformDetails)">
            <summary>
            Construct with details of Google Cloud Run.
            </summary>
            <param name="cloudRunDetails">Details of Google Cloud Run.</param>
        </member>
        <member name="P:Google.Api.Gax.Platform.GaeDetails">
            <summary>
            Google App Engine (GAE) platform details.
            <c>null</c> if not executing on GAE.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.Platform.GceDetails">
            <summary>
            Google Compute Engine (GCE) platform details.
            <c>null</c> if not executing on GCE. 
            </summary>
        </member>
        <member name="P:Google.Api.Gax.Platform.GkeDetails">
            <summary>
            Google Container (Kubernetes) Engine (GKE) platform details.
            <c>null</c> if not executing on GKE. 
            </summary>
        </member>
        <member name="P:Google.Api.Gax.Platform.CloudRunDetails">
            <summary>
            Google Cloud Run platform details.
            <c>null</c> if not executing on Google Cloud Run. 
            </summary>
        </member>
        <member name="P:Google.Api.Gax.Platform.Type">
            <summary>
            The current execution platform.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.Platform.ProjectId">
            <summary>
            The current Project ID.
            <c>null</c> if the Project ID cannot be determined on the current execution platform.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Platform.ToString">
            <inheritdoc/>
        </member>
        <member name="T:Google.Api.Gax.PlatformType">
            <summary>
            Execution platform type.
            </summary>
        </member>
        <member name="F:Google.Api.Gax.PlatformType.Unknown">
            <summary>
            Unknown execution platform.
            </summary>
        </member>
        <member name="F:Google.Api.Gax.PlatformType.Gce">
            <summary>
            Execution platform is Google Compute Engine.
            </summary>
        </member>
        <member name="F:Google.Api.Gax.PlatformType.Gae">
            <summary>
            Execution platform is Google App Engine.
            </summary>
        </member>
        <member name="F:Google.Api.Gax.PlatformType.Gke">
            <summary>
            Execution platform is Google Container Engine (Kubernetes).
            </summary>
        </member>
        <member name="F:Google.Api.Gax.PlatformType.CloudRun">
            <summary>
            Execution platform is Google Cloud Run.
            </summary>
        </member>
        <member name="T:Google.Api.Gax.Polling">
            <summary>
            Helper methods for polling scenarios.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.Polling.PollRepeatedly``1(System.Func{System.Nullable{System.DateTime},``0},System.Predicate{``0},Google.Api.Gax.IClock,Google.Api.Gax.IScheduler,Google.Api.Gax.PollSettings,System.Threading.CancellationToken)">
            <summary>
            Repeatedly calls the specified polling action, delaying between calls,
            until a given condition is met in the response.
            </summary>
            <typeparam name="TResponse">The response type.</typeparam>
            <param name="pollAction">The poll action, typically performing an RPC. The value passed to the
            action is the overall deadline, so that the RPC settings can be adjusted accordingly. A null value
            indicates no deadline.</param>
            <param name="completionPredicate">The test for whether to return the response (<c>true</c>) or continue
            polling (<c>false</c>). Must not be null.</param>
            <param name="clock">The clock to use for determining deadlines. Must not be null.</param>
            <param name="scheduler">The scheduler to use for delaying between calls. Must not be null.</param>
            <param name="pollSettings">The poll settings, controlling timeouts, call settings and delays.</param>
            <param name="cancellationToken">The cancellation token used to cancel delays, if any.</param>
            <returns>The completed response.</returns>
            <exception cref="T:System.TimeoutException">The timeout specified in the poll settings expired.</exception>
        </member>
        <member name="M:Google.Api.Gax.Polling.PollRepeatedlyAsync``1(System.Func{System.Nullable{System.DateTime},System.Threading.Tasks.Task{``0}},System.Predicate{``0},Google.Api.Gax.IClock,Google.Api.Gax.IScheduler,Google.Api.Gax.PollSettings,System.Threading.CancellationToken)">
            <summary>
            Asynchronously repeatedly calls the specified polling action, delaying between calls,
            until a given condition is met in the response.
            </summary>
            <typeparam name="TResponse">The response type.</typeparam>
            <param name="pollAction">The poll action, typically performing an RPC. The value passed to the
            action is the overall deadline, so that the RPC settings can be adjusted accordingly. A null
            value indicates no deadline.</param>
            <param name="completionPredicate">The test for whether to return the response (<c>true</c>) or continue
            polling (<c>false</c>). Must not be null.</param>
            <param name="clock">The clock to use for determining deadlines. Must not be null.</param>
            <param name="scheduler">The scheduler to use for delaying between calls. Must not be null.</param>
            <param name="pollSettings">The poll settings, controlling timeouts, call settings and delays.</param>
            <param name="cancellationToken">The cancellation token used to cancel delays, if any.</param>
            <returns>A task representing the asynchronous operation. The result of the task will be the completed response.</returns>
        </member>
        <member name="T:Google.Api.Gax.PollSettings">
            <summary>
            Settings controlling repeated polling, for example when waiting for a long-running operation
            to complete.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.PollSettings.Expiration">
            <summary>
            How long to wait before giving up. This is never null.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.PollSettings.Delay">
            <summary>
            The delay between RPC calls when fetching the operation status. This is never negative.
            There is no exponential backoff between calls; the same delay is used for each call.
            </summary>
            <remarks>
            This is the delay between the a successful RPC response being received
            and the next RPC request being sent.
            </remarks>
        </member>
        <member name="P:Google.Api.Gax.PollSettings.DelayMultiplier">
            <summary>
            The multiplier to apply to the delay on each iteration; must be greater or equal to 1.0.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.PollSettings.MaxDelay">
            <summary>
            The maximum delay to use. If the increasing delay due to the delay multiplier exceeds this,
            this maximum is used instead.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.PollSettings.#ctor(Google.Api.Gax.Expiration,System.TimeSpan)">
            <summary>
            Creates poll settings from the given expiration and constant delay.
            </summary>
            <param name="expiration">The expiration to use in order to know when to stop polling. Must not be null.</param>
            <param name="delay">The constant delay between RPC calls. Must be non-negative.</param>
        </member>
        <member name="M:Google.Api.Gax.PollSettings.#ctor(Google.Api.Gax.Expiration,System.TimeSpan,System.Double,System.TimeSpan)">
            <summary>
            Creates poll settings from the given expiration, delay, delay multiplier and maximum delay.
            </summary>
            <param name="expiration">The expiration to use in order to know when to stop polling. Must not be null.</param>
            <param name="delay">The delay between RPC calls. Must be non-negative.</param>
            <param name="delayMultiplier">The multiplier to apply to the delay on each iteration; must be greater or equal to 1.0.</param>
            <param name="maxDelay">The maximum delay to use.</param>
        </member>
        <member name="M:Google.Api.Gax.PollSettings.NextDelay(System.TimeSpan)">
            <summary>
            Works out the next delay from the current one, based on the multiplier and maximum.
            </summary>
            <param name="currentDelay">The current delay.</param>
            <returns>The next delay.</returns>
        </member>
        <member name="T:Google.Api.Gax.ResourceMismatchException">
            <summary>
            Exception used to indicate that an attempt was made to get or create a resource,
            and the retrieved resource did not match the expected constraints.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.ResourceMismatchException.#ctor(System.String)">
            <summary>
            Constructs a new instance of the exception.
            </summary>
            <param name="message">The error message for the exception.</param>
        </member>
        <member name="T:Google.Api.Gax.ResourceNameList`1">
            <summary>
            A list of resource names of a specific type, that delegates all operations to an
            underlying list of string-based resource names.
            </summary>
            <typeparam name="TName">The type of the resource name contained in this list.</typeparam>
        </member>
        <member name="M:Google.Api.Gax.ResourceNameList`1.#ctor(System.Collections.Generic.IList{System.String},System.Func{System.String,`0})">
            <summary>
            Constructs a <see cref="T:Google.Api.Gax.ResourceNameList`1"/> from an underlying string-based list
            and a resource name parser.
            </summary>
            <param name="underlyingList"></param>
            <param name="stringToName"></param>
        </member>
        <member name="P:Google.Api.Gax.ResourceNameList`1.Count">
            <inheritdoc />
        </member>
        <member name="P:Google.Api.Gax.ResourceNameList`1.IsReadOnly">
            <inheritdoc />
        </member>
        <member name="P:Google.Api.Gax.ResourceNameList`1.Item(System.Int32)">
            <inheritdoc />
        </member>
        <member name="M:Google.Api.Gax.ResourceNameList`1.Add(`0)">
            <inheritdoc />
        </member>
        <member name="M:Google.Api.Gax.ResourceNameList`1.Add(System.Collections.Generic.IEnumerable{`0})">
            <summary>
            Adds all items to this list.
            </summary>
            <param name="items">The items to add to this list.</param>
        </member>
        <member name="M:Google.Api.Gax.ResourceNameList`1.Clear">
            <inheritdoc />
        </member>
        <member name="M:Google.Api.Gax.ResourceNameList`1.Contains(`0)">
            <inheritdoc />
        </member>
        <member name="M:Google.Api.Gax.ResourceNameList`1.CopyTo(`0[],System.Int32)">
            <inheritdoc />
        </member>
        <member name="M:Google.Api.Gax.ResourceNameList`1.GetEnumerator">
            <inheritdoc />
        </member>
        <member name="M:Google.Api.Gax.ResourceNameList`1.System#Collections#IEnumerable#GetEnumerator">
            <inheritdoc />
        </member>
        <member name="M:Google.Api.Gax.ResourceNameList`1.IndexOf(`0)">
            <inheritdoc />
        </member>
        <member name="M:Google.Api.Gax.ResourceNameList`1.Insert(System.Int32,`0)">
            <inheritdoc />
        </member>
        <member name="M:Google.Api.Gax.ResourceNameList`1.Remove(`0)">
            <inheritdoc />
        </member>
        <member name="M:Google.Api.Gax.ResourceNameList`1.RemoveAt(System.Int32)">
            <inheritdoc />
        </member>
        <member name="T:Google.Api.Gax.ResourceNames.BillingAccountName">
            <summary>
            Resource name for the 'billing account' resource which is widespread across Google Cloud Platform.
            While most resource names are generated on a per-API basis, many APIs use a billing account resource, and it's
            useful to be able to pass values from one API to another.
            </summary>
        </member>
        <member name="T:Google.Api.Gax.ResourceNames.BillingAccountName.ResourceNameType">
            <summary>The possible contents of <see cref="T:Google.Api.Gax.ResourceNames.BillingAccountName"/>.</summary>
        </member>
        <member name="F:Google.Api.Gax.ResourceNames.BillingAccountName.ResourceNameType.Unparsed">
            <summary>An unparsed resource name.</summary>
        </member>
        <member name="F:Google.Api.Gax.ResourceNames.BillingAccountName.ResourceNameType.BillingAccount">
            <summary>A resource name with pattern <c>billingAccounts/{billing_account}</c>.</summary>
        </member>
        <member name="M:Google.Api.Gax.ResourceNames.BillingAccountName.FromUnparsed(Google.Api.Gax.UnparsedResourceName)">
            <summary>Creates a <see cref="T:Google.Api.Gax.ResourceNames.BillingAccountName"/> containing an unparsed resource name.</summary>
            <param name="unparsedResourceName">The unparsed resource name. Must not be <c>null</c>.</param>
            <returns>
            A new instance of <see cref="T:Google.Api.Gax.ResourceNames.BillingAccountName"/> containing the provided
            <paramref name="unparsedResourceName"/>.
            </returns>
        </member>
        <member name="M:Google.Api.Gax.ResourceNames.BillingAccountName.FromBillingAccount(System.String)">
            <summary>
            Creates a <see cref="T:Google.Api.Gax.ResourceNames.BillingAccountName"/> with the pattern <c>billingAccounts/{billing_account}</c>.
            </summary>
            <param name="billingAccountId">The <c>BillingAccount</c> ID. Must not be <c>null</c> or empty.</param>
            <returns>A new instance of <see cref="T:Google.Api.Gax.ResourceNames.BillingAccountName"/> constructed from the provided ids.</returns>
        </member>
        <member name="M:Google.Api.Gax.ResourceNames.BillingAccountName.Format(System.String)">
            <summary>
            Formats the IDs into the string representation of this <see cref="T:Google.Api.Gax.ResourceNames.BillingAccountName"/> with pattern
            <c>billingAccounts/{billing_account}</c>.
            </summary>
            <param name="billingAccountId">The <c>BillingAccount</c> ID. Must not be <c>null</c> or empty.</param>
            <returns>
            The string representation of this <see cref="T:Google.Api.Gax.ResourceNames.BillingAccountName"/> with pattern
            <c>billingAccounts/{billing_account}</c>.
            </returns>
        </member>
        <member name="M:Google.Api.Gax.ResourceNames.BillingAccountName.FormatBillingAccount(System.String)">
            <summary>
            Formats the IDs into the string representation of this <see cref="T:Google.Api.Gax.ResourceNames.BillingAccountName"/> with pattern
            <c>billingAccounts/{billing_account}</c>.
            </summary>
            <param name="billingAccountId">The <c>BillingAccount</c> ID. Must not be <c>null</c> or empty.</param>
            <returns>
            The string representation of this <see cref="T:Google.Api.Gax.ResourceNames.BillingAccountName"/> with pattern
            <c>billingAccounts/{billing_account}</c>.
            </returns>
        </member>
        <member name="M:Google.Api.Gax.ResourceNames.BillingAccountName.Parse(System.String)">
            <summary>
            Parses the given resource name string into a new <see cref="T:Google.Api.Gax.ResourceNames.BillingAccountName"/> instance.
            </summary>
            <remarks>
            To parse successfully, the resource name must be formatted as one of the following:
            <list type="bullet"><item><description><c>billingAccounts/{billing_account}</c></description></item></list>
            </remarks>
            <param name="billingAccountName">The resource name in string form. Must not be <c>null</c>.</param>
            <returns>The parsed <see cref="T:Google.Api.Gax.ResourceNames.BillingAccountName"/> if successful.</returns>
        </member>
        <member name="M:Google.Api.Gax.ResourceNames.BillingAccountName.Parse(System.String,System.Boolean)">
            <summary>
            Parses the given resource name string into a new <see cref="T:Google.Api.Gax.ResourceNames.BillingAccountName"/> instance; optionally
            allowing an unparseable resource name.
            </summary>
            <remarks>
            To parse successfully, the resource name must be formatted as one of the following:
            <list type="bullet"><item><description><c>billingAccounts/{billing_account}</c></description></item></list>
            Or may be in any format if <paramref name="allowUnparsed"/> is <c>true</c>.
            </remarks>
            <param name="billingAccountName">The resource name in string form. Must not be <c>null</c>.</param>
            <param name="allowUnparsed">
            If <c>true</c> will successfully store an unparseable resource name into the <see cref="P:Google.Api.Gax.ResourceNames.BillingAccountName.UnparsedResource"/>
            property; otherwise will throw an <see cref="T:System.ArgumentException"/> if an unparseable resource name is
            specified.
            </param>
            <returns>The parsed <see cref="T:Google.Api.Gax.ResourceNames.BillingAccountName"/> if successful.</returns>
        </member>
        <member name="M:Google.Api.Gax.ResourceNames.BillingAccountName.TryParse(System.String,Google.Api.Gax.ResourceNames.BillingAccountName@)">
            <summary>
            Tries to parse the given resource name string into a new <see cref="T:Google.Api.Gax.ResourceNames.BillingAccountName"/> instance.
            </summary>
            <remarks>
            To parse successfully, the resource name must be formatted as one of the following:
            <list type="bullet"><item><description><c>billingAccounts/{billing_account}</c></description></item></list>
            </remarks>
            <param name="billingAccountName">The resource name in string form. Must not be <c>null</c>.</param>
            <param name="result">
            When this method returns, the parsed <see cref="T:Google.Api.Gax.ResourceNames.BillingAccountName"/>, or <c>null</c> if parsing failed.
            </param>
            <returns><c>true</c> if the name was parsed successfully; <c>false</c> otherwise.</returns>
        </member>
        <member name="M:Google.Api.Gax.ResourceNames.BillingAccountName.TryParse(System.String,System.Boolean,Google.Api.Gax.ResourceNames.BillingAccountName@)">
            <summary>
            Tries to parse the given resource name string into a new <see cref="T:Google.Api.Gax.ResourceNames.BillingAccountName"/> instance;
            optionally allowing an unparseable resource name.
            </summary>
            <remarks>
            To parse successfully, the resource name must be formatted as one of the following:
            <list type="bullet"><item><description><c>billingAccounts/{billing_account}</c></description></item></list>
            Or may be in any format if <paramref name="allowUnparsed"/> is <c>true</c>.
            </remarks>
            <param name="billingAccountName">The resource name in string form. Must not be <c>null</c>.</param>
            <param name="allowUnparsed">
            If <c>true</c> will successfully store an unparseable resource name into the <see cref="P:Google.Api.Gax.ResourceNames.BillingAccountName.UnparsedResource"/>
            property; otherwise will throw an <see cref="T:System.ArgumentException"/> if an unparseable resource name is
            specified.
            </param>
            <param name="result">
            When this method returns, the parsed <see cref="T:Google.Api.Gax.ResourceNames.BillingAccountName"/>, or <c>null</c> if parsing failed.
            </param>
            <returns><c>true</c> if the name was parsed successfully; <c>false</c> otherwise.</returns>
        </member>
        <member name="M:Google.Api.Gax.ResourceNames.BillingAccountName.#ctor(System.String)">
            <summary>
            Constructs a new instance of a <see cref="T:Google.Api.Gax.ResourceNames.BillingAccountName"/> class from the component parts of pattern
            <c>billingAccounts/{billing_account}</c>
            </summary>
            <param name="billingAccountId">The <c>BillingAccount</c> ID. Must not be <c>null</c> or empty.</param>
        </member>
        <member name="P:Google.Api.Gax.ResourceNames.BillingAccountName.Type">
            <summary>The <see cref="T:Google.Api.Gax.ResourceNames.BillingAccountName.ResourceNameType"/> of the contained resource name.</summary>
        </member>
        <member name="P:Google.Api.Gax.ResourceNames.BillingAccountName.UnparsedResource">
            <summary>
            The contained <see cref="T:Google.Api.Gax.UnparsedResourceName"/>. Only non-<c>null</c>if this instance contains an
            unparsed resource name.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.ResourceNames.BillingAccountName.BillingAccountId">
            <summary>
            The <c>BillingAccount</c> ID. Will not be <c>null</c>, unless this instance contains an unparsed resource
            name.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.ResourceNames.BillingAccountName.IsKnownPattern">
            <inheritdoc/>
        </member>
        <member name="M:Google.Api.Gax.ResourceNames.BillingAccountName.ToString">
            <inheritdoc/>
        </member>
        <member name="M:Google.Api.Gax.ResourceNames.BillingAccountName.GetHashCode">
            <inheritdoc/>
        </member>
        <member name="M:Google.Api.Gax.ResourceNames.BillingAccountName.Equals(System.Object)">
            <inheritdoc/>
        </member>
        <member name="M:Google.Api.Gax.ResourceNames.BillingAccountName.Equals(Google.Api.Gax.ResourceNames.BillingAccountName)">
            <inheritdoc/>
        </member>
        <member name="M:Google.Api.Gax.ResourceNames.BillingAccountName.op_Equality(Google.Api.Gax.ResourceNames.BillingAccountName,Google.Api.Gax.ResourceNames.BillingAccountName)">
            <inheritdoc/>
        </member>
        <member name="M:Google.Api.Gax.ResourceNames.BillingAccountName.op_Inequality(Google.Api.Gax.ResourceNames.BillingAccountName,Google.Api.Gax.ResourceNames.BillingAccountName)">
            <inheritdoc/>
        </member>
        <member name="T:Google.Api.Gax.ResourceNames.FolderName">
            <summary>
            Resource name for the 'folder' resource which is widespread across Google Cloud Platform.
            While most resource names are generated on a per-API basis, many APIs use a folder resource, and it's
            useful to be able to pass values from one API to another.
            </summary>
        </member>
        <member name="T:Google.Api.Gax.ResourceNames.FolderName.ResourceNameType">
            <summary>The possible contents of <see cref="T:Google.Api.Gax.ResourceNames.FolderName"/>.</summary>
        </member>
        <member name="F:Google.Api.Gax.ResourceNames.FolderName.ResourceNameType.Unparsed">
            <summary>An unparsed resource name.</summary>
        </member>
        <member name="F:Google.Api.Gax.ResourceNames.FolderName.ResourceNameType.Folder">
            <summary>A resource name with pattern <c>folders/{folder}</c>.</summary>
        </member>
        <member name="M:Google.Api.Gax.ResourceNames.FolderName.FromUnparsed(Google.Api.Gax.UnparsedResourceName)">
            <summary>Creates a <see cref="T:Google.Api.Gax.ResourceNames.FolderName"/> containing an unparsed resource name.</summary>
            <param name="unparsedResourceName">The unparsed resource name. Must not be <c>null</c>.</param>
            <returns>
            A new instance of <see cref="T:Google.Api.Gax.ResourceNames.FolderName"/> containing the provided <paramref name="unparsedResourceName"/>.
            </returns>
        </member>
        <member name="M:Google.Api.Gax.ResourceNames.FolderName.FromFolder(System.String)">
            <summary>Creates a <see cref="T:Google.Api.Gax.ResourceNames.FolderName"/> with the pattern <c>folders/{folder}</c>.</summary>
            <param name="folderId">The <c>Folder</c> ID. Must not be <c>null</c> or empty.</param>
            <returns>A new instance of <see cref="T:Google.Api.Gax.ResourceNames.FolderName"/> constructed from the provided ids.</returns>
        </member>
        <member name="M:Google.Api.Gax.ResourceNames.FolderName.Format(System.String)">
            <summary>
            Formats the IDs into the string representation of this <see cref="T:Google.Api.Gax.ResourceNames.FolderName"/> with pattern
            <c>folders/{folder}</c>.
            </summary>
            <param name="folderId">The <c>Folder</c> ID. Must not be <c>null</c> or empty.</param>
            <returns>
            The string representation of this <see cref="T:Google.Api.Gax.ResourceNames.FolderName"/> with pattern <c>folders/{folder}</c>.
            </returns>
        </member>
        <member name="M:Google.Api.Gax.ResourceNames.FolderName.FormatFolder(System.String)">
            <summary>
            Formats the IDs into the string representation of this <see cref="T:Google.Api.Gax.ResourceNames.FolderName"/> with pattern
            <c>folders/{folder}</c>.
            </summary>
            <param name="folderId">The <c>Folder</c> ID. Must not be <c>null</c> or empty.</param>
            <returns>
            The string representation of this <see cref="T:Google.Api.Gax.ResourceNames.FolderName"/> with pattern <c>folders/{folder}</c>.
            </returns>
        </member>
        <member name="M:Google.Api.Gax.ResourceNames.FolderName.Parse(System.String)">
            <summary>Parses the given resource name string into a new <see cref="T:Google.Api.Gax.ResourceNames.FolderName"/> instance.</summary>
            <remarks>
            To parse successfully, the resource name must be formatted as one of the following:
            <list type="bullet"><item><description><c>folders/{folder}</c></description></item></list>
            </remarks>
            <param name="folderName">The resource name in string form. Must not be <c>null</c>.</param>
            <returns>The parsed <see cref="T:Google.Api.Gax.ResourceNames.FolderName"/> if successful.</returns>
        </member>
        <member name="M:Google.Api.Gax.ResourceNames.FolderName.Parse(System.String,System.Boolean)">
            <summary>
            Parses the given resource name string into a new <see cref="T:Google.Api.Gax.ResourceNames.FolderName"/> instance; optionally allowing an
            unparseable resource name.
            </summary>
            <remarks>
            To parse successfully, the resource name must be formatted as one of the following:
            <list type="bullet"><item><description><c>folders/{folder}</c></description></item></list>
            Or may be in any format if <paramref name="allowUnparsed"/> is <c>true</c>.
            </remarks>
            <param name="folderName">The resource name in string form. Must not be <c>null</c>.</param>
            <param name="allowUnparsed">
            If <c>true</c> will successfully store an unparseable resource name into the <see cref="P:Google.Api.Gax.ResourceNames.FolderName.UnparsedResource"/>
            property; otherwise will throw an <see cref="T:System.ArgumentException"/> if an unparseable resource name is
            specified.
            </param>
            <returns>The parsed <see cref="T:Google.Api.Gax.ResourceNames.FolderName"/> if successful.</returns>
        </member>
        <member name="M:Google.Api.Gax.ResourceNames.FolderName.TryParse(System.String,Google.Api.Gax.ResourceNames.FolderName@)">
            <summary>
            Tries to parse the given resource name string into a new <see cref="T:Google.Api.Gax.ResourceNames.FolderName"/> instance.
            </summary>
            <remarks>
            To parse successfully, the resource name must be formatted as one of the following:
            <list type="bullet"><item><description><c>folders/{folder}</c></description></item></list>
            </remarks>
            <param name="folderName">The resource name in string form. Must not be <c>null</c>.</param>
            <param name="result">
            When this method returns, the parsed <see cref="T:Google.Api.Gax.ResourceNames.FolderName"/>, or <c>null</c> if parsing failed.
            </param>
            <returns><c>true</c> if the name was parsed successfully; <c>false</c> otherwise.</returns>
        </member>
        <member name="M:Google.Api.Gax.ResourceNames.FolderName.TryParse(System.String,System.Boolean,Google.Api.Gax.ResourceNames.FolderName@)">
            <summary>
            Tries to parse the given resource name string into a new <see cref="T:Google.Api.Gax.ResourceNames.FolderName"/> instance; optionally
            allowing an unparseable resource name.
            </summary>
            <remarks>
            To parse successfully, the resource name must be formatted as one of the following:
            <list type="bullet"><item><description><c>folders/{folder}</c></description></item></list>
            Or may be in any format if <paramref name="allowUnparsed"/> is <c>true</c>.
            </remarks>
            <param name="folderName">The resource name in string form. Must not be <c>null</c>.</param>
            <param name="allowUnparsed">
            If <c>true</c> will successfully store an unparseable resource name into the <see cref="P:Google.Api.Gax.ResourceNames.FolderName.UnparsedResource"/>
            property; otherwise will throw an <see cref="T:System.ArgumentException"/> if an unparseable resource name is
            specified.
            </param>
            <param name="result">
            When this method returns, the parsed <see cref="T:Google.Api.Gax.ResourceNames.FolderName"/>, or <c>null</c> if parsing failed.
            </param>
            <returns><c>true</c> if the name was parsed successfully; <c>false</c> otherwise.</returns>
        </member>
        <member name="M:Google.Api.Gax.ResourceNames.FolderName.#ctor(System.String)">
            <summary>
            Constructs a new instance of a <see cref="T:Google.Api.Gax.ResourceNames.FolderName"/> class from the component parts of pattern
            <c>folders/{folder}</c>
            </summary>
            <param name="folderId">The <c>Folder</c> ID. Must not be <c>null</c> or empty.</param>
        </member>
        <member name="P:Google.Api.Gax.ResourceNames.FolderName.Type">
            <summary>The <see cref="T:Google.Api.Gax.ResourceNames.FolderName.ResourceNameType"/> of the contained resource name.</summary>
        </member>
        <member name="P:Google.Api.Gax.ResourceNames.FolderName.UnparsedResource">
            <summary>
            The contained <see cref="T:Google.Api.Gax.UnparsedResourceName"/>. Only non-<c>null</c>if this instance contains an
            unparsed resource name.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.ResourceNames.FolderName.FolderId">
            <summary>
            The <c>Folder</c> ID. Will not be <c>null</c>, unless this instance contains an unparsed resource name.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.ResourceNames.FolderName.IsKnownPattern">
            <inheritdoc/>
        </member>
        <member name="M:Google.Api.Gax.ResourceNames.FolderName.ToString">
            <inheritdoc/>
        </member>
        <member name="M:Google.Api.Gax.ResourceNames.FolderName.GetHashCode">
            <inheritdoc/>
        </member>
        <member name="M:Google.Api.Gax.ResourceNames.FolderName.Equals(System.Object)">
            <inheritdoc/>
        </member>
        <member name="M:Google.Api.Gax.ResourceNames.FolderName.Equals(Google.Api.Gax.ResourceNames.FolderName)">
            <inheritdoc/>
        </member>
        <member name="M:Google.Api.Gax.ResourceNames.FolderName.op_Equality(Google.Api.Gax.ResourceNames.FolderName,Google.Api.Gax.ResourceNames.FolderName)">
            <inheritdoc/>
        </member>
        <member name="M:Google.Api.Gax.ResourceNames.FolderName.op_Inequality(Google.Api.Gax.ResourceNames.FolderName,Google.Api.Gax.ResourceNames.FolderName)">
            <inheritdoc/>
        </member>
        <member name="T:Google.Api.Gax.ResourceNames.LocationName">
            <summary>
            Resource name for the 'location' resource which is widespread across Google Cloud Platform.
            While most resource names are generated on a per-API basis, many APIs use a location resource, and it's
            useful to be able to pass values from one API to another.
            </summary>
        </member>
        <member name="T:Google.Api.Gax.ResourceNames.LocationName.ResourceNameType">
            <summary>The possible contents of <see cref="T:Google.Api.Gax.ResourceNames.LocationName"/>.</summary>
        </member>
        <member name="F:Google.Api.Gax.ResourceNames.LocationName.ResourceNameType.Unparsed">
            <summary>An unparsed resource name.</summary>
        </member>
        <member name="F:Google.Api.Gax.ResourceNames.LocationName.ResourceNameType.ProjectLocation">
            <summary>A resource name with pattern <c>projects/{project}/locations/{location}</c>.</summary>
        </member>
        <member name="M:Google.Api.Gax.ResourceNames.LocationName.FromUnparsed(Google.Api.Gax.UnparsedResourceName)">
            <summary>Creates a <see cref="T:Google.Api.Gax.ResourceNames.LocationName"/> containing an unparsed resource name.</summary>
            <param name="unparsedResourceName">The unparsed resource name. Must not be <c>null</c>.</param>
            <returns>
            A new instance of <see cref="T:Google.Api.Gax.ResourceNames.LocationName"/> containing the provided <paramref name="unparsedResourceName"/>
            .
            </returns>
        </member>
        <member name="M:Google.Api.Gax.ResourceNames.LocationName.FromProjectLocation(System.String,System.String)">
            <summary>
            Creates a <see cref="T:Google.Api.Gax.ResourceNames.LocationName"/> with the pattern <c>projects/{project}/locations/{location}</c>.
            </summary>
            <param name="projectId">The <c>Project</c> ID. Must not be <c>null</c> or empty.</param>
            <param name="locationId">The <c>Location</c> ID. Must not be <c>null</c> or empty.</param>
            <returns>A new instance of <see cref="T:Google.Api.Gax.ResourceNames.LocationName"/> constructed from the provided ids.</returns>
        </member>
        <member name="M:Google.Api.Gax.ResourceNames.LocationName.Format(System.String,System.String)">
            <summary>
            Formats the IDs into the string representation of this <see cref="T:Google.Api.Gax.ResourceNames.LocationName"/> with pattern
            <c>projects/{project}/locations/{location}</c>.
            </summary>
            <param name="projectId">The <c>Project</c> ID. Must not be <c>null</c> or empty.</param>
            <param name="locationId">The <c>Location</c> ID. Must not be <c>null</c> or empty.</param>
            <returns>
            The string representation of this <see cref="T:Google.Api.Gax.ResourceNames.LocationName"/> with pattern
            <c>projects/{project}/locations/{location}</c>.
            </returns>
        </member>
        <member name="M:Google.Api.Gax.ResourceNames.LocationName.FormatProjectLocation(System.String,System.String)">
            <summary>
            Formats the IDs into the string representation of this <see cref="T:Google.Api.Gax.ResourceNames.LocationName"/> with pattern
            <c>projects/{project}/locations/{location}</c>.
            </summary>
            <param name="projectId">The <c>Project</c> ID. Must not be <c>null</c> or empty.</param>
            <param name="locationId">The <c>Location</c> ID. Must not be <c>null</c> or empty.</param>
            <returns>
            The string representation of this <see cref="T:Google.Api.Gax.ResourceNames.LocationName"/> with pattern
            <c>projects/{project}/locations/{location}</c>.
            </returns>
        </member>
        <member name="M:Google.Api.Gax.ResourceNames.LocationName.Parse(System.String)">
            <summary>Parses the given resource name string into a new <see cref="T:Google.Api.Gax.ResourceNames.LocationName"/> instance.</summary>
            <remarks>
            To parse successfully, the resource name must be formatted as one of the following:
            <list type="bullet">
            <item><description><c>projects/{project}/locations/{location}</c></description></item>
            </list>
            </remarks>
            <param name="locationName">The resource name in string form. Must not be <c>null</c>.</param>
            <returns>The parsed <see cref="T:Google.Api.Gax.ResourceNames.LocationName"/> if successful.</returns>
        </member>
        <member name="M:Google.Api.Gax.ResourceNames.LocationName.Parse(System.String,System.Boolean)">
            <summary>
            Parses the given resource name string into a new <see cref="T:Google.Api.Gax.ResourceNames.LocationName"/> instance; optionally allowing an
            unparseable resource name.
            </summary>
            <remarks>
            To parse successfully, the resource name must be formatted as one of the following:
            <list type="bullet">
            <item><description><c>projects/{project}/locations/{location}</c></description></item>
            </list>
            Or may be in any format if <paramref name="allowUnparsed"/> is <c>true</c>.
            </remarks>
            <param name="locationName">The resource name in string form. Must not be <c>null</c>.</param>
            <param name="allowUnparsed">
            If <c>true</c> will successfully store an unparseable resource name into the <see cref="P:Google.Api.Gax.ResourceNames.LocationName.UnparsedResource"/>
            property; otherwise will throw an <see cref="T:System.ArgumentException"/> if an unparseable resource name is
            specified.
            </param>
            <returns>The parsed <see cref="T:Google.Api.Gax.ResourceNames.LocationName"/> if successful.</returns>
        </member>
        <member name="M:Google.Api.Gax.ResourceNames.LocationName.TryParse(System.String,Google.Api.Gax.ResourceNames.LocationName@)">
            <summary>
            Tries to parse the given resource name string into a new <see cref="T:Google.Api.Gax.ResourceNames.LocationName"/> instance.
            </summary>
            <remarks>
            To parse successfully, the resource name must be formatted as one of the following:
            <list type="bullet">
            <item><description><c>projects/{project}/locations/{location}</c></description></item>
            </list>
            </remarks>
            <param name="locationName">The resource name in string form. Must not be <c>null</c>.</param>
            <param name="result">
            When this method returns, the parsed <see cref="T:Google.Api.Gax.ResourceNames.LocationName"/>, or <c>null</c> if parsing failed.
            </param>
            <returns><c>true</c> if the name was parsed successfully; <c>false</c> otherwise.</returns>
        </member>
        <member name="M:Google.Api.Gax.ResourceNames.LocationName.TryParse(System.String,System.Boolean,Google.Api.Gax.ResourceNames.LocationName@)">
            <summary>
            Tries to parse the given resource name string into a new <see cref="T:Google.Api.Gax.ResourceNames.LocationName"/> instance; optionally
            allowing an unparseable resource name.
            </summary>
            <remarks>
            To parse successfully, the resource name must be formatted as one of the following:
            <list type="bullet">
            <item><description><c>projects/{project}/locations/{location}</c></description></item>
            </list>
            Or may be in any format if <paramref name="allowUnparsed"/> is <c>true</c>.
            </remarks>
            <param name="locationName">The resource name in string form. Must not be <c>null</c>.</param>
            <param name="allowUnparsed">
            If <c>true</c> will successfully store an unparseable resource name into the <see cref="P:Google.Api.Gax.ResourceNames.LocationName.UnparsedResource"/>
            property; otherwise will throw an <see cref="T:System.ArgumentException"/> if an unparseable resource name is
            specified.
            </param>
            <param name="result">
            When this method returns, the parsed <see cref="T:Google.Api.Gax.ResourceNames.LocationName"/>, or <c>null</c> if parsing failed.
            </param>
            <returns><c>true</c> if the name was parsed successfully; <c>false</c> otherwise.</returns>
        </member>
        <member name="M:Google.Api.Gax.ResourceNames.LocationName.#ctor(System.String,System.String)">
            <summary>
            Constructs a new instance of a <see cref="T:Google.Api.Gax.ResourceNames.LocationName"/> class from the component parts of pattern
            <c>projects/{project}/locations/{location}</c>
            </summary>
            <param name="projectId">The <c>Project</c> ID. Must not be <c>null</c> or empty.</param>
            <param name="locationId">The <c>Location</c> ID. Must not be <c>null</c> or empty.</param>
        </member>
        <member name="P:Google.Api.Gax.ResourceNames.LocationName.Type">
            <summary>The <see cref="T:Google.Api.Gax.ResourceNames.LocationName.ResourceNameType"/> of the contained resource name.</summary>
        </member>
        <member name="P:Google.Api.Gax.ResourceNames.LocationName.UnparsedResource">
            <summary>
            The contained <see cref="T:Google.Api.Gax.UnparsedResourceName"/>. Only non-<c>null</c>if this instance contains an
            unparsed resource name.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.ResourceNames.LocationName.LocationId">
            <summary>
            The <c>Location</c> ID. Will not be <c>null</c>, unless this instance contains an unparsed resource name.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.ResourceNames.LocationName.ProjectId">
            <summary>
            The <c>Project</c> ID. Will not be <c>null</c>, unless this instance contains an unparsed resource name.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.ResourceNames.LocationName.IsKnownPattern">
            <inheritdoc/>
        </member>
        <member name="M:Google.Api.Gax.ResourceNames.LocationName.ToString">
            <inheritdoc/>
        </member>
        <member name="M:Google.Api.Gax.ResourceNames.LocationName.GetHashCode">
            <inheritdoc/>
        </member>
        <member name="M:Google.Api.Gax.ResourceNames.LocationName.Equals(System.Object)">
            <inheritdoc/>
        </member>
        <member name="M:Google.Api.Gax.ResourceNames.LocationName.Equals(Google.Api.Gax.ResourceNames.LocationName)">
            <inheritdoc/>
        </member>
        <member name="M:Google.Api.Gax.ResourceNames.LocationName.op_Equality(Google.Api.Gax.ResourceNames.LocationName,Google.Api.Gax.ResourceNames.LocationName)">
            <inheritdoc/>
        </member>
        <member name="M:Google.Api.Gax.ResourceNames.LocationName.op_Inequality(Google.Api.Gax.ResourceNames.LocationName,Google.Api.Gax.ResourceNames.LocationName)">
            <inheritdoc/>
        </member>
        <member name="T:Google.Api.Gax.ResourceNames.OrganizationName">
            <summary>
            Resource name for the 'organization' resource which is widespread across Google Cloud Platform.
            While most resource names are generated on a per-API basis, many APIs use an organization resource, and it's
            useful to be able to pass values from one API to another.
            </summary>
        </member>
        <member name="T:Google.Api.Gax.ResourceNames.OrganizationName.ResourceNameType">
            <summary>The possible contents of <see cref="T:Google.Api.Gax.ResourceNames.OrganizationName"/>.</summary>
        </member>
        <member name="F:Google.Api.Gax.ResourceNames.OrganizationName.ResourceNameType.Unparsed">
            <summary>An unparsed resource name.</summary>
        </member>
        <member name="F:Google.Api.Gax.ResourceNames.OrganizationName.ResourceNameType.Organization">
            <summary>A resource name with pattern <c>organizations/{organization}</c>.</summary>
        </member>
        <member name="M:Google.Api.Gax.ResourceNames.OrganizationName.FromUnparsed(Google.Api.Gax.UnparsedResourceName)">
            <summary>Creates a <see cref="T:Google.Api.Gax.ResourceNames.OrganizationName"/> containing an unparsed resource name.</summary>
            <param name="unparsedResourceName">The unparsed resource name. Must not be <c>null</c>.</param>
            <returns>
            A new instance of <see cref="T:Google.Api.Gax.ResourceNames.OrganizationName"/> containing the provided
            <paramref name="unparsedResourceName"/>.
            </returns>
        </member>
        <member name="M:Google.Api.Gax.ResourceNames.OrganizationName.FromOrganization(System.String)">
            <summary>
            Creates a <see cref="T:Google.Api.Gax.ResourceNames.OrganizationName"/> with the pattern <c>organizations/{organization}</c>.
            </summary>
            <param name="organizationId">The <c>Organization</c> ID. Must not be <c>null</c> or empty.</param>
            <returns>A new instance of <see cref="T:Google.Api.Gax.ResourceNames.OrganizationName"/> constructed from the provided ids.</returns>
        </member>
        <member name="M:Google.Api.Gax.ResourceNames.OrganizationName.Format(System.String)">
            <summary>
            Formats the IDs into the string representation of this <see cref="T:Google.Api.Gax.ResourceNames.OrganizationName"/> with pattern
            <c>organizations/{organization}</c>.
            </summary>
            <param name="organizationId">The <c>Organization</c> ID. Must not be <c>null</c> or empty.</param>
            <returns>
            The string representation of this <see cref="T:Google.Api.Gax.ResourceNames.OrganizationName"/> with pattern
            <c>organizations/{organization}</c>.
            </returns>
        </member>
        <member name="M:Google.Api.Gax.ResourceNames.OrganizationName.FormatOrganization(System.String)">
            <summary>
            Formats the IDs into the string representation of this <see cref="T:Google.Api.Gax.ResourceNames.OrganizationName"/> with pattern
            <c>organizations/{organization}</c>.
            </summary>
            <param name="organizationId">The <c>Organization</c> ID. Must not be <c>null</c> or empty.</param>
            <returns>
            The string representation of this <see cref="T:Google.Api.Gax.ResourceNames.OrganizationName"/> with pattern
            <c>organizations/{organization}</c>.
            </returns>
        </member>
        <member name="M:Google.Api.Gax.ResourceNames.OrganizationName.Parse(System.String)">
            <summary>Parses the given resource name string into a new <see cref="T:Google.Api.Gax.ResourceNames.OrganizationName"/> instance.</summary>
            <remarks>
            To parse successfully, the resource name must be formatted as one of the following:
            <list type="bullet"><item><description><c>organizations/{organization}</c></description></item></list>
            </remarks>
            <param name="organizationName">The resource name in string form. Must not be <c>null</c>.</param>
            <returns>The parsed <see cref="T:Google.Api.Gax.ResourceNames.OrganizationName"/> if successful.</returns>
        </member>
        <member name="M:Google.Api.Gax.ResourceNames.OrganizationName.Parse(System.String,System.Boolean)">
            <summary>
            Parses the given resource name string into a new <see cref="T:Google.Api.Gax.ResourceNames.OrganizationName"/> instance; optionally
            allowing an unparseable resource name.
            </summary>
            <remarks>
            To parse successfully, the resource name must be formatted as one of the following:
            <list type="bullet"><item><description><c>organizations/{organization}</c></description></item></list>
            Or may be in any format if <paramref name="allowUnparsed"/> is <c>true</c>.
            </remarks>
            <param name="organizationName">The resource name in string form. Must not be <c>null</c>.</param>
            <param name="allowUnparsed">
            If <c>true</c> will successfully store an unparseable resource name into the <see cref="P:Google.Api.Gax.ResourceNames.OrganizationName.UnparsedResource"/>
            property; otherwise will throw an <see cref="T:System.ArgumentException"/> if an unparseable resource name is
            specified.
            </param>
            <returns>The parsed <see cref="T:Google.Api.Gax.ResourceNames.OrganizationName"/> if successful.</returns>
        </member>
        <member name="M:Google.Api.Gax.ResourceNames.OrganizationName.TryParse(System.String,Google.Api.Gax.ResourceNames.OrganizationName@)">
            <summary>
            Tries to parse the given resource name string into a new <see cref="T:Google.Api.Gax.ResourceNames.OrganizationName"/> instance.
            </summary>
            <remarks>
            To parse successfully, the resource name must be formatted as one of the following:
            <list type="bullet"><item><description><c>organizations/{organization}</c></description></item></list>
            </remarks>
            <param name="organizationName">The resource name in string form. Must not be <c>null</c>.</param>
            <param name="result">
            When this method returns, the parsed <see cref="T:Google.Api.Gax.ResourceNames.OrganizationName"/>, or <c>null</c> if parsing failed.
            </param>
            <returns><c>true</c> if the name was parsed successfully; <c>false</c> otherwise.</returns>
        </member>
        <member name="M:Google.Api.Gax.ResourceNames.OrganizationName.TryParse(System.String,System.Boolean,Google.Api.Gax.ResourceNames.OrganizationName@)">
            <summary>
            Tries to parse the given resource name string into a new <see cref="T:Google.Api.Gax.ResourceNames.OrganizationName"/> instance; optionally
            allowing an unparseable resource name.
            </summary>
            <remarks>
            To parse successfully, the resource name must be formatted as one of the following:
            <list type="bullet"><item><description><c>organizations/{organization}</c></description></item></list>
            Or may be in any format if <paramref name="allowUnparsed"/> is <c>true</c>.
            </remarks>
            <param name="organizationName">The resource name in string form. Must not be <c>null</c>.</param>
            <param name="allowUnparsed">
            If <c>true</c> will successfully store an unparseable resource name into the <see cref="P:Google.Api.Gax.ResourceNames.OrganizationName.UnparsedResource"/>
            property; otherwise will throw an <see cref="T:System.ArgumentException"/> if an unparseable resource name is
            specified.
            </param>
            <param name="result">
            When this method returns, the parsed <see cref="T:Google.Api.Gax.ResourceNames.OrganizationName"/>, or <c>null</c> if parsing failed.
            </param>
            <returns><c>true</c> if the name was parsed successfully; <c>false</c> otherwise.</returns>
        </member>
        <member name="M:Google.Api.Gax.ResourceNames.OrganizationName.#ctor(System.String)">
            <summary>
            Constructs a new instance of a <see cref="T:Google.Api.Gax.ResourceNames.OrganizationName"/> class from the component parts of pattern
            <c>organizations/{organization}</c>
            </summary>
            <param name="organizationId">The <c>Organization</c> ID. Must not be <c>null</c> or empty.</param>
        </member>
        <member name="P:Google.Api.Gax.ResourceNames.OrganizationName.Type">
            <summary>The <see cref="T:Google.Api.Gax.ResourceNames.OrganizationName.ResourceNameType"/> of the contained resource name.</summary>
        </member>
        <member name="P:Google.Api.Gax.ResourceNames.OrganizationName.UnparsedResource">
            <summary>
            The contained <see cref="T:Google.Api.Gax.UnparsedResourceName"/>. Only non-<c>null</c>if this instance contains an
            unparsed resource name.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.ResourceNames.OrganizationName.OrganizationId">
            <summary>
            The <c>Organization</c> ID. Will not be <c>null</c>, unless this instance contains an unparsed resource
            name.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.ResourceNames.OrganizationName.IsKnownPattern">
            <inheritdoc/>
        </member>
        <member name="M:Google.Api.Gax.ResourceNames.OrganizationName.ToString">
            <inheritdoc/>
        </member>
        <member name="M:Google.Api.Gax.ResourceNames.OrganizationName.GetHashCode">
            <inheritdoc/>
        </member>
        <member name="M:Google.Api.Gax.ResourceNames.OrganizationName.Equals(System.Object)">
            <inheritdoc/>
        </member>
        <member name="M:Google.Api.Gax.ResourceNames.OrganizationName.Equals(Google.Api.Gax.ResourceNames.OrganizationName)">
            <inheritdoc/>
        </member>
        <member name="M:Google.Api.Gax.ResourceNames.OrganizationName.op_Equality(Google.Api.Gax.ResourceNames.OrganizationName,Google.Api.Gax.ResourceNames.OrganizationName)">
            <inheritdoc/>
        </member>
        <member name="M:Google.Api.Gax.ResourceNames.OrganizationName.op_Inequality(Google.Api.Gax.ResourceNames.OrganizationName,Google.Api.Gax.ResourceNames.OrganizationName)">
            <inheritdoc/>
        </member>
        <member name="T:Google.Api.Gax.ResourceNames.ProjectName">
            <summary>
            Resource name for the 'project' resource which is widespread across Google Cloud Platform.
            While most resource names are generated on a per-API basis, many APIs use a project resource, and it's
            useful to be able to pass values from one API to another.
            </summary>
        </member>
        <member name="T:Google.Api.Gax.ResourceNames.ProjectName.ResourceNameType">
            <summary>The possible contents of <see cref="T:Google.Api.Gax.ResourceNames.ProjectName"/>.</summary>
        </member>
        <member name="F:Google.Api.Gax.ResourceNames.ProjectName.ResourceNameType.Unparsed">
            <summary>An unparsed resource name.</summary>
        </member>
        <member name="F:Google.Api.Gax.ResourceNames.ProjectName.ResourceNameType.Project">
            <summary>A resource name with pattern <c>projects/{project}</c>.</summary>
        </member>
        <member name="M:Google.Api.Gax.ResourceNames.ProjectName.FromUnparsed(Google.Api.Gax.UnparsedResourceName)">
            <summary>Creates a <see cref="T:Google.Api.Gax.ResourceNames.ProjectName"/> containing an unparsed resource name.</summary>
            <param name="unparsedResourceName">The unparsed resource name. Must not be <c>null</c>.</param>
            <returns>
            A new instance of <see cref="T:Google.Api.Gax.ResourceNames.ProjectName"/> containing the provided <paramref name="unparsedResourceName"/>.
            </returns>
        </member>
        <member name="M:Google.Api.Gax.ResourceNames.ProjectName.FromProject(System.String)">
            <summary>Creates a <see cref="T:Google.Api.Gax.ResourceNames.ProjectName"/> with the pattern <c>projects/{project}</c>.</summary>
            <param name="projectId">The <c>Project</c> ID. Must not be <c>null</c> or empty.</param>
            <returns>A new instance of <see cref="T:Google.Api.Gax.ResourceNames.ProjectName"/> constructed from the provided ids.</returns>
        </member>
        <member name="M:Google.Api.Gax.ResourceNames.ProjectName.Format(System.String)">
            <summary>
            Formats the IDs into the string representation of this <see cref="T:Google.Api.Gax.ResourceNames.ProjectName"/> with pattern
            <c>projects/{project}</c>.
            </summary>
            <param name="projectId">The <c>Project</c> ID. Must not be <c>null</c> or empty.</param>
            <returns>
            The string representation of this <see cref="T:Google.Api.Gax.ResourceNames.ProjectName"/> with pattern <c>projects/{project}</c>.
            </returns>
        </member>
        <member name="M:Google.Api.Gax.ResourceNames.ProjectName.FormatProject(System.String)">
            <summary>
            Formats the IDs into the string representation of this <see cref="T:Google.Api.Gax.ResourceNames.ProjectName"/> with pattern
            <c>projects/{project}</c>.
            </summary>
            <param name="projectId">The <c>Project</c> ID. Must not be <c>null</c> or empty.</param>
            <returns>
            The string representation of this <see cref="T:Google.Api.Gax.ResourceNames.ProjectName"/> with pattern <c>projects/{project}</c>.
            </returns>
        </member>
        <member name="M:Google.Api.Gax.ResourceNames.ProjectName.Parse(System.String)">
            <summary>Parses the given resource name string into a new <see cref="T:Google.Api.Gax.ResourceNames.ProjectName"/> instance.</summary>
            <remarks>
            To parse successfully, the resource name must be formatted as one of the following:
            <list type="bullet"><item><description><c>projects/{project}</c></description></item></list>
            </remarks>
            <param name="projectName">The resource name in string form. Must not be <c>null</c>.</param>
            <returns>The parsed <see cref="T:Google.Api.Gax.ResourceNames.ProjectName"/> if successful.</returns>
        </member>
        <member name="M:Google.Api.Gax.ResourceNames.ProjectName.Parse(System.String,System.Boolean)">
            <summary>
            Parses the given resource name string into a new <see cref="T:Google.Api.Gax.ResourceNames.ProjectName"/> instance; optionally allowing an
            unparseable resource name.
            </summary>
            <remarks>
            To parse successfully, the resource name must be formatted as one of the following:
            <list type="bullet"><item><description><c>projects/{project}</c></description></item></list>
            Or may be in any format if <paramref name="allowUnparsed"/> is <c>true</c>.
            </remarks>
            <param name="projectName">The resource name in string form. Must not be <c>null</c>.</param>
            <param name="allowUnparsed">
            If <c>true</c> will successfully store an unparseable resource name into the <see cref="P:Google.Api.Gax.ResourceNames.ProjectName.UnparsedResource"/>
            property; otherwise will throw an <see cref="T:System.ArgumentException"/> if an unparseable resource name is
            specified.
            </param>
            <returns>The parsed <see cref="T:Google.Api.Gax.ResourceNames.ProjectName"/> if successful.</returns>
        </member>
        <member name="M:Google.Api.Gax.ResourceNames.ProjectName.TryParse(System.String,Google.Api.Gax.ResourceNames.ProjectName@)">
            <summary>
            Tries to parse the given resource name string into a new <see cref="T:Google.Api.Gax.ResourceNames.ProjectName"/> instance.
            </summary>
            <remarks>
            To parse successfully, the resource name must be formatted as one of the following:
            <list type="bullet"><item><description><c>projects/{project}</c></description></item></list>
            </remarks>
            <param name="projectName">The resource name in string form. Must not be <c>null</c>.</param>
            <param name="result">
            When this method returns, the parsed <see cref="T:Google.Api.Gax.ResourceNames.ProjectName"/>, or <c>null</c> if parsing failed.
            </param>
            <returns><c>true</c> if the name was parsed successfully; <c>false</c> otherwise.</returns>
        </member>
        <member name="M:Google.Api.Gax.ResourceNames.ProjectName.TryParse(System.String,System.Boolean,Google.Api.Gax.ResourceNames.ProjectName@)">
            <summary>
            Tries to parse the given resource name string into a new <see cref="T:Google.Api.Gax.ResourceNames.ProjectName"/> instance; optionally
            allowing an unparseable resource name.
            </summary>
            <remarks>
            To parse successfully, the resource name must be formatted as one of the following:
            <list type="bullet"><item><description><c>projects/{project}</c></description></item></list>
            Or may be in any format if <paramref name="allowUnparsed"/> is <c>true</c>.
            </remarks>
            <param name="projectName">The resource name in string form. Must not be <c>null</c>.</param>
            <param name="allowUnparsed">
            If <c>true</c> will successfully store an unparseable resource name into the <see cref="P:Google.Api.Gax.ResourceNames.ProjectName.UnparsedResource"/>
            property; otherwise will throw an <see cref="T:System.ArgumentException"/> if an unparseable resource name is
            specified.
            </param>
            <param name="result">
            When this method returns, the parsed <see cref="T:Google.Api.Gax.ResourceNames.ProjectName"/>, or <c>null</c> if parsing failed.
            </param>
            <returns><c>true</c> if the name was parsed successfully; <c>false</c> otherwise.</returns>
        </member>
        <member name="M:Google.Api.Gax.ResourceNames.ProjectName.#ctor(System.String)">
            <summary>
            Constructs a new instance of a <see cref="T:Google.Api.Gax.ResourceNames.ProjectName"/> class from the component parts of pattern
            <c>projects/{project}</c>
            </summary>
            <param name="projectId">The <c>Project</c> ID. Must not be <c>null</c> or empty.</param>
        </member>
        <member name="P:Google.Api.Gax.ResourceNames.ProjectName.Type">
            <summary>The <see cref="T:Google.Api.Gax.ResourceNames.ProjectName.ResourceNameType"/> of the contained resource name.</summary>
        </member>
        <member name="P:Google.Api.Gax.ResourceNames.ProjectName.UnparsedResource">
            <summary>
            The contained <see cref="T:Google.Api.Gax.UnparsedResourceName"/>. Only non-<c>null</c>if this instance contains an
            unparsed resource name.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.ResourceNames.ProjectName.ProjectId">
            <summary>
            The <c>Project</c> ID. Will not be <c>null</c>, unless this instance contains an unparsed resource name.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.ResourceNames.ProjectName.IsKnownPattern">
            <inheritdoc/>
        </member>
        <member name="M:Google.Api.Gax.ResourceNames.ProjectName.ToString">
            <inheritdoc/>
        </member>
        <member name="M:Google.Api.Gax.ResourceNames.ProjectName.GetHashCode">
            <inheritdoc/>
        </member>
        <member name="M:Google.Api.Gax.ResourceNames.ProjectName.Equals(System.Object)">
            <inheritdoc/>
        </member>
        <member name="M:Google.Api.Gax.ResourceNames.ProjectName.Equals(Google.Api.Gax.ResourceNames.ProjectName)">
            <inheritdoc/>
        </member>
        <member name="M:Google.Api.Gax.ResourceNames.ProjectName.op_Equality(Google.Api.Gax.ResourceNames.ProjectName,Google.Api.Gax.ResourceNames.ProjectName)">
            <inheritdoc/>
        </member>
        <member name="M:Google.Api.Gax.ResourceNames.ProjectName.op_Inequality(Google.Api.Gax.ResourceNames.ProjectName,Google.Api.Gax.ResourceNames.ProjectName)">
            <inheritdoc/>
        </member>
        <member name="T:Google.Api.Gax.SystemClock">
            <summary>
            A singleton implementation of <see cref="T:Google.Api.Gax.IClock"/> which delegates to the BCL
            <see cref="P:System.DateTime.UtcNow"/> property.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.SystemClock.Instance">
            <summary>
            Retrieves the singleton instance of this type.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.SystemClock.GetCurrentDateTimeUtc">
            <summary>
            Returns the current date and time in UTC, using <see cref="P:System.DateTime.UtcNow"/>.
            </summary>
            <returns>The current date and time in UTC.</returns>
        </member>
        <member name="T:Google.Api.Gax.SystemScheduler">
            <summary>
            Singleton implementation of <see cref="T:Google.Api.Gax.IScheduler"/> which uses <see cref="M:System.Threading.Tasks.Task.Delay(System.TimeSpan,System.Threading.CancellationToken)"/>.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.SystemScheduler.Instance">
            <summary>
            Retrieves the singleton instance.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.SystemScheduler.Delay(System.TimeSpan,System.Threading.CancellationToken)">
            <inheritdoc />
        </member>
        <member name="T:Google.Api.Gax.TaskCompletionSourceExtensions">
            <summary>
            Extension methods for <see cref="T:System.Threading.Tasks.TaskCompletionSource`1"/>.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.TaskCompletionSourceExtensions.WithCancellationToken``1(System.Threading.Tasks.TaskCompletionSource{``0},System.Threading.CancellationToken)">
            <summary>
            Returns a task from a task completion source, but observing a given cancellation token.
            </summary>
            <typeparam name="TResult">The result type of the task completion source</typeparam>
            <param name="source">The task completion source. Must not be null.</param>
            <param name="cancellationToken">The cancellation token to observe.</param>
            <returns>A task that will complete when <paramref name="source"/> completes, but
            will observe <paramref name="cancellationToken"/> for cancellation.</returns>
        </member>
        <member name="T:Google.Api.Gax.TaskExtensions">
            <summary>
            Extension methods for tasks.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.TaskExtensions.ResultWithUnwrappedExceptions``1(System.Threading.Tasks.Task{``0})">
            <summary>
            Synchronously waits for the given task to complete, and returns the result.
            Any <see cref="T:System.AggregateException"/> thrown is unwrapped to the first inner exception.
            </summary>
            <typeparam name="T">The result type of the task</typeparam>
            <param name="task">The task to wait for.</param>
            <returns>The result of the completed task.</returns>
        </member>
        <member name="M:Google.Api.Gax.TaskExtensions.WaitWithUnwrappedExceptions(System.Threading.Tasks.Task)">
            <summary>
            Synchronously waits for the given task to complete.
            Any <see cref="T:System.AggregateException"/> thrown is unwrapped to the first inner exception.
            </summary>
            <param name="task">The task to wait for.</param>
        </member>
        <member name="M:Google.Api.Gax.TaskExtensions.WaitWithUnwrappedExceptions(System.Threading.Tasks.Task,System.TimeSpan)">
            <summary>
            Synchronously waits for the given task to complete.
            Any <see cref="T:System.AggregateException"/> thrown is unwrapped to the first inner exception.
            </summary>
            <param name="task">The task to wait for.</param>
            <param name="timeout">A TimeSpan that represents the number of milliseconds to wait, or
            -1 milliseconds to wait indefinitely.</param>
        </member>
        <member name="M:Google.Api.Gax.TaskExtensions.WaitWithUnwrappedExceptions(System.Threading.Tasks.Task,System.Int32)">
            <summary>
            Synchronously waits for the given task to complete.
            Any <see cref="T:System.AggregateException"/> thrown is unwrapped to the first inner exception.
            </summary>
            <param name="task">The task to wait for.</param>
            <param name="millisecondsTimeout">The number of milliseconds to wait, or
            -1 to wait indefinitely.</param>
        </member>
        <member name="M:Google.Api.Gax.TaskExtensions.WaitWithUnwrappedExceptions(System.Threading.Tasks.Task,System.Int32,System.Threading.CancellationToken)">
            <summary>
            Synchronously waits for the given task to complete.
            Any <see cref="T:System.AggregateException"/> thrown is unwrapped to the first inner exception.
            </summary>
            <param name="task">The task to wait for.</param>
            <param name="millisecondsTimeout">The number of milliseconds to wait, or
            -1 to wait indefinitely.</param>
            <param name="cancellationToken">A cancellation token to observe while waiting for the task to complete</param>
        </member>
        <member name="M:Google.Api.Gax.TaskExtensions.WaitWithUnwrappedExceptions(System.Threading.Tasks.Task,System.Threading.CancellationToken)">
            <summary>
            Synchronously waits for the given task to complete.
            Any <see cref="T:System.AggregateException"/> thrown is unwrapped to the first inner exception.
            </summary>
            <param name="task">The task to wait for.</param>
            <param name="cancellationToken">A cancellation token to observe while waiting for the task to complete</param>
        </member>
        <member name="T:Google.Api.Gax.TemplatedResourceName">
            <summary>
            Class for representing and working with resource names.
            </summary>
            <remarks>
            <para>
            A resource name is represented by a <see cref="T:Google.Api.Gax.PathTemplate"/>, an assignment of resource IDs to parameters in
            the template, and an optional service name. This class allows the service name and resource IDs to be
            modified, but only within the same template.
            </para>
            </remarks>
        </member>
        <member name="P:Google.Api.Gax.TemplatedResourceName.Template">
            <summary>
            The template this resource name is associated with. Never null.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.TemplatedResourceName.ServiceName">
            <summary>
            The service name part of this resource name, or null if no service name is specified.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.TemplatedResourceName.Item(System.Int32)">
            <summary>
            Gets or sets the identifier for the specified parameter index.
            </summary>
            <param name="index">The index of the parameter value to retrieve.</param>
            <returns>The identifier within the resource name at the given parameter index.</returns>
        </member>
        <member name="P:Google.Api.Gax.TemplatedResourceName.Item(System.String)">
            <summary>
            Gets or sets the identifier for the specified parameter name.
            </summary>
            <param name="parameterName">The name of the parameter value to retrieve.</param>
            <returns>The identifier within the resource name with the given parameter name.</returns>
        </member>
        <member name="M:Google.Api.Gax.TemplatedResourceName.#ctor(Google.Api.Gax.PathTemplate,System.String[])">
            <summary>
            Creates a resource name with the given template and resource IDs.
            The resource IDs are cloned, so later changes to <paramref name="resourceIds"/>
            are ignored. This constructor does not populate the <see cref="P:Google.Api.Gax.TemplatedResourceName.ServiceName"/> property,
            but that can be set after construction.
            </summary>
            <param name="template">The template for the new resource name. Must not be null.</param>
            <param name="resourceIds">The resource IDs to populate template parameters with. Must not be null.</param>
        </member>
        <member name="M:Google.Api.Gax.TemplatedResourceName.Clone">
            <summary>
            Creates a clone of this resource name, which is then independent of the original.
            </summary>
            <returns>A clone of this resource name.</returns>
        </member>
        <member name="M:Google.Api.Gax.TemplatedResourceName.#ctor(Google.Api.Gax.PathTemplate,System.String,System.String[],System.Boolean)">
            <summary>
            Private constructor used by internal code to avoid repeated cloning and validation.
            </summary>
        </member>
        <member name="P:Google.Api.Gax.TemplatedResourceName.IsKnownPattern">
            <inheritdoc />
        </member>
        <member name="M:Google.Api.Gax.TemplatedResourceName.ToString">
            <summary>
            Returns a string representation of this resource name, expanding the template
            parameters with the resource IDs and prepending the service name (if present).
            </summary>
            <returns>A string representation of this resource name.</returns>
        </member>
        <member name="M:Google.Api.Gax.TemplatedResourceName.GetHashCode">
            <inheritdoc />
        </member>
        <member name="M:Google.Api.Gax.TemplatedResourceName.Equals(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Google.Api.Gax.TemplatedResourceName.Equals(Google.Api.Gax.TemplatedResourceName)">
            <inheritdoc />
        </member>
        <member name="M:Google.Api.Gax.TemplatedResourceName.op_Equality(Google.Api.Gax.TemplatedResourceName,Google.Api.Gax.TemplatedResourceName)">
            <inheritdoc />
        </member>
        <member name="M:Google.Api.Gax.TemplatedResourceName.op_Inequality(Google.Api.Gax.TemplatedResourceName,Google.Api.Gax.TemplatedResourceName)">
            <inheritdoc />
        </member>
        <member name="T:Google.Api.Gax.UnparsedResourceName">
            <summary>
            A resource name in which nothing is known about the name structure.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.UnparsedResourceName.Parse(System.String)">
            <summary>
            Parse a resource name into an <see cref="T:Google.Api.Gax.UnparsedResourceName"/>.
            Only minimal verification is carried out that <paramref name="name"/> is a valid resource name string.
            </summary>
            <param name="name">A resource name.</param>
            <exception cref="T:System.ArgumentNullException"><paramref name="name"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="name"/> is an invalid resource name.</exception>
            <returns>An <see cref="T:Google.Api.Gax.UnparsedResourceName"/> representing the given string.</returns>
        </member>
        <member name="M:Google.Api.Gax.UnparsedResourceName.TryParse(System.String,Google.Api.Gax.UnparsedResourceName@)">
            <summary>
            Tries to parse the given resource name into an <see cref="T:Google.Api.Gax.UnparsedResourceName"/>.
            Only minimal verification is carried out that <paramref name="name"/> is a value resource name string.
            </summary>
            <param name="name">A resource name.</param>
            <param name="result">The <see cref="T:Google.Api.Gax.UnparsedResourceName"/> result if parsing is successful, otherwise <c>null</c>.</param>
            <returns><c>true</c> if <paramref name="name"/> was successfully parsed, otherwise <c>false</c>.</returns>
        </member>
        <member name="M:Google.Api.Gax.UnparsedResourceName.#ctor(System.String)">
            <summary>
            Creates an unkown resource name from the given resource name string.
            Only minimal verification is carried out that <paramref name="name"/> is a value resource name string.
            </summary>
            <param name="name"></param>
        </member>
        <member name="P:Google.Api.Gax.UnparsedResourceName.IsKnownPattern">
            <inheritdoc />
        </member>
        <member name="M:Google.Api.Gax.UnparsedResourceName.ToString">
            <inheritdoc />
        </member>
        <member name="M:Google.Api.Gax.UnparsedResourceName.GetHashCode">
            <inheritdoc />
        </member>
        <member name="M:Google.Api.Gax.UnparsedResourceName.Equals(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Google.Api.Gax.UnparsedResourceName.Equals(Google.Api.Gax.UnparsedResourceName)">
            <inheritdoc />
        </member>
        <member name="M:Google.Api.Gax.UnparsedResourceName.op_Equality(Google.Api.Gax.UnparsedResourceName,Google.Api.Gax.UnparsedResourceName)">
            <inheritdoc />
        </member>
        <member name="M:Google.Api.Gax.UnparsedResourceName.op_Inequality(Google.Api.Gax.UnparsedResourceName,Google.Api.Gax.UnparsedResourceName)">
            <inheritdoc />
        </member>
        <member name="T:Google.Api.Gax.VersionHeaderBuilder">
            <summary>
            Helps build version strings for the x-goog-api-client header.
            The value is a space-separated list of name/value pairs, where the value
            should be a semantic version string. Names must be unique.
            </summary>
        </member>
        <member name="F:Google.Api.Gax.VersionHeaderBuilder.HeaderName">
            <summary>
            The name of the header to set.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.VersionHeaderBuilder.AppendVersion(System.String,System.String)">
            <summary>
            Appends the given name/version string to the list.
            </summary>
            <param name="name">The name. Must not be null or empty, or contain a space or a slash.</param>
            <param name="version">The version. Must not be null, or contain a space or a slash.</param>
        </member>
        <member name="M:Google.Api.Gax.VersionHeaderBuilder.AppendAssemblyVersion(System.String,System.Type)">
            <summary>
            Appends a name/version string, taking the version from the version of the assembly
            containing the given type.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.VersionHeaderBuilder.AppendDotNetEnvironment">
            <summary>
            Appends the .NET environment information to the list.
            </summary>
        </member>
        <member name="M:Google.Api.Gax.VersionHeaderBuilder.IsHeaderNameValueValid(System.String)">
            <summary>
            Whether the name or value that are supposed to be included in a header are valid
            </summary>
        </member>
        <member name="M:Google.Api.Gax.VersionHeaderBuilder.ToString">
            <inheritdoc />
        </member>
        <member name="M:Google.Api.Gax.VersionHeaderBuilder.Clone">
            <summary>
            Clones this VersionHeaderBuilder, creating an independent copy with the same names/values.
            </summary>
            <returns>A clone of this builder.</returns>
        </member>
    </members>
</doc>
