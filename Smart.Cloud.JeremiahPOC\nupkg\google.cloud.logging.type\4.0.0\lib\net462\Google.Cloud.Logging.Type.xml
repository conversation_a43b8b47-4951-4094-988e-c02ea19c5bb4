<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Google.Cloud.Logging.Type</name>
    </assembly>
    <members>
        <member name="T:Google.Cloud.Logging.Type.HttpRequestReflection">
            <summary>Holder for reflection information generated from google/logging/type/http_request.proto</summary>
        </member>
        <member name="P:Google.Cloud.Logging.Type.HttpRequestReflection.Descriptor">
            <summary>File descriptor for google/logging/type/http_request.proto</summary>
        </member>
        <member name="T:Google.Cloud.Logging.Type.HttpRequest">
            <summary>
            A common proto for logging HTTP requests. Only contains semantics
            defined by the HTTP specification. Product-specific logging
            information MUST be defined in a separate message.
            </summary>
        </member>
        <member name="F:Google.Cloud.Logging.Type.HttpRequest.RequestMethodFieldNumber">
            <summary>Field number for the "request_method" field.</summary>
        </member>
        <member name="P:Google.Cloud.Logging.Type.HttpRequest.RequestMethod">
            <summary>
            The request method. Examples: `"GET"`, `"HEAD"`, `"PUT"`, `"POST"`.
            </summary>
        </member>
        <member name="F:Google.Cloud.Logging.Type.HttpRequest.RequestUrlFieldNumber">
            <summary>Field number for the "request_url" field.</summary>
        </member>
        <member name="P:Google.Cloud.Logging.Type.HttpRequest.RequestUrl">
            <summary>
            The scheme (http, https), the host name, the path and the query
            portion of the URL that was requested.
            Example: `"http://example.com/some/info?color=red"`.
            </summary>
        </member>
        <member name="F:Google.Cloud.Logging.Type.HttpRequest.RequestSizeFieldNumber">
            <summary>Field number for the "request_size" field.</summary>
        </member>
        <member name="P:Google.Cloud.Logging.Type.HttpRequest.RequestSize">
            <summary>
            The size of the HTTP request message in bytes, including the request
            headers and the request body.
            </summary>
        </member>
        <member name="F:Google.Cloud.Logging.Type.HttpRequest.StatusFieldNumber">
            <summary>Field number for the "status" field.</summary>
        </member>
        <member name="P:Google.Cloud.Logging.Type.HttpRequest.Status">
            <summary>
            The response code indicating the status of response.
            Examples: 200, 404.
            </summary>
        </member>
        <member name="F:Google.Cloud.Logging.Type.HttpRequest.ResponseSizeFieldNumber">
            <summary>Field number for the "response_size" field.</summary>
        </member>
        <member name="P:Google.Cloud.Logging.Type.HttpRequest.ResponseSize">
            <summary>
            The size of the HTTP response message sent back to the client, in bytes,
            including the response headers and the response body.
            </summary>
        </member>
        <member name="F:Google.Cloud.Logging.Type.HttpRequest.UserAgentFieldNumber">
            <summary>Field number for the "user_agent" field.</summary>
        </member>
        <member name="P:Google.Cloud.Logging.Type.HttpRequest.UserAgent">
            <summary>
            The user agent sent by the client. Example:
            `"Mozilla/4.0 (compatible; MSIE 6.0; Windows 98; Q312461; .NET
            CLR 1.0.3705)"`.
            </summary>
        </member>
        <member name="F:Google.Cloud.Logging.Type.HttpRequest.RemoteIpFieldNumber">
            <summary>Field number for the "remote_ip" field.</summary>
        </member>
        <member name="P:Google.Cloud.Logging.Type.HttpRequest.RemoteIp">
            <summary>
            The IP address (IPv4 or IPv6) of the client that issued the HTTP
            request. This field can include port information. Examples:
            `"***********"`, `"********:80"`, `"FE80::0202:B3FF:FE1E:8329"`.
            </summary>
        </member>
        <member name="F:Google.Cloud.Logging.Type.HttpRequest.ServerIpFieldNumber">
            <summary>Field number for the "server_ip" field.</summary>
        </member>
        <member name="P:Google.Cloud.Logging.Type.HttpRequest.ServerIp">
            <summary>
            The IP address (IPv4 or IPv6) of the origin server that the request was
            sent to. This field can include port information. Examples:
            `"***********"`, `"********:80"`, `"FE80::0202:B3FF:FE1E:8329"`.
            </summary>
        </member>
        <member name="F:Google.Cloud.Logging.Type.HttpRequest.RefererFieldNumber">
            <summary>Field number for the "referer" field.</summary>
        </member>
        <member name="P:Google.Cloud.Logging.Type.HttpRequest.Referer">
            <summary>
            The referer URL of the request, as defined in
            [HTTP/1.1 Header Field
            Definitions](http://www.w3.org/Protocols/rfc2616/rfc2616-sec14.html).
            </summary>
        </member>
        <member name="F:Google.Cloud.Logging.Type.HttpRequest.LatencyFieldNumber">
            <summary>Field number for the "latency" field.</summary>
        </member>
        <member name="P:Google.Cloud.Logging.Type.HttpRequest.Latency">
            <summary>
            The request processing latency on the server, from the time the request was
            received until the response was sent.
            </summary>
        </member>
        <member name="F:Google.Cloud.Logging.Type.HttpRequest.CacheLookupFieldNumber">
            <summary>Field number for the "cache_lookup" field.</summary>
        </member>
        <member name="P:Google.Cloud.Logging.Type.HttpRequest.CacheLookup">
            <summary>
            Whether or not a cache lookup was attempted.
            </summary>
        </member>
        <member name="F:Google.Cloud.Logging.Type.HttpRequest.CacheHitFieldNumber">
            <summary>Field number for the "cache_hit" field.</summary>
        </member>
        <member name="P:Google.Cloud.Logging.Type.HttpRequest.CacheHit">
            <summary>
            Whether or not an entity was served from cache
            (with or without validation).
            </summary>
        </member>
        <member name="F:Google.Cloud.Logging.Type.HttpRequest.CacheValidatedWithOriginServerFieldNumber">
            <summary>Field number for the "cache_validated_with_origin_server" field.</summary>
        </member>
        <member name="P:Google.Cloud.Logging.Type.HttpRequest.CacheValidatedWithOriginServer">
            <summary>
            Whether or not the response was validated with the origin server before
            being served from cache. This field is only meaningful if `cache_hit` is
            True.
            </summary>
        </member>
        <member name="F:Google.Cloud.Logging.Type.HttpRequest.CacheFillBytesFieldNumber">
            <summary>Field number for the "cache_fill_bytes" field.</summary>
        </member>
        <member name="P:Google.Cloud.Logging.Type.HttpRequest.CacheFillBytes">
            <summary>
            The number of HTTP response bytes inserted into cache. Set only when a
            cache fill was attempted.
            </summary>
        </member>
        <member name="F:Google.Cloud.Logging.Type.HttpRequest.ProtocolFieldNumber">
            <summary>Field number for the "protocol" field.</summary>
        </member>
        <member name="P:Google.Cloud.Logging.Type.HttpRequest.Protocol">
            <summary>
            Protocol used for the request. Examples: "HTTP/1.1", "HTTP/2", "websocket"
            </summary>
        </member>
        <member name="T:Google.Cloud.Logging.Type.LogSeverityReflection">
            <summary>Holder for reflection information generated from google/logging/type/log_severity.proto</summary>
        </member>
        <member name="P:Google.Cloud.Logging.Type.LogSeverityReflection.Descriptor">
            <summary>File descriptor for google/logging/type/log_severity.proto</summary>
        </member>
        <member name="T:Google.Cloud.Logging.Type.LogSeverity">
             <summary>
             The severity of the event described in a log entry, expressed as one of the
             standard severity levels listed below.  For your reference, the levels are
             assigned the listed numeric values. The effect of using numeric values other
             than those listed is undefined.
            
             You can filter for log entries by severity.  For example, the following
             filter expression will match log entries with severities `INFO`, `NOTICE`,
             and `WARNING`:
            
                 severity > DEBUG AND severity &lt;= WARNING
            
             If you are writing log entries, you should map other severity encodings to
             one of these standard levels. For example, you might map all of Java's FINE,
             FINER, and FINEST levels to `LogSeverity.DEBUG`. You can preserve the
             original severity level in the log entry payload if you wish.
             </summary>
        </member>
        <member name="F:Google.Cloud.Logging.Type.LogSeverity.Default">
            <summary>
            (0) The log entry has no assigned severity level.
            </summary>
        </member>
        <member name="F:Google.Cloud.Logging.Type.LogSeverity.Debug">
            <summary>
            (100) Debug or trace information.
            </summary>
        </member>
        <member name="F:Google.Cloud.Logging.Type.LogSeverity.Info">
            <summary>
            (200) Routine information, such as ongoing status or performance.
            </summary>
        </member>
        <member name="F:Google.Cloud.Logging.Type.LogSeverity.Notice">
            <summary>
            (300) Normal but significant events, such as start up, shut down, or
            a configuration change.
            </summary>
        </member>
        <member name="F:Google.Cloud.Logging.Type.LogSeverity.Warning">
            <summary>
            (400) Warning events might cause problems.
            </summary>
        </member>
        <member name="F:Google.Cloud.Logging.Type.LogSeverity.Error">
            <summary>
            (500) Error events are likely to cause problems.
            </summary>
        </member>
        <member name="F:Google.Cloud.Logging.Type.LogSeverity.Critical">
            <summary>
            (600) Critical events cause more severe problems or outages.
            </summary>
        </member>
        <member name="F:Google.Cloud.Logging.Type.LogSeverity.Alert">
            <summary>
            (700) A person must take an action immediately.
            </summary>
        </member>
        <member name="F:Google.Cloud.Logging.Type.LogSeverity.Emergency">
            <summary>
            (800) One or more systems are unusable.
            </summary>
        </member>
    </members>
</doc>
