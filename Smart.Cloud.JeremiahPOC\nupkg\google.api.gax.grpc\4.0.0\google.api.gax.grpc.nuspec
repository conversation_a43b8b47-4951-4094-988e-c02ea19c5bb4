﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2013/05/nuspec.xsd">
  <metadata>
    <id>Google.Api.Gax.Grpc</id>
    <version>4.0.0</version>
    <title>Google gRPC API Extensions</title>
    <authors>Google LLC</authors>
    <license type="file">LICENSE</license>
    <licenseUrl>https://aka.ms/deprecateLicenseUrl</licenseUrl>
    <icon>NuGetIcon.png</icon>
    <projectUrl>https://github.com/googleapis/gax-dotnet</projectUrl>
    <iconUrl>https://www.gstatic.com/images/branding/product/1x/google_developers_64dp.png</iconUrl>
    <description>Additional support classes for Google gRPC API client libraries</description>
    <copyright>Copyright 2020 Google LLC</copyright>
    <tags>Google</tags>
    <repository type="git" url="https://github.com/googleapis/gax-dotnet" commit="7489c236e0a5801d0f6e89bf1c23433f48521956" />
    <dependencies>
      <group targetFramework=".NETFramework4.6.2">
        <dependency id="Google.Api.CommonProtos" version="2.6.0" exclude="Build,Analyzers" />
        <dependency id="Google.Api.Gax" version="4.0.0" exclude="Build,Analyzers" />
        <dependency id="Google.Apis.Auth" version="[1.56.0, 2.0.0)" exclude="Build,Analyzers" />
        <dependency id="Grpc.Auth" version="[2.46.3, 3.0.0)" exclude="Build,Analyzers" />
        <dependency id="Grpc.Core.Api" version="[2.46.3, 3.0.0)" exclude="Build,Analyzers" />
        <dependency id="Grpc.Net.Client" version="[2.46.0, 3.0.0)" exclude="Build,Analyzers" />
        <dependency id="Microsoft.Extensions.DependencyInjection.Abstractions" version="6.0.0" exclude="Build,Analyzers" />
      </group>
      <group targetFramework=".NETStandard2.1">
        <dependency id="Google.Api.CommonProtos" version="2.6.0" exclude="Build,Analyzers" />
        <dependency id="Google.Api.Gax" version="4.0.0" exclude="Build,Analyzers" />
        <dependency id="Google.Apis.Auth" version="[1.56.0, 2.0.0)" exclude="Build,Analyzers" />
        <dependency id="Grpc.Auth" version="[2.46.3, 3.0.0)" exclude="Build,Analyzers" />
        <dependency id="Grpc.Core.Api" version="[2.46.3, 3.0.0)" exclude="Build,Analyzers" />
        <dependency id="Grpc.Net.Client" version="[2.46.0, 3.0.0)" exclude="Build,Analyzers" />
        <dependency id="Microsoft.Extensions.DependencyInjection.Abstractions" version="6.0.0" exclude="Build,Analyzers" />
      </group>
    </dependencies>
  </metadata>
</package>