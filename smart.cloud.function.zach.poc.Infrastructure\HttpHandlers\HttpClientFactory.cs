﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using System.Net.Http.Headers;

namespace smart.cloud.function.zach.poc.Infrastructure.HttpHandlers;
public static class HttpClientFactory
{
    public static IServiceCollection AddHttpClients(this IServiceCollection services)
    {
        var serviceProvider = services.BuildServiceProvider();
        var configurationAccessor = serviceProvider.GetService<IConfiguration>();
        var apiUrl = configurationAccessor?["ExternalApiURL:ApiUrl"] ?? "default-client";

        if (!string.IsNullOrEmpty(apiUrl) && apiUrl != "default-client")
        {
            services.AddHttpClient(apiUrl, client =>
            {
                client.BaseAddress = new Uri(apiUrl);
                client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
                Authentication(client);
            }).ConfigurePrimaryHttpMessageHandler(() => new HttpClientHandler()
            {
                UseDefaultCredentials = true
            });
        }
        else
        {
            // Add a default client if configuration is missing
            services.AddHttpClient("default-client");
        }
        return services;

        void Authentication(HttpClient client)
        {
            var appKey = Environment.GetEnvironmentVariable("ApigeeAppKey");
            if (!string.IsNullOrEmpty(appKey))
            {
                client.DefaultRequestHeaders?.Add("Authorization", "Bearer " + appKey);
            }
        }

    }
}

