<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Google.Cloud.Functions.Framework</name>
    </assembly>
    <members>
        <member name="T:Google.Cloud.Functions.Framework.CloudEventAdapter">
            <summary>
            An adapter to implement an HTTP Function based on a CloudEvent Function.
            </summary>
        </member>
        <member name="M:Google.Cloud.Functions.Framework.CloudEventAdapter.#ctor(Google.Cloud.Functions.Framework.ICloudEventFunction,CloudNative.CloudEvents.CloudEventFormatter,Microsoft.Extensions.Logging.ILogger{Google.Cloud.Functions.Framework.CloudEventAdapter})">
            <summary>
            Constructs a new instance based on the given CloudEvent Function.
            </summary>
            <param name="function">The CloudEvent Function to invoke.</param>
            <param name="formatter">The CloudEvent formatter to use when deserializing requests.</param>
            <param name="logger">The logger to use to report errors.</param>
        </member>
        <member name="M:Google.Cloud.Functions.Framework.CloudEventAdapter.HandleAsync(Microsoft.AspNetCore.Http.HttpContext)">
            <summary>
            Handles an HTTP request by extracting the CloudEvent from it and passing it to the
            original CloudEvent Function. The request fails if it does not contain a CloudEvent.
            </summary>
            <param name="context">The HTTP context containing the request and response.</param>
            <returns>A task representing the asynchronous operation.</returns>
        </member>
        <member name="M:Google.Cloud.Functions.Framework.CloudEventAdapter.ConvertRequestAsync(Microsoft.AspNetCore.Http.HttpRequest,CloudNative.CloudEvents.CloudEventFormatter,Microsoft.Extensions.Logging.ILogger)">
            <summary>
            Converts an HTTP request into a CloudEvent, either using regular CloudEvent parsing,
            or GCF event conversion if necessary.
            </summary>
            <param name="request">The request to convert.</param>
            <param name="formatter">The formatter to use for conversion.</param>
            <param name="logger">The logger to use to report any warnings.</param>
            <returns>A valid CloudEvent</returns>
        </member>
        <member name="T:Google.Cloud.Functions.Framework.CloudEventAdapter`1">
            <summary>
            An adapter to implement an HTTP Function based on a CloudEvent Function, with built-in event deserialization.
            </summary>
        </member>
        <member name="M:Google.Cloud.Functions.Framework.CloudEventAdapter`1.#ctor(Google.Cloud.Functions.Framework.ICloudEventFunction{`0},Microsoft.Extensions.Logging.ILogger{Google.Cloud.Functions.Framework.CloudEventAdapter{`0}})">
            <summary>
            Constructor without a <see cref="T:CloudNative.CloudEvents.CloudEventFormatter"/>, whose sole purpose is to throw a clear exception
            if an event formatter has not been configured.
            </summary>
        </member>
        <member name="M:Google.Cloud.Functions.Framework.CloudEventAdapter`1.#ctor(Google.Cloud.Functions.Framework.ICloudEventFunction{`0},CloudNative.CloudEvents.CloudEventFormatter,Microsoft.Extensions.Logging.ILogger{Google.Cloud.Functions.Framework.CloudEventAdapter{`0}})">
            <summary>
            Constructs a new instance based on the given CloudEvent Function.
            </summary>
            <param name="function">The CloudEvent Function to invoke.</param>
            <param name="formatter">The CloudEvent formatter to use to deserialize the CloudEvent.</param>
            <param name="logger">The logger to use to report errors.</param>
        </member>
        <member name="M:Google.Cloud.Functions.Framework.CloudEventAdapter`1.HandleAsync(Microsoft.AspNetCore.Http.HttpContext)">
            <summary>
            Handles an HTTP request by extracting the CloudEvent from it, deserializing the data, and passing
            both the event and the data to the original CloudEvent Function.
            The request fails if it does not contain a CloudEvent.
            </summary>
            <param name="context">The HTTP context containing the request and response.</param>
            <returns>A task representing the asynchronous operation.</returns>
        </member>
        <member name="T:Google.Cloud.Functions.Framework.GcfEvents.Context">
            <summary>
            The context of a GCF event.
            </summary>
        </member>
        <member name="P:Google.Cloud.Functions.Framework.GcfEvents.Context.Id">
            <summary>
            A unique ID for the event.
            </summary>
        </member>
        <member name="P:Google.Cloud.Functions.Framework.GcfEvents.Context.Timestamp">
            <summary>
            The date/time this event was created.
            </summary>
        </member>
        <member name="P:Google.Cloud.Functions.Framework.GcfEvents.Context.Type">
            <summary>
            The type of the event. For example: "google.pubsub.topic.publish".
            </summary>
        </member>
        <member name="P:Google.Cloud.Functions.Framework.GcfEvents.Context.Resource">
            <summary>
            The resource associated with the event.
            </summary>
        </member>
        <member name="T:Google.Cloud.Functions.Framework.GcfEvents.GcfConverters">
            <summary>
            Conversions from GCF events to CloudEvents.
            </summary>
        </member>
        <member name="T:Google.Cloud.Functions.Framework.GcfEvents.GcfConverters.EventAdapter">
            <summary>
            An event-specific adapter, picked based on the event type from the GCF event.
            This class provides seams for event-specific changes to the data and the source/subject,
            but the defult implementations of the seams are reasonable for most events.
            </summary>
        </member>
        <member name="M:Google.Cloud.Functions.Framework.GcfEvents.GcfConverters.EventAdapter.PopulateAttributes(Google.Cloud.Functions.Framework.GcfEvents.Request,CloudNative.CloudEvents.CloudEvent)">
            <summary>
            Populate the attributes in the CloudEvent that can't always be determined directly.
            In particular, this method must populate the Source context attribute,
            should populate the Subject context attribute if it's defined for this event type,
            and should populate any additional extension attributes to be compatible with Eventarc.
            The resource within context of the request will definitely have its Service and Name properties populated.
            This base implementation populates the Source based on the service and resource.
            </summary>
            <param name="request">The incoming request. The request's context always has a resource, and the service and name
            are always populated by the time this method is called.</param>
            <param name="evt">The event to populate</param>
        </member>
        <member name="M:Google.Cloud.Functions.Framework.GcfEvents.GcfConverters.StorageEventAdapter.PopulateAttributes(Google.Cloud.Functions.Framework.GcfEvents.Request,CloudNative.CloudEvents.CloudEvent)">
            <summary>
            Split a Storage object resource name into bucket (in the source) and object (in the subject)
            </summary>
        </member>
        <member name="M:Google.Cloud.Functions.Framework.GcfEvents.GcfConverters.PubSubEventAdapter.MaybeReshapeData(Google.Cloud.Functions.Framework.GcfEvents.Request)">
            <summary>
            Wrap the PubSub message and copy the message ID and publication time into
            it, for consistency with Eventarc.
            </summary>
        </member>
        <member name="M:Google.Cloud.Functions.Framework.GcfEvents.GcfConverters.PubSubEventAdapter.NormalizeRawRequest(Google.Cloud.Functions.Framework.GcfEvents.Request,System.String,Microsoft.Extensions.Logging.ILogger)">
            <summary>
            Normalizes a raw Pub/Sub push notification (from either the emulator
            or the real Pub/Sub service) into the existing legacy format.
            </summary>
            <param name="request">The incoming request body, parsed as a <see cref="T:Google.Cloud.Functions.Framework.GcfEvents.Request"/> object</param>
            <param name="path">The HTTP request path, from which the topic name will be extracted.</param>
            <param name="logger">The logger to use for a warning if the path cannot be used to derive a topic.</param>
        </member>
        <member name="T:Google.Cloud.Functions.Framework.GcfEvents.GcfConverters.FirestoreDocumentEventAdapter">
            <summary>
            Adapter for Firestore events that need extra source/subject handling.
            The resource names for Firestore have "documents" as the fifth segment.
            For example:
            "projects/project-id/databases/(default)/documents/gcf-test/IH75dRdeYJKd4uuQiqch".
            We validate that the fifth segment has the value we expect, and then use that segment onwards as the subject.
            </summary>
        </member>
        <member name="T:Google.Cloud.Functions.Framework.GcfEvents.GcfConverters.FirebaseRtdbEventAdapter">
            <summary>
            Adapter for Firebase RTDB events that need extra source/subject handling.
            The resource names for Firebase have "refs" as the fifth segment.
            For example:
            "projects/_/instances/my-project-id/refs/gcf-test/xyz" or
            We validate that the fifth segment has the value we expect, and then use that segment onwards as the subject.
            Additionally, we add the location, derived from the "domain" part of the context, into the source
            </summary>
        </member>
        <member name="M:Google.Cloud.Functions.Framework.GcfEvents.GcfConverters.FirebaseAuthEventAdapter.MaybeReshapeData(Google.Cloud.Functions.Framework.GcfEvents.Request)">
            <summary>
            Rename fields within metadata: lastSignedInAt => lastSignInTime, and createdAt => createTime.
            </summary>
            <param name="request"></param>
        </member>
        <member name="T:Google.Cloud.Functions.Framework.GcfEvents.GcfConverters.ConversionException">
            <summary>
            Exception thrown to indicate that the conversion of an HTTP request
            into a CloudEvent has failed. This is handled within <see cref="T:Google.Cloud.Functions.Framework.CloudEventAdapter`1"></see> and <see cref="T:Google.Cloud.Functions.Framework.CloudEventAdapter"/>.
            </summary>
        </member>
        <member name="P:Google.Cloud.Functions.Framework.GcfEvents.Request.RawPubSubSubscription">
            <summary>
            Raw PubSub only: the subscription triggering the request.
            </summary>
        </member>
        <member name="P:Google.Cloud.Functions.Framework.GcfEvents.Request.RawPubSubMessage">
            <summary>
            Raw PubSub only: the PubSub message.
            </summary>
        </member>
        <member name="M:Google.Cloud.Functions.Framework.GcfEvents.Request.NormalizeContext">
            <summary>
            Copies any top-level data into the context. Data
            already present in the context "wins".
            </summary>
        </member>
        <member name="T:Google.Cloud.Functions.Framework.GcfEvents.Resource">
            <summary>
            The resource that an event applies to.
            </summary>
        </member>
        <member name="P:Google.Cloud.Functions.Framework.GcfEvents.Resource.Service">
            <summary>
            The service that triggered the event.
            </summary>
        </member>
        <member name="P:Google.Cloud.Functions.Framework.GcfEvents.Resource.Name">
            <summary>
            The name associated with the event.
            </summary>
        </member>
        <member name="P:Google.Cloud.Functions.Framework.GcfEvents.Resource.Type">
            <summary>
            The type of the resource.
            </summary>
        </member>
        <member name="T:Google.Cloud.Functions.Framework.ICloudEventFunction">
            <summary>
            Function accepting a CloudEvent without performing any type-specific data deserialization.
            </summary>
        </member>
        <member name="M:Google.Cloud.Functions.Framework.ICloudEventFunction.HandleAsync(CloudNative.CloudEvents.CloudEvent,System.Threading.CancellationToken)">
            <summary>
            Asynchronously handles the specified CloudEvent.
            </summary>
            <param name="cloudEvent">The CloudEvent extracted from the request.</param>
            <param name="cancellationToken">A cancellation token which indicates if the request is aborted.</param>
            <returns>A task representing the potentially-asynchronous handling of the event.
            If the task completes, the function is deemed to be successful.</returns>
        </member>
        <member name="T:Google.Cloud.Functions.Framework.ICloudEventFunction`1">
            <summary>
            Function accepting a CloudEvent expecting a particular data type, which is automatically deserialized
            before function invocation.
            </summary>
            <typeparam name="TData">The expected type of the CloudEvent data. This type must be decorated
            with <see cref="T:CloudNative.CloudEvents.CloudEventFormatterAttribute"/> to indicate how to deserialize the CloudEvent.</typeparam>
        </member>
        <member name="M:Google.Cloud.Functions.Framework.ICloudEventFunction`1.HandleAsync(CloudNative.CloudEvents.CloudEvent,`0,System.Threading.CancellationToken)">
            <summary>
            Asynchronously handles the specified CloudEvent.
            </summary>
            <param name="cloudEvent">The original CloudEvent extracted from the request.</param>
            <param name="data">The deserialized object constructed from the data.</param>
            <param name="cancellationToken">A cancellation token which indicates if the request is aborted.</param>
            <returns>A task representing the potentially-asynchronous handling of the event.
            If the task completes, the function is deemed to be successful.</returns>
        </member>
        <member name="T:Google.Cloud.Functions.Framework.IHttpFunction">
            <summary>
            A simple HTTP function, which populates the <see cref="P:Microsoft.AspNetCore.Http.HttpContext.Response"/>.
            </summary>
        </member>
        <member name="M:Google.Cloud.Functions.Framework.IHttpFunction.HandleAsync(Microsoft.AspNetCore.Http.HttpContext)">
            <summary>
            Asynchronously handles the request in <see cref="P:Microsoft.AspNetCore.Http.HttpContext.Request"/>,
            populating <see cref="P:Microsoft.AspNetCore.Http.HttpContext.Response"/> to indicate the result.
            </summary>
            <param name="context">The HTTP context containing the request and response.</param>
            <returns>A task to indicate when the request is complete.</returns>
        </member>
        <member name="T:Google.Cloud.Functions.Framework.IHttpRequestReader`1">
            <summary>
            Responsible for reading a strongly typed request object from an
            HTTP request.
            </summary>
        </member>
        <member name="M:Google.Cloud.Functions.Framework.IHttpRequestReader`1.ReadRequestAsync(Microsoft.AspNetCore.Http.HttpRequest)">
            <summary>
            Asynchronously reads a strongly typed request object.
            </summary>
            <returns>A task representing the asynchronous operation.</returns>
        </member>
        <member name="T:Google.Cloud.Functions.Framework.IHttpResponseWriter`1">
            <summary>
            Responsible for writing a strongly typed response object to an
            HTTP response.
            </summary>
        </member>
        <member name="M:Google.Cloud.Functions.Framework.IHttpResponseWriter`1.WriteResponseAsync(Microsoft.AspNetCore.Http.HttpResponse,`0)">
            <summary>
            Asynchronously writes a strongly typed response object to
            an HTTP response.
            </summary>
            <returns>A task representing the asynchronous operation.</returns>
        </member>
        <member name="T:Google.Cloud.Functions.Framework.ITypedFunction`2">
            <summary>
            A typed function accepting a structured request and asynchronously 
            returning a structured response.
            </summary>
            <typeparam name="TRequest">Type of the request object.</typeparam>
            <typeparam name="TResponse">Type of the response object.</typeparam>
        </member>
        <member name="M:Google.Cloud.Functions.Framework.ITypedFunction`2.HandleAsync(`0,System.Threading.CancellationToken)">
            <summary>
            Asynchronously handles an incoming request, expected to return 
            a structured response.
            </summary>
            <param name="request">The request payload, deserialized from the incoming request.</param>
            <param name="cancellationToken">A cancellation token which indicates if the request is aborted.</param>
            <returns>A task representing the asynchronous operation.</returns>
        </member>
        <member name="T:Google.Cloud.Functions.Framework.Preconditions">
            <summary>
            Simple preconditions class, internal to avoid any conflicts and compatibility issues.
            </summary>
        </member>
        <member name="T:Google.Cloud.Functions.Framework.TypedFunctionAdapter`2">
            <summary>
            An adapter to implement an HTTP Function based on a <see cref="T:Google.Cloud.Functions.Framework.ITypedFunction`2"/>,
            with built-in request deserialization and response serialization.
            </summary>
        </member>
        <member name="M:Google.Cloud.Functions.Framework.TypedFunctionAdapter`2.#ctor(Google.Cloud.Functions.Framework.ITypedFunction{`0,`1},Google.Cloud.Functions.Framework.IHttpRequestReader{`0},Google.Cloud.Functions.Framework.IHttpResponseWriter{`1},Microsoft.Extensions.Logging.ILogger{Google.Cloud.Functions.Framework.TypedFunctionAdapter{`0,`1}})">
            <summary>
            Constructs a new instance based on the given TypedFunction.
            </summary>
        </member>
        <member name="M:Google.Cloud.Functions.Framework.TypedFunctionAdapter`2.HandleAsync(Microsoft.AspNetCore.Http.HttpContext)">
            <summary>
            Handles an HTTP request by extracting the CloudEvent from it, deserializing the data, and passing
            both the event and the data to the original CloudEvent Function.
            The request fails if it does not contain a CloudEvent.
            </summary>
            <param name="context">The HTTP context containing the request and response.</param>
            <returns>A task representing the asynchronous operation.</returns>
        </member>
    </members>
</doc>
