<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Google.Cloud.Functions.Hosting</name>
    </assembly>
    <members>
        <member name="T:Google.Cloud.Functions.Hosting.EntryPoint">
            <summary>
            The entry point for the hosting package. This is used automatically by the entry point generated by MS Build
            targets within the Google.Cloud.Functions.Hosting NuGet package.
            </summary>
        </member>
        <member name="F:Google.Cloud.Functions.Hosting.EntryPoint.FunctionTargetEnvironmentVariable">
            <summary>
            The environment variable used to detect the function target name, when not otherwise provided.
            </summary>
        </member>
        <member name="F:Google.Cloud.Functions.Hosting.EntryPoint.PortEnvironmentVariable">
            <summary>
            The environment variable used to detect the port to listen on.
            </summary>
        </member>
        <member name="M:Google.Cloud.Functions.Hosting.EntryPoint.StartAsync(System.Reflection.Assembly,System.String[])">
            <summary>
            Starts a web server to serve the function in the specified assembly. This method is called
            automatically be the generated entry point.
            </summary>
            <param name="functionAssembly">The assembly containing the function to execute.</param>
            <param name="args">Arguments to parse </param>
            <returns>A task representing the asynchronous operation.
            The result of the task is an exit code for the process, which is 0 for success or non-zero
            for any failures.
            </returns>
        </member>
        <member name="T:Google.Cloud.Functions.Hosting.FunctionsEnvironmentVariablesConfigurationSource">
            <summary>
            An IConfigurationSource that deals with *specific* Functions Framework environment variables
            (unlike the more general EnvironmentVariablesConfigurationSource).
            </summary>
        </member>
        <member name="T:Google.Cloud.Functions.Hosting.FunctionsFrameworkOptions">
            <summary>
            Convenience type to allow options to be bound within <see cref="T:Google.Cloud.Functions.Hosting.HostingInternals"/>.
            </summary>
        </member>
        <member name="T:Google.Cloud.Functions.Hosting.FunctionsStartup">
            <summary>
            Base class for startup classes providing additional configuration for functions
            in the Functions Framework, typically configuring services, but also with the ability
            to use additional application configuration sources or alternative logging providers.
            These types are discovered using <see cref="T:Google.Cloud.Functions.Hosting.FunctionsStartupAttribute"/>,
            which also allows ordering to be specified.
            </summary>
        </member>
        <member name="M:Google.Cloud.Functions.Hosting.FunctionsStartup.ConfigureServices(Microsoft.AspNetCore.Hosting.WebHostBuilderContext,Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            Configures additional services for the Functions Framework.
            The default implementation of this method does nothing.
            </summary>
            <param name="context">The context for the web host being built.</param>
            <param name="services">The service collection to configure.</param>
        </member>
        <member name="M:Google.Cloud.Functions.Hosting.FunctionsStartup.Configure(Microsoft.AspNetCore.Hosting.WebHostBuilderContext,Microsoft.AspNetCore.Builder.IApplicationBuilder)">
            <summary>
            Configures the application, typically to add middleware into the pipeline.
            This method is called at the start of pipeline configuration; after this has been called on
            all startup classes, the Functions Framework adds the mapping for prohibited paths (e.g. "favicon.ico")
            and the pipeline termination which calls the function target.
            The default implementation of this method does nothing.
            </summary>
            <param name="context">The context for the web host being built.</param>
            <param name="app">The application builder.</param>
        </member>
        <member name="M:Google.Cloud.Functions.Hosting.FunctionsStartup.ConfigureAppConfiguration(Microsoft.AspNetCore.Hosting.WebHostBuilderContext,Microsoft.Extensions.Configuration.IConfigurationBuilder)">
            <summary>
            Configures the application configuration, typically by adding another configuration source.
            </summary>
            <param name="context">The context for the web host being built.</param>
            <param name="configuration">The configuration builder.</param>
        </member>
        <member name="M:Google.Cloud.Functions.Hosting.FunctionsStartup.ConfigureLogging(Microsoft.AspNetCore.Hosting.WebHostBuilderContext,Microsoft.Extensions.Logging.ILoggingBuilder)">
            <summary>
            Configures the application logging, typically by adding another logging provider.
            </summary>
            <param name="context">The context for the web host being built.</param>
            <param name="logging">The configuration builder.</param>
        </member>
        <member name="T:Google.Cloud.Functions.Hosting.FunctionsStartupAttribute">
            <summary>
            Assembly and class attribute to specify a <see cref="T:Google.Cloud.Functions.Hosting.FunctionsStartup"/> to use
            for additional configuration in the Functions Framework. To use multiple
            startup classes, specify this attribute multiple times. Both the assembly and
            the function type are queried for this attribute when determining the functions startup
            classes to use.
            </summary>
        </member>
        <member name="P:Google.Cloud.Functions.Hosting.FunctionsStartupAttribute.StartupType">
            <summary>
            The Type of the <see cref="T:Google.Cloud.Functions.Hosting.FunctionsStartup"/> class to register.
            </summary>
        </member>
        <member name="P:Google.Cloud.Functions.Hosting.FunctionsStartupAttribute.Order">
            <summary>
            The ordering of application of the provider type relative to others
            specified by other attributes. Configurers specified in attributes
            with lower order numbers are invoked before those with higher order numbers.
            Defaults to 0.
            </summary>
        </member>
        <member name="M:Google.Cloud.Functions.Hosting.FunctionsStartupAttribute.#ctor(System.Type)">
            <summary>
            Constructs a new instance.
            </summary>
            <param name="startupType">The Type of the <see cref="T:Google.Cloud.Functions.Hosting.FunctionsStartup"/> class to register.</param>
        </member>
        <member name="M:Google.Cloud.Functions.Hosting.FunctionsStartupAttribute.GetStartupTypes(System.Reflection.Assembly,System.Type)">
            <summary>
            Returns the functions startup classes found in the attributes of <paramref name="assembly"/> and
            <paramref name="target"/>, ordered by <see cref="P:Google.Cloud.Functions.Hosting.FunctionsStartupAttribute.Order"/> and then by the full name of the type.
            </summary>
            <param name="assembly">The assembly to inspect for attributes.</param>
            <param name="target">Optional type to query for attributes. If this is null, only
            startup classes from <paramref name="assembly"/> are returned.
            </param>
            <returns>A (possibly empty) sequence of the startup types detected via attributes.</returns>
        </member>
        <member name="M:Google.Cloud.Functions.Hosting.HostingInternals.ValidateStartupClasses(Microsoft.AspNetCore.Builder.IApplicationBuilder)">
            <summary>
            Validates that the startup classes which would be used for the finally-selected function are the
            same (and in the same order) as the startup classes we *actually* used. This method is only called
            when the app is started by <see cref="T:Google.Cloud.Functions.Hosting.EntryPoint"/>, just to check that nothing really weird has
            happened (such as a startup changing the function target to a different function which needs
            different startups).
            </summary>
        </member>
        <member name="M:Google.Cloud.Functions.Hosting.HostingInternals.Execute(Microsoft.AspNetCore.Http.HttpContext)">
            <summary>
            Executes a request by asking the dependency injection context for an IHttpFunction, then
            executing it.
            </summary>
        </member>
        <member name="M:Google.Cloud.Functions.Hosting.HostingInternals.GetFunctionTarget(Microsoft.Extensions.Configuration.IConfiguration,System.Reflection.Assembly)">
            <summary>
            Determines the function target based on a configuration and the function assembly.
            This will throw an exception if it can't determine the right function type.
            </summary>
        </member>
        <member name="M:Google.Cloud.Functions.Hosting.HostingInternals.TryGetFunctionTarget(Microsoft.Extensions.Configuration.IConfiguration,System.Reflection.Assembly)">
            <summary>
            Determines the function target based on a configuration and the function assembly.
            This will return null if it can't determine the right function type.
            </summary>
        </member>
        <member name="M:Google.Cloud.Functions.Hosting.HostingInternals.FindDefaultFunctionType(System.Type[])">
            <summary>
            Attempts to find a single valid non-abstract function class within the given set of types.
            (Note that "non-abstract class" is a pretty low bar; we could enforce non-generic etc, but we'll
            discover problems there easily enough anyway.)
            </summary>
            <remarks>
            This method is internal rather than private for the sake of testability.
            </remarks>
            <param name="types">The types to search through.</param>
            <returns>The function type to use by default</returns>
            <exception cref="T:System.ArgumentException">There isn't a single valid function type.</exception>
        </member>
        <member name="M:Google.Cloud.Functions.Hosting.HostingInternals.TryFindDefaultFunctionType(System.Type[])">
            <summary>
            Attempts to find a single valid non-abstract function class within the given set of types, but returns
            null (instead of failing) if there isn't exactly one such class.
            </summary>
            <param name="types">The types to check for a valid function class.</param>
            <returns>The single valid function class, or null if there are either 0 or more than 1 such classes.</returns>
        </member>
        <member name="M:Google.Cloud.Functions.Hosting.HostingInternals.FindValidFunctionTypes(System.Type[])">
            <summary>
            Returns a list of valid function types. Designed to be used by <see cref="M:Google.Cloud.Functions.Hosting.HostingInternals.FindDefaultFunctionType(System.Type[])"/>
            and <see cref="M:Google.Cloud.Functions.Hosting.HostingInternals.TryFindDefaultFunctionType(System.Type[])"/>; think carefully before using in a different context.
            </summary>
        </member>
        <member name="M:Google.Cloud.Functions.Hosting.HostingInternals.GetGenericInterfaceImplementationTypeArgument(System.Type,System.Type)">
            <summary>
            Checks whether a given type implements a generic interface (assumed to have a single type parameter),
            and returns the type argument if so. For example, if we have "class MyFunction : ICloudEventFunction{PubSubMessage}"
            then a call to GetGenericInterfaceImplementationTypeArgument(typeof(MyFunction), typeof(ICloudEventFunction&lt;&gt;))
            will return typeof(PubSubMessage).
            </summary>
            <param name="target">The target type to check.</param>
            <param name="genericInterface">The generic interface</param>
            <returns></returns>
        </member>
        <member name="T:Google.Cloud.Functions.Hosting.HostingInternals.JsonHttpRequestReader`1">
            <summary>
            Implementation of <see cref="T:Google.Cloud.Functions.Framework.IHttpRequestReader`1"/> using System.Text.Json.
            </summary>
        </member>
        <member name="T:Google.Cloud.Functions.Hosting.HostingInternals.JsonHttpResponseWriter`1">
            <summary>
            Implementation of <see cref="T:Google.Cloud.Functions.Hosting.HostingInternals.JsonHttpResponseWriter`1"/> using System.Text.Json.
            </summary>
        </member>
        <member name="T:Google.Cloud.Functions.Hosting.Logging.FactoryLoggerProvider">
            <summary>
            Simple logger provider that just calls a factory method each time it's asked for logger.
            </summary>
        </member>
        <member name="T:Google.Cloud.Functions.Hosting.Logging.JsonConsoleLogger">
            <summary>
            Logger that writes a single line of JSON to the console per event, in a format that Google Cloud Logging can consume and display nicely.
            </summary>
        </member>
        <member name="F:Google.Cloud.Functions.Hosting.Logging.JsonConsoleLogger.NoScopesDepth">
            <summary>
            We don't get the current state of a Utf8JsonWriter, so when writing out scopes, we need to use current the depth of the writer to
            determine whether or not we've already started writing out the scopes.
            </summary>
        </member>
        <member name="T:Google.Cloud.Functions.Hosting.Logging.LoggerBase">
            <summary>
            Base class for loggers that don't do any of their own filtering, and don't support scopes.
            </summary>
        </member>
        <member name="M:Google.Cloud.Functions.Hosting.Logging.LoggerBase.Log``1(Microsoft.Extensions.Logging.LogLevel,Microsoft.Extensions.Logging.EventId,``0,System.Exception,System.Func{``0,System.Exception,System.String})">
            <summary>
            Performs common filtering and formats the message, before delegating
            to <see cref="M:Google.Cloud.Functions.Hosting.Logging.LoggerBase.LogImpl``1(Microsoft.Extensions.Logging.LogLevel,Microsoft.Extensions.Logging.EventId,``0,System.Exception,System.String)"/>.
            </summary>
        </member>
        <member name="M:Google.Cloud.Functions.Hosting.Logging.LoggerBase.LogImpl``1(Microsoft.Extensions.Logging.LogLevel,Microsoft.Extensions.Logging.EventId,``0,System.Exception,System.String)">
            <summary>
            Delegated "we've definitely got something to log" handling.
            </summary>
        </member>
        <member name="M:Google.Cloud.Functions.Hosting.Logging.LoggerBase.ToInvariantString(System.Object)">
            <summary>
            Convenience method to convert a value into a string using the invariant
            culture for formatting. Null input is converted into an empty string.
            </summary>
        </member>
        <member name="T:Google.Cloud.Functions.Hosting.Preconditions">
            <summary>
            Simple preconditions class, internal to avoid any conflicts and compatibility issues.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Hosting.ApplicationBuilderExtensions">
            <summary>
            Extensions for configuring an ApplicationBuilder for the Functions Framework.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Hosting.ApplicationBuilderExtensions.UseFunctionsFramework(Microsoft.AspNetCore.Builder.IApplicationBuilder,Microsoft.AspNetCore.Hosting.WebHostBuilderContext)">
            <summary>
            Configures the given application builder to use the Functions Framework.
            This method executes the <see cref="M:Google.Cloud.Functions.Hosting.FunctionsStartup.Configure(Microsoft.AspNetCore.Hosting.WebHostBuilderContext,Microsoft.AspNetCore.Builder.IApplicationBuilder)"/>
            method on any registered functions startup classes before adding handlers that
            return "not found" responses for fixed paths (e.g. "favicon.ico") and setting the terminal
            handler to execute the target function.
            </summary>
            <remarks>
            This method requires (at a minimum) that a function target has been registered,
            as it uses the <see cref="T:Google.Cloud.Functions.Framework.IHttpFunction"/> interface to handle requests.
            The target is typically registered using the
            <see cref="M:Microsoft.Extensions.DependencyInjection.FunctionsFrameworkServiceCollectionExtensions.AddFunctionTarget(Microsoft.Extensions.DependencyInjection.IServiceCollection,Microsoft.AspNetCore.Hosting.WebHostBuilderContext,System.Reflection.Assembly)"/>
            method or another <c>AddFunctionTarget</c> overload.
            </remarks>
            <param name="app">The application builder to configure.</param>
            <param name="context">The context of the web host builder being configured.</param>
            <returns>The original builder, for method chaining.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Hosting.ApplicationBuilderExtensions.UseFunctionsFramework(Microsoft.AspNetCore.Builder.IApplicationBuilder,Microsoft.AspNetCore.Hosting.WebHostBuilderContext,System.Boolean)">
            <summary>
            Internal method called directly by EntryPoint, so that it can validate that the appropriate startup
            classes to use haven't changed as a result of using the startup classes that *have* been executed.
            </summary>        
        </member>
        <member name="T:Microsoft.AspNetCore.Hosting.FunctionsFrameworkWebHostBuilderExtensions">
            <summary>
            Extensions for configuring a WebHostBuilder for the Functions Framework.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Hosting.FunctionsFrameworkWebHostBuilderExtensions.ConfigureKestrelForFunctionsFramework(Microsoft.AspNetCore.Hosting.IWebHostBuilder)">
            <summary>
            Configures Kestrel to listen to the port and address specified in the Functions Framework configuration.
            </summary>
            <param name="builder">The web host builder to configure.</param>
            <returns>The original builder, for method chaining.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Hosting.FunctionsFrameworkWebHostBuilderExtensions.UseFunctionsStartups(Microsoft.AspNetCore.Hosting.IWebHostBuilder,System.Reflection.Assembly,System.Type)">
            <summary>
            Uses the functions startup classes specified by <see cref="T:Google.Cloud.Functions.Hosting.FunctionsStartupAttribute"/> when configuring the host.
            Startup classes can contribute to logging, configuration sources, services, and application configuration.
            </summary>
            <param name="webHostBuilder">The web host builder to configure.</param>
            <param name="assembly">The assembly to query for attributes specifying startup classes.</param>
            <param name="functionType">The function type to query for attributes specifying startup classes, or null to skip this query.</param>
            <returns>The original builder, for method chaining.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Hosting.FunctionsFrameworkWebHostBuilderExtensions.UseFunctionsStartup(Microsoft.AspNetCore.Hosting.IWebHostBuilder,Google.Cloud.Functions.Hosting.FunctionsStartup)">
            <summary>
            Adds the given startup class into the service collection. This method can be called multiple times, and all startup
            classes will be used.
            Startup classes can contribute to logging, configuration sources, services, and application configuration.
            </summary>
            <param name="webHostBuilder">The web host builder to configure.</param>
            <param name="startup">The startup class to use.</param>
            <returns>The original builder, for method chaining.</returns>
        </member>
        <member name="T:Microsoft.Extensions.Configuration.FunctionsFrameworkConfigurationExtensions">
            <summary>
            Extensions for adding configuration for the Functions Framework.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.FunctionsFrameworkConfigurationExtensions.AddFunctionsEnvironment(Microsoft.Extensions.Configuration.IConfigurationBuilder)">
            <summary>
            Adds a configuration source for the Functions Framework based on environment variables (e.g. PORT and FUNCTION_TARGET).
            </summary>
            <param name="builder">The configuration builder to add the source to.</param>
            <returns>The original builder, for method chaining.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.FunctionsFrameworkConfigurationExtensions.AddFunctionsCommandLine(Microsoft.Extensions.Configuration.IConfigurationBuilder,System.String[])">
            <summary>
            Adds a configuration source for the Functions Framework based on command line arguments.
            </summary>
            <param name="builder">The configuration builder to add the source to.</param>
            <param name="args">The command line arguments to use for configuration.</param>
            <returns>The original builder, for method chaining.</returns>
        </member>
        <member name="T:Microsoft.Extensions.Logging.FunctionsFrameworkLoggingExtensions">
            <summary>
            Extensions for configuration logging for the Functions Framework.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Logging.FunctionsFrameworkLoggingExtensions.AddFunctionsConsoleLogging(Microsoft.Extensions.Logging.ILoggingBuilder,Microsoft.AspNetCore.Hosting.WebHostBuilderContext)">
            <summary>
            Adds a Functions Framework console logger, either using a "single line per log entry"
            plain text format (with separate lines for scopes and exceptions, when present) or a JSON format,
            depending on the execution environment.
            </summary>
            <param name="builder">The logging builder to add the logger to.</param>
            <param name="context">The context of the web host builder being configured.</param>
            <returns>The original builder, for method chaining.</returns>
        </member>
        <member name="T:Microsoft.Extensions.DependencyInjection.FunctionsFrameworkServiceCollectionExtensions">
            <summary>
            Extensions for adding services to the Functions Framework.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.FunctionsFrameworkServiceCollectionExtensions.AddFunctionTarget(Microsoft.Extensions.DependencyInjection.IServiceCollection,Microsoft.AspNetCore.Hosting.WebHostBuilderContext,System.Reflection.Assembly)">
            <summary>
            Adds required services to the service collection within the given web host builder for the Functions Framework
            to use a target function from the given assembly. If the Functions Framework configuration within the web host
            builder (typically provided by command line arguments or environment variables)
            does not specify a target function, the assembly is scanned for a single compatible function type.
            </summary>
            <remarks>
            If this method completes successfully, a binding for <see cref="T:Google.Cloud.Functions.Framework.IHttpFunction"/> will definitely
            have been added to the service collection. Other bindings may also be present, in order to adapt
            the function to <see cref="T:Google.Cloud.Functions.Framework.IHttpFunction"/>.
            </remarks>
            <param name="services">The service collection to configure.</param>
            <param name="context">The context of the web host builder being configured.</param>
            <param name="assembly">The assembly expected to contain the Functions Framework target function.</param>
            <returns>The original builder, for method chaining.</returns>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.FunctionsFrameworkServiceCollectionExtensions.AddFunctionTarget``1(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            Adds services required for the Functions Framework to use the function target type specified by the
            <typeparamref name="TFunction"/> type parameter.
            </summary>
            <remarks>
            If this method completes successfully, a binding for <see cref="T:Google.Cloud.Functions.Framework.IHttpFunction"/> will definitely
            have been added to the service collection. Other bindings may also be present, in order to adapt
            the function to <see cref="T:Google.Cloud.Functions.Framework.IHttpFunction"/>.
            </remarks>
            <typeparam name="TFunction">The function target type.</typeparam>
            <param name="services">The service collection to configure.</param>
            <returns>The original service collection, for method chaining.</returns>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.FunctionsFrameworkServiceCollectionExtensions.AddFunctionTarget(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.Type)">
            <summary>
            Adds services required for the Functions Framework to use the specified function target type, which must
            implement one of the Functions Framework function interfaces. 
            </summary>
            <remarks>
            If this method completes successfully, a binding for <see cref="T:Google.Cloud.Functions.Framework.IHttpFunction"/> will definitely
            have been added to the service collection. Other bindings may also be present, in order to adapt
            the function to <see cref="T:Google.Cloud.Functions.Framework.IHttpFunction"/>.
            </remarks>
            <param name="services">The service collection to configure.</param>
            <param name="type">The target function type.</param>
            <returns>The original service collection, for method chaining.</returns>
        </member>
    </members>
</doc>
