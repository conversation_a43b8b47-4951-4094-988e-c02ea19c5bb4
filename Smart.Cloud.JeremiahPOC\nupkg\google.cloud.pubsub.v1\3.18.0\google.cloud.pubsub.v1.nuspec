﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2013/05/nuspec.xsd">
  <metadata>
    <id>Google.Cloud.PubSub.V1</id>
    <version>3.18.0</version>
    <authors>Google LLC</authors>
    <license type="expression">Apache-2.0</license>
    <licenseUrl>https://licenses.nuget.org/Apache-2.0</licenseUrl>
    <icon>NuGetIcon.png</icon>
    <projectUrl>https://github.com/googleapis/google-cloud-dotnet</projectUrl>
    <iconUrl>https://cloud.google.com/images/gcp-icon-64x64.png</iconUrl>
    <description>Recommended Google client library to access the Google Cloud Pub/Sub API, which provides reliable, many-to-many, asynchronous messaging between applications.</description>
    <copyright>Copyright 2024 Google LLC</copyright>
    <tags>PubSub Google Cloud</tags>
    <repository type="git" url="https://github.com/googleapis/google-cloud-dotnet" commit="227a8d51df0c0d90ca1f9591b153be550710cff9" />
    <dependencies>
      <group targetFramework=".NETFramework4.6.2">
        <dependency id="Google.Api.Gax.Grpc" version="[4.9.0, 5.0.0)" exclude="Build,Analyzers" />
        <dependency id="Google.Cloud.Iam.V1" version="[3.3.0, 4.0.0)" exclude="Build,Analyzers" />
        <dependency id="Grpc.Core" version="[2.46.6, 3.0.0)" include="All" />
      </group>
      <group targetFramework=".NETStandard2.0">
        <dependency id="Google.Api.Gax.Grpc" version="[4.9.0, 5.0.0)" exclude="Build,Analyzers" />
        <dependency id="Google.Cloud.Iam.V1" version="[3.3.0, 4.0.0)" exclude="Build,Analyzers" />
      </group>
    </dependencies>
  </metadata>
</package>