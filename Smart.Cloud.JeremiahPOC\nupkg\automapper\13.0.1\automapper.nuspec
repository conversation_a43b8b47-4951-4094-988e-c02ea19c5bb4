﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2013/05/nuspec.xsd">
  <metadata>
    <id>AutoMapper</id>
    <version>13.0.1</version>
    <authors><PERSON></authors>
    <license type="expression">MIT</license>
    <licenseUrl>https://licenses.nuget.org/MIT</licenseUrl>
    <icon>icon.png</icon>
    <readme>README.md</readme>
    <projectUrl>https://automapper.org/</projectUrl>
    <description>A convention-based object-object mapper.</description>
    <repository type="git" url="https://github.com/AutoMapper/AutoMapper" commit="f13753466880413af7e4b2f642240ee06514544a" />
    <dependencies>
      <group targetFramework="net6.0">
        <dependency id="Microsoft.Extensions.Options" version="6.0.0" exclude="Build,Analyzers" />
      </group>
    </dependencies>
  </metadata>
</package>