﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2013/05/nuspec.xsd">
  <metadata>
    <id>Google.Apis.Auth</id>
    <version>1.56.0</version>
    <title>Google APIs Client Library</title>
    <authors>Google LLC</authors>
    <owners>Google LLC</owners>
    <requireLicenseAcceptance>false</requireLicenseAcceptance>
    <license type="file">LICENSE</license>
    <licenseUrl>https://aka.ms/deprecateLicenseUrl</licenseUrl>
    <icon>NuGetIcon.png</icon>
    <projectUrl>https://github.com/googleapis/google-api-dotnet-client</projectUrl>
    <iconUrl>https://www.gstatic.com/images/branding/product/1x/google_developers_64dp.png</iconUrl>
    <description>The Google APIs Client Library is a runtime client for working with Google services.

This package includes auth components like user-credential, authorization code flow, etc. for making authenticated calls using the OAuth2 spec.

Supported Platforms:
- .NET Framework 4.5
- .NET Framework 4.6.1
- .Net Standard 1.3
- .Net Standard 2.0</description>
    <copyright>Copyright 2021 Google LLC</copyright>
    <tags>Google</tags>
    <repository type="git" url="https://github.com/googleapis/google-api-dotnet-client" />
    <dependencies>
      <group targetFramework=".NETFramework4.5">
        <dependency id="Google.Apis.Core" version="1.56.0" exclude="Build,Analyzers" />
        <dependency id="Google.Apis" version="1.56.0" exclude="Build,Analyzers" />
      </group>
      <group targetFramework=".NETFramework4.6.1">
        <dependency id="Google.Apis.Core" version="1.56.0" exclude="Build,Analyzers" />
        <dependency id="Google.Apis" version="1.56.0" exclude="Build,Analyzers" />
      </group>
      <group targetFramework=".NETStandard1.3">
        <dependency id="Google.Apis.Core" version="1.56.0" exclude="Build,Analyzers" />
        <dependency id="Google.Apis" version="1.56.0" exclude="Build,Analyzers" />
        <dependency id="NETStandard.Library" version="1.6.1" exclude="Build,Analyzers" />
        <dependency id="System.Diagnostics.Process" version="4.3.0" exclude="Build,Analyzers" />
        <dependency id="System.Net.Requests" version="4.3.0" exclude="Build,Analyzers" />
      </group>
      <group targetFramework=".NETStandard2.0">
        <dependency id="Google.Apis.Core" version="1.56.0" exclude="Build,Analyzers" />
        <dependency id="Google.Apis" version="1.56.0" exclude="Build,Analyzers" />
      </group>
    </dependencies>
    <frameworkAssemblies>
      <frameworkAssembly assemblyName="System.Net.Http" targetFramework=".NETFramework4.5, .NETFramework4.6.1" />
    </frameworkAssemblies>
  </metadata>
</package>