﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2013/05/nuspec.xsd">
  <metadata>
    <id>Grpc.Auth</id>
    <version>2.46.3</version>
    <authors>The gRPC Authors</authors>
    <license type="expression">Apache-2.0</license>
    <licenseUrl>https://licenses.nuget.org/Apache-2.0</licenseUrl>
    <icon>packageIcon.png</icon>
    <projectUrl>https://github.com/grpc/grpc</projectUrl>
    <description>gRPC C# Authentication Library</description>
    <copyright>Copyright 2015 The gRPC Authors</copyright>
    <tags>gRPC RPC HTTP/2 Auth OAuth2</tags>
    <repository type="git" url="https://github.com/grpc/grpc.git" commit="53d69cc581c5b7305708587f4f1939278477c28a" />
    <dependencies>
      <group targetFramework=".NETFramework4.5">
        <dependency id="Grpc.Core.Api" version="2.46.3" include="All" />
        <dependency id="Google.Apis.Auth" version="1.46.0" exclude="Build,Analyzers" />
      </group>
      <group targetFramework=".NETStandard1.5">
        <dependency id="Grpc.Core.Api" version="2.46.3" include="All" />
        <dependency id="Google.Apis.Auth" version="1.46.0" exclude="Build,Analyzers" />
        <dependency id="NETStandard.Library" version="1.6.1" exclude="Build,Analyzers" />
      </group>
      <group targetFramework=".NETStandard2.0">
        <dependency id="Grpc.Core.Api" version="2.46.3" include="All" />
        <dependency id="Google.Apis.Auth" version="1.46.0" exclude="Build,Analyzers" />
      </group>
    </dependencies>
    <frameworkAssemblies>
      <frameworkAssembly assemblyName="Microsoft.CSharp" targetFramework=".NETFramework4.5" />
      <frameworkAssembly assemblyName="System" targetFramework=".NETFramework4.5" />
    </frameworkAssemblies>
  </metadata>
</package>