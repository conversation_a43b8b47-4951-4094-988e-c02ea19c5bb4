<?xml version="1.0"?>
<doc>
    <assembly>
        <name>CloudNative.CloudEvents.SystemTextJson</name>
    </assembly>
    <members>
        <member name="T:CloudNative.CloudEvents.SystemTextJson.JsonEventFormatter">
            <summary>
            Formatter that implements the JSON Event Format, using System.Text.Json for JSON serialization and deserialization.
            </summary>
            <remarks>
            <para>
            When encoding CloudEvent data, the behavior of this implementation depends on the data
            content type of the CloudEvent and the type of the <see cref="P:CloudNative.CloudEvents.CloudEvent.Data"/> property value,
            following the rules below. Derived classes can specialize this behavior by overriding
            <see cref="M:CloudNative.CloudEvents.SystemTextJson.JsonEventFormatter.EncodeStructuredModeData(CloudNative.CloudEvents.CloudEvent,System.Text.Json.Utf8JsonWriter)"/> or <see cref="M:CloudNative.CloudEvents.SystemTextJson.JsonEventFormatter.EncodeBinaryModeEventData(CloudNative.CloudEvents.CloudEvent)"/>.
            </para>
            <list type="bullet">
            <item><description>
            If the data value is null, the content is empty for a binary mode message, and neither the "data"
            nor "data_base64" property is populated in a structured mode message.
            </description></item>
            <item><description>
            If the data value is a byte array, it is serialized either directly as binary data
            (for binary mode messages) or as base64 data (for structured mode messages).
            </description></item>
            <item><description>
            Otherwise, if the data content type is absent or has a media type indicating JSON, the data is encoded as JSON,
            using the <see cref="T:System.Text.Json.JsonSerializerOptions"/> passed into the constructor, or the default options.
            </description></item>
            <item><description>
            Otherwise, if the data content type has a media type beginning with "text/" and the data value is a string,
            the data is serialized as a string.
            </description></item>
            <item><description>
            Otherwise, the encoding operation fails.
            </description></item>
            </list>
            <para>
            When decoding structured mode CloudEvent data, this implementation uses the following rules,
            which can be modified by overriding <see cref="M:CloudNative.CloudEvents.SystemTextJson.JsonEventFormatter.DecodeStructuredModeDataBase64Property(System.Text.Json.JsonElement,CloudNative.CloudEvents.CloudEvent)"/>
            and <see cref="M:CloudNative.CloudEvents.SystemTextJson.JsonEventFormatter.DecodeStructuredModeDataProperty(System.Text.Json.JsonElement,CloudNative.CloudEvents.CloudEvent)"/>.
            </para>
            <list type="bullet">
            <item><description>
            If the "data_base64" property is present, its value is decoded as a byte array.
            </description></item>
            <item><description>
            If the "data" property is present (and non-null) and the content type is absent or indicates a JSON media type,
            the JSON token present in the property is preserved as a <see cref="T:System.Text.Json.JsonElement"/> that can be used for further
            deserialization (e.g. to a specific CLR type).
            </description></item>
            <item><description>
            If the "data" property has a string value and a non-JSON content type has been specified, the data is
            deserialized as a string.
            </description></item>
            <item><description>
            If the "data" property has a non-null, non-string value and a non-JSON content type has been specified,
            the deserialization operation fails.
            </description></item>
            </list>
            <para>
            In a binary mode message, the data is parsed based on the content type of the message. When the content
            type is absent or has a media type of "application/json", the data is parsed as JSON, with the result as
            a <see cref="T:System.Text.Json.JsonElement"/> (or null if the data is empty). When the content type has a media type beginning
            with "text/", the data is parsed as a string. In all other cases, the data is left as a byte array.
            This behavior can be specialized by overriding <see cref="M:CloudNative.CloudEvents.SystemTextJson.JsonEventFormatter.DecodeBinaryModeEventData(System.ReadOnlyMemory{System.Byte},CloudNative.CloudEvents.CloudEvent)"/>.
            </para>
            </remarks>
        </member>
        <member name="F:CloudNative.CloudEvents.SystemTextJson.JsonEventFormatter.DataBase64PropertyName">
            <summary>
            The property name to use for base64-encoded binary data in a structured-mode message.
            </summary>
        </member>
        <member name="F:CloudNative.CloudEvents.SystemTextJson.JsonEventFormatter.DataPropertyName">
            <summary>
            The property name to use for general data in a structured-mode message.
            </summary>
        </member>
        <member name="P:CloudNative.CloudEvents.SystemTextJson.JsonEventFormatter.SerializerOptions">
            <summary>
            The options to use when serializing objects to JSON.
            </summary>
        </member>
        <member name="P:CloudNative.CloudEvents.SystemTextJson.JsonEventFormatter.DocumentOptions">
            <summary>
            The options to use when parsing JSON documents.
            </summary>
        </member>
        <member name="M:CloudNative.CloudEvents.SystemTextJson.JsonEventFormatter.#ctor">
            <summary>
            Creates a JsonEventFormatter that uses the default <see cref="T:System.Text.Json.JsonSerializerOptions"/>
            and <see cref="T:System.Text.Json.JsonDocumentOptions"/> for serializing and parsing.
            </summary>
        </member>
        <member name="M:CloudNative.CloudEvents.SystemTextJson.JsonEventFormatter.#ctor(System.Text.Json.JsonSerializerOptions,System.Text.Json.JsonDocumentOptions)">
            <summary>
            Creates a JsonEventFormatter that uses the specified <see cref="T:System.Text.Json.JsonSerializerOptions"/>
            and <see cref="T:System.Text.Json.JsonDocumentOptions"/> for serializing and parsing.
            </summary>
            <param name="serializerOptions">The options to use when serializing objects to JSON. May be null.</param>
            <param name="documentOptions">The options to use when parsing JSON documents.</param>
        </member>
        <member name="M:CloudNative.CloudEvents.SystemTextJson.JsonEventFormatter.DecodeStructuredModeMessageAsync(System.IO.Stream,System.Net.Mime.ContentType,System.Collections.Generic.IEnumerable{CloudNative.CloudEvents.CloudEventAttribute})">
            <inheritdoc />
        </member>
        <member name="M:CloudNative.CloudEvents.SystemTextJson.JsonEventFormatter.DecodeStructuredModeMessage(System.IO.Stream,System.Net.Mime.ContentType,System.Collections.Generic.IEnumerable{CloudNative.CloudEvents.CloudEventAttribute})">
            <inheritdoc />
        </member>
        <member name="M:CloudNative.CloudEvents.SystemTextJson.JsonEventFormatter.DecodeStructuredModeMessage(System.ReadOnlyMemory{System.Byte},System.Net.Mime.ContentType,System.Collections.Generic.IEnumerable{CloudNative.CloudEvents.CloudEventAttribute})">
            <inheritdoc />
        </member>
        <member name="M:CloudNative.CloudEvents.SystemTextJson.JsonEventFormatter.DecodeBatchModeMessageAsync(System.IO.Stream,System.Net.Mime.ContentType,System.Collections.Generic.IEnumerable{CloudNative.CloudEvents.CloudEventAttribute})">
            <inheritdoc />
        </member>
        <member name="M:CloudNative.CloudEvents.SystemTextJson.JsonEventFormatter.DecodeBatchModeMessage(System.IO.Stream,System.Net.Mime.ContentType,System.Collections.Generic.IEnumerable{CloudNative.CloudEvents.CloudEventAttribute})">
            <inheritdoc />
        </member>
        <member name="M:CloudNative.CloudEvents.SystemTextJson.JsonEventFormatter.DecodeBatchModeMessage(System.ReadOnlyMemory{System.Byte},System.Net.Mime.ContentType,System.Collections.Generic.IEnumerable{CloudNative.CloudEvents.CloudEventAttribute})">
            <inheritdoc />
        </member>
        <member name="M:CloudNative.CloudEvents.SystemTextJson.JsonEventFormatter.ConvertFromJsonElement(System.Text.Json.JsonElement,System.Collections.Generic.IEnumerable{CloudNative.CloudEvents.CloudEventAttribute})">
            <summary>
            Converts the given <see cref="T:System.Text.Json.JsonElement"/> into a <see cref="T:CloudNative.CloudEvents.CloudEvent"/>.
            </summary>
            <param name="element">The JSON representation of a CloudEvent. Must have a <see cref="P:System.Text.Json.JsonElement.ValueKind"/> of <see cref="F:System.Text.Json.JsonValueKind.Object"/>.</param>
            <param name="extensionAttributes">The extension attributes to use when populating the CloudEvent. May be null.</param>
            <returns>The SDK representation of the CloudEvent.</returns>
        </member>
        <member name="M:CloudNative.CloudEvents.SystemTextJson.JsonEventFormatter.DecodeStructuredModeDataBase64Property(System.Text.Json.JsonElement,CloudNative.CloudEvents.CloudEvent)">
            <summary>
            Decodes the "data_base64" property provided within a structured-mode message,
            populating the <see cref="P:CloudNative.CloudEvents.CloudEvent.Data"/> property accordingly.
            </summary>
            <remarks>
            <para>
            This implementation converts JSON string tokens to byte arrays, and fails for any other token type.
            </para>
            <para>
            Override this method to provide more specialized conversions.
            </para>
            </remarks>
            <param name="dataBase64Element">The "data_base64" property value within the structured-mode message. Will not be null, and will
            not have a null token type.</param>
            <param name="cloudEvent">The event being decoded. This should not be modified except to
            populate the <see cref="P:CloudNative.CloudEvents.CloudEvent.Data"/> property, but may be used to provide extra
            information such as the data content type. Will not be null.</param>
            <returns>The data to populate in the <see cref="P:CloudNative.CloudEvents.CloudEvent.Data"/> property.</returns>
        </member>
        <member name="M:CloudNative.CloudEvents.SystemTextJson.JsonEventFormatter.DecodeStructuredModeDataProperty(System.Text.Json.JsonElement,CloudNative.CloudEvents.CloudEvent)">
            <summary>
            Decodes the "data" property provided within a structured-mode message,
            populating the <see cref="P:CloudNative.CloudEvents.CloudEvent.Data"/> property accordingly.
            </summary>
            <remarks>
            <para>
            This implementation will populate the Data property with the verbatim <see cref="T:System.Text.Json.JsonElement"/> if
            the content type is deemed to be JSON according to <see cref="M:CloudNative.CloudEvents.SystemTextJson.JsonEventFormatter.IsJsonMediaType(System.String)"/>. Otherwise,
            it validates that the token is a string, and the Data property is populated with that string.
            </para>
            <para>
            Override this method to provide more specialized conversions.
            </para>
            </remarks>
            <param name="dataElement">The "data" property value within the structured-mode message. Will not be null, and will
            not have a null token type.</param>
            <param name="cloudEvent">The event being decoded. This should not be modified except to
            populate the <see cref="P:CloudNative.CloudEvents.CloudEvent.Data"/> property, but may be used to provide extra
            information such as the data content type. Will not be null, and the <see cref="P:CloudNative.CloudEvents.CloudEvent.DataContentType"/>
            property will be non-null.</param>
            <returns>The data to populate in the <see cref="P:CloudNative.CloudEvents.CloudEvent.Data"/> property.</returns>
        </member>
        <member name="M:CloudNative.CloudEvents.SystemTextJson.JsonEventFormatter.EncodeBatchModeMessage(System.Collections.Generic.IEnumerable{CloudNative.CloudEvents.CloudEvent},System.Net.Mime.ContentType@)">
            <inheritdoc />
        </member>
        <member name="M:CloudNative.CloudEvents.SystemTextJson.JsonEventFormatter.EncodeStructuredModeMessage(CloudNative.CloudEvents.CloudEvent,System.Net.Mime.ContentType@)">
            <inheritdoc />
        </member>
        <member name="M:CloudNative.CloudEvents.SystemTextJson.JsonEventFormatter.ConvertToJsonElement(CloudNative.CloudEvents.CloudEvent)">
            <summary>
            Converts the given <see cref="T:CloudNative.CloudEvents.CloudEvent"/> to a <see cref="T:System.Text.Json.JsonElement"/> containing the structured mode JSON format representation
            of the event.
            </summary>
            <remarks>The current implementation of this method is inefficient. Care should be taken before
            using this in performance-sensitive scenarios. The efficiency may well be improved in the future.</remarks>
            <param name="cloudEvent">The event to convert. Must not be null.</param>
            <returns>A <see cref="T:System.Text.Json.JsonElement"/> containing the structured mode JSON format representation of the event.</returns>
        </member>
        <member name="M:CloudNative.CloudEvents.SystemTextJson.JsonEventFormatter.InferDataContentType(System.Object)">
            <summary>
            Infers the data content type of a CloudEvent based on its data. This implementation
            infers a data content type of "application/json" for any non-binary data, and performs
            no inference for binary data.
            </summary>
            <param name="data">The CloudEvent to infer the data content from. Must not be null.</param>
            <returns>The inferred data content type, or null if no inference is performed.</returns>
        </member>
        <member name="M:CloudNative.CloudEvents.SystemTextJson.JsonEventFormatter.EncodeStructuredModeData(CloudNative.CloudEvents.CloudEvent,System.Text.Json.Utf8JsonWriter)">
            <summary>
            Encodes structured mode data within a CloudEvent, writing it to the specified <see cref="T:System.Text.Json.Utf8JsonWriter"/>.
            </summary>
            <remarks>
            <para>
            This implementation follows the rules listed in the class remarks. Override this method
            to provide more specialized behavior, writing only <see cref="F:CloudNative.CloudEvents.SystemTextJson.JsonEventFormatter.DataPropertyName"/> or
            <see cref="F:CloudNative.CloudEvents.SystemTextJson.JsonEventFormatter.DataBase64PropertyName"/> properties.
            </para>
            </remarks>
            <param name="cloudEvent">The CloudEvent being encoded, which will have a non-null value for
            its <see cref="P:CloudNative.CloudEvents.CloudEvent.Data"/> property.
            <param name="writer"/>The writer to serialize the data to. Will not be null.</param>
        </member>
        <member name="M:CloudNative.CloudEvents.SystemTextJson.JsonEventFormatter.EncodeBinaryModeEventData(CloudNative.CloudEvents.CloudEvent)">
            <inheritdoc />
        </member>
        <member name="M:CloudNative.CloudEvents.SystemTextJson.JsonEventFormatter.DecodeBinaryModeEventData(System.ReadOnlyMemory{System.Byte},CloudNative.CloudEvents.CloudEvent)">
            <inheritdoc />
        </member>
        <member name="M:CloudNative.CloudEvents.SystemTextJson.JsonEventFormatter.IsJsonMediaType(System.String)">
            <summary>
            Determines whether the given media type should be handled as JSON.
            The default implementation treats anything ending with "/json" or "+json"
            as JSON.
            </summary>
            <param name="mediaType">The media type to check for JSON. Will not be null.</param>
            <returns>Whether or not <paramref name="mediaType"/> indicates JSON data.</returns>
        </member>
        <member name="T:CloudNative.CloudEvents.SystemTextJson.JsonEventFormatter`1">
            <summary>
            CloudEvent formatter implementing the JSON Event Format, but with an expectation that
            any CloudEvent with a data payload can be converted to <typeparamref name="T" /> using
            the <see cref="T:System.Text.Json.JsonSerializer"/> associated with the formatter. The content type is ignored.
            </summary>
            <typeparam name="T">The type of data to serialize and deserialize.</typeparam>
        </member>
        <member name="M:CloudNative.CloudEvents.SystemTextJson.JsonEventFormatter`1.#ctor">
            <summary>
            Creates a JsonEventFormatter that uses the default <see cref="T:System.Text.Json.JsonSerializerOptions"/>
            and <see cref="T:System.Text.Json.JsonDocumentOptions"/> for serializing and parsing.
            </summary>
        </member>
        <member name="M:CloudNative.CloudEvents.SystemTextJson.JsonEventFormatter`1.#ctor(System.Text.Json.JsonSerializerOptions,System.Text.Json.JsonDocumentOptions)">
            <summary>
            Creates a JsonEventFormatter that uses the serializer <see cref="T:System.Text.Json.JsonSerializerOptions"/>
            and <see cref="T:System.Text.Json.JsonDocumentOptions"/> for serializing and parsing.
            </summary>
            <param name="serializerOptions">The options to use when serializing and parsing. May be null.</param>
            <param name="documentOptions">The options to use when parsing JSON documents.</param>
        </member>
        <member name="M:CloudNative.CloudEvents.SystemTextJson.JsonEventFormatter`1.EncodeBinaryModeEventData(CloudNative.CloudEvents.CloudEvent)">
            <inheritdoc />
        </member>
        <member name="M:CloudNative.CloudEvents.SystemTextJson.JsonEventFormatter`1.DecodeBinaryModeEventData(System.ReadOnlyMemory{System.Byte},CloudNative.CloudEvents.CloudEvent)">
            <inheritdoc />
        </member>
        <member name="M:CloudNative.CloudEvents.SystemTextJson.JsonEventFormatter`1.EncodeStructuredModeData(CloudNative.CloudEvents.CloudEvent,System.Text.Json.Utf8JsonWriter)">
            <inheritdoc />
        </member>
        <member name="M:CloudNative.CloudEvents.SystemTextJson.JsonEventFormatter`1.DecodeStructuredModeDataProperty(System.Text.Json.JsonElement,CloudNative.CloudEvents.CloudEvent)">
            <inheritdoc />
        </member>
        <member name="M:CloudNative.CloudEvents.SystemTextJson.JsonEventFormatter`1.DecodeStructuredModeDataBase64Property(System.Text.Json.JsonElement,CloudNative.CloudEvents.CloudEvent)">
            <inheritdoc />
        </member>
    </members>
</doc>
