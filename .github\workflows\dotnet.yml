# This workflow will build a .NET project
# For more information see: https://docs.github.com/en/actions/automating-builds-and-tests/building-and-testing-net

name: .NET

on:
  push:
    branches: [ "develop" ]
  pull_request:
    branches: [ "develop" ]

jobs:
  unit-test:
    uses: HCAHealthtrust/devops-github-actions/.github/workflows/unit-test-execution.yaml@main
    with:
      projectPath: './Smart.Cloud.JeremiahPOC.Tests'
      dotNetVersion: '8.0.x'
      
