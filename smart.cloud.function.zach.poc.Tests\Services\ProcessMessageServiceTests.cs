using Google.Events.Protobuf.Cloud.PubSub.V1;
using Microsoft.Extensions.Logging;
using Moq;
using Newtonsoft.Json;
using Smart.Cloud.Core.Common.EventModels;
using Smart.Cloud.Core.PubSub.Interfaces;
using smart.cloud.function.zach.poc.Core.Interfaces.IServices;
using smart.cloud.function.zach.poc.Core.Models;
using smart.cloud.function.zach.poc.Services.Services;
using Xunit;

namespace smart.cloud.function.zach.poc.Tests.Services;

public class ProcessMessageServiceTests
{
    [Fact]
    public async Task Test_ProcessMessage_DependenciesWereCalled()
    {
        // Arrange
        var payload = GetMockPayload();
        var mockMessage = GetMockMessage(payload);

        var mockedPayloadHandler = new Mock<IPayloadHandlerService>();
        MockDeserializationOfPayload(mockedPayloadHandler, mockMessage);

        var mockApiConnectorService = new Mock<IApiConnectorService>();
        var mockLogger = new Mock<ILogger<ProcessMessageService>>();

        var processMessageService = new ProcessMessageService(
            mockedPayloadHandler.Object,
            mockApiConnectorService.Object,
            mockLogger.Object
        );

        var messageData = GetMockPublishedMessage(mockMessage);


        // Act
        await processMessageService.ProcessMessage(messageData);


        // Assert
        mockedPayloadHandler.Verify(handler => handler.DeserializePayload<Payload>(It.IsAny<MessagePublishedData>()),
            Times.Once);

        mockApiConnectorService.Verify(service => service.SendToApi(It.IsAny<EmployeeMessage>()),
            Times.Once);
    }

    #region Fixtures to mock dependency behaviors

    private void MockDeserializationOfPayload(Mock<IPayloadHandlerService> mocker, MessageBase<Payload> message)
    {
        mocker.Setup(x => x.DeserializePayload<Payload>(It.IsAny<MessagePublishedData>())).Returns(message);
    }



    #endregion Fixtures to mock dependency behaviors

    #region Fixtures to create mock data

    private Payload GetMockPayload()
    {
        return new Payload(new EmployeeMessage 
        { 
            EmployeeId = 12345,
            FirstName = "John",
            LastName = "Doe",
            HiringDate = DateTime.UtcNow
        });
    }

    private MessageBase<Payload> GetMockMessage(Payload mockPayload)
    {
        return new MessageBase<Payload>
        {
            Header = GetMockHeader(),
            Body = GetMockBody(mockPayload)
        };
    }

    private Header GetMockHeader()
    {
        return new Header
        {
            Version = "1.0.0",
            Sender = "sender",
            Event = "event",
            EventType = "eventType",
            ActionType = "actionType",
            ActionStatus = "actionStatus",
            CorrelationId = new Guid(),
            TimeStamp = DateTimeOffset.UtcNow,
            Actor = "actor"
        };
    }

    private Body<Payload> GetMockBody(Payload mockPayload)
    {
        return new Body<Payload> { Payload = mockPayload };
    }

    private MessagePublishedData GetMockPublishedMessage(MessageBase<Payload> mockMessage)
    {
        // Create JSON string of standard message with custom payload in the body.
        var jsonString = JsonConvert.SerializeObject(mockMessage);

        return new MessagePublishedData
        {
            Message = new PubsubMessage { TextData = jsonString }
        };
    }

    #endregion Fixtures to create mock data
}
