﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2013/05/nuspec.xsd">
  <metadata>
    <id>Google.Api.Gax.Grpc</id>
    <version>4.9.0</version>
    <title>Google gRPC API Extensions</title>
    <authors>Google LLC</authors>
    <license type="expression">BSD-3-Clause</license>
    <licenseUrl>https://licenses.nuget.org/BSD-3-Clause</licenseUrl>
    <icon>NuGetIcon.png</icon>
    <projectUrl>https://github.com/googleapis/gax-dotnet</projectUrl>
    <iconUrl>https://www.gstatic.com/images/branding/product/1x/google_developers_64dp.png</iconUrl>
    <description>Additional support classes for Google gRPC API client libraries</description>
    <copyright>Copyright 2020 Google LLC</copyright>
    <tags>Google</tags>
    <repository type="git" url="https://github.com/googleapis/gax-dotnet" commit="963c88412321977fce5b690e4a3bfce154df928d" />
    <dependencies>
      <group targetFramework=".NETFramework4.6.2">
        <dependency id="Google.Api.CommonProtos" version="2.16.0" exclude="Build,Analyzers" />
        <dependency id="Google.Api.Gax" version="4.9.0" exclude="Build,Analyzers" />
        <dependency id="Google.Apis.Auth" version="1.68.0" exclude="Build,Analyzers" />
        <dependency id="Grpc.Auth" version="2.66.0" exclude="Build,Analyzers" />
        <dependency id="Grpc.Core.Api" version="2.66.0" exclude="Build,Analyzers" />
        <dependency id="Grpc.Net.Client" version="2.66.0" exclude="Build,Analyzers" />
        <dependency id="Microsoft.Extensions.DependencyInjection.Abstractions" version="6.0.0" exclude="Build,Analyzers" />
      </group>
      <group targetFramework=".NETStandard2.0">
        <dependency id="Google.Api.CommonProtos" version="2.16.0" exclude="Build,Analyzers" />
        <dependency id="Google.Api.Gax" version="4.9.0" exclude="Build,Analyzers" />
        <dependency id="Google.Apis.Auth" version="1.68.0" exclude="Build,Analyzers" />
        <dependency id="Grpc.Auth" version="2.66.0" exclude="Build,Analyzers" />
        <dependency id="Grpc.Core.Api" version="2.66.0" exclude="Build,Analyzers" />
        <dependency id="Grpc.Net.Client" version="2.66.0" exclude="Build,Analyzers" />
        <dependency id="Microsoft.Extensions.DependencyInjection.Abstractions" version="6.0.0" exclude="Build,Analyzers" />
      </group>
    </dependencies>
  </metadata>
</package>