﻿using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using Smart.Cloud.JeremiahPOC.Core.Interfaces.IInfrastructure;
using Smart.Cloud.JeremiahPOC.Core.Interfaces.IServices;
using Smart.Cloud.JeremiahPOC.Core.Models;
using System.Net.Http.Json;

namespace Smart.Cloud.JeremiahPOC.Services.Services;

public class ApiConnectorService : IApiConnectorService
{
    private readonly IApiWrapper _apiWrapper;
    private readonly string? _transferOrderServiceUrl;

    public ApiConnectorService(IApiWrapper apiWrapper, IConfiguration configuration)
    {
        _transferOrderServiceUrl = configuration["ApiUrl"];
        _apiWrapper = apiWrapper;
    }

    public async Task SendToApiPost<T>(T data)
    {
        var _httpClient = new HttpClient();

        string url = "https://localhost:7203/api/Employee/CreateEmployee";
        HttpResponseMessage response = await _httpClient.PostAsJsonAsync(url, data);

        // Ensure the request was successful
        response.EnsureSuccessStatusCode(); 

        // Read the response content as a string
        string responseBody = await response.Content.ReadAsStringAsync();

        var responseObject = JsonConvert.DeserializeObject<EmployeeResponse>(responseBody);

        //HttpContent httpContent = JsonContent.Create(data);

        //await _apiWrapper.PostAsyncApi<string>(_transferOrderServiceUrl!, ApiEndPoint,
        //    parameters: null, httpContent);
    }

    public async Task SendToApiGet<T>(T data)
    {
        var _httpClient = new HttpClient();

        string url = "https://localhost:7203/api/Employee/GetEmployeeByEmployeeIdAsync";
        HttpResponseMessage response = await _httpClient.GetAsync($"{url}?employeeId={data}");

        // Ensure the request was successful
        response.EnsureSuccessStatusCode(); 

        // Read the response content as a string
        string responseBody = await response.Content.ReadAsStringAsync();

        var responseObject = JsonConvert.DeserializeObject<EmployeeResponse>(responseBody);
    }
}

