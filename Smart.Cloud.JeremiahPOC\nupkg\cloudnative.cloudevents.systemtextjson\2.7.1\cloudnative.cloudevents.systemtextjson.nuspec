﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2013/05/nuspec.xsd">
  <metadata>
    <id>CloudNative.CloudEvents.SystemTextJson</id>
    <version>2.7.1</version>
    <authors>CloudNative.CloudEvents.SystemTextJson</authors>
    <license type="expression">Apache-2.0</license>
    <licenseUrl>https://licenses.nuget.org/Apache-2.0</licenseUrl>
    <icon>nuget-icon.png</icon>
    <projectUrl>https://cloudevents.io/</projectUrl>
    <description>JSON support for the CNCF CloudEvents SDK, based on System.Text.Json.</description>
    <copyright>Copyright Cloud Native Foundation</copyright>
    <tags>cncf cloudnative cloudevents events json systemtextjson</tags>
    <repository type="git" url="https://github.com/cloudevents/sdk-csharp" commit="79999423345c75e4457abc10bd853e5899cfb1e7" />
    <dependencies>
      <group targetFramework=".NETStandard2.0">
        <dependency id="CloudNative.CloudEvents" version="2.7.1" exclude="Build,Analyzers" />
        <dependency id="System.Text.Json" version="5.0.2" exclude="Build,Analyzers" />
      </group>
      <group targetFramework=".NETStandard2.1">
        <dependency id="CloudNative.CloudEvents" version="2.7.1" exclude="Build,Analyzers" />
        <dependency id="System.Text.Json" version="5.0.2" exclude="Build,Analyzers" />
      </group>
    </dependencies>
  </metadata>
</package>