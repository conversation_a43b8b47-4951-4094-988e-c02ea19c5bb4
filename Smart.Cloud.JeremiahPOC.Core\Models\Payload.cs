﻿
namespace Smart.Cloud.JeremiahPOC.Core.Models;

public class Payload
{
    public EmployeeRequest EmployeeRequest { get; set; }

    public Payload()
    {
        EmployeeRequest = new EmployeeRequest();
    }

    public Payload(EmployeeRequest employee) : this()
    {
        EmployeeRequest = employee;
    }
}

// TODO: Replace this type with your own type
public class EmployeeRequest
{
        public string EmployeeId { get; set; } = string.Empty;
        public string FirstName { get; set; } = string.Empty;
        public string LastName { get; set; } = string.Empty;
        public DateTime HiringDate { get; set; }
        public string InputType { get; set; } = string.Empty;
}
