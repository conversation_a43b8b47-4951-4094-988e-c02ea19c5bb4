﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2013/05/nuspec.xsd">
  <metadata>
    <id>Grpc.Auth</id>
    <version>2.66.0</version>
    <authors>The gRPC Authors</authors>
    <license type="expression">Apache-2.0</license>
    <licenseUrl>https://licenses.nuget.org/Apache-2.0</licenseUrl>
    <icon>packageIcon.png</icon>
    <readme>README.md</readme>
    <projectUrl>https://github.com/grpc/grpc-dotnet</projectUrl>
    <description>gRPC C# Authentication Library</description>
    <copyright>Copyright 2019 The gRPC Authors</copyright>
    <tags>gRPC RPC HTTP/2 Auth OAuth2</tags>
    <repository type="git" url="https://github.com/grpc/grpc-dotnet.git" commit="64e87a676b6aba16945f70aed3bb1c1dbbbbbad0" />
    <dependencies>
      <group targetFramework=".NETFramework4.6.2">
        <dependency id="Grpc.Core.Api" version="2.66.0" exclude="Build,Analyzers" />
        <dependency id="Google.Apis.Auth" version="1.68.0" exclude="Build,Analyzers" />
      </group>
      <group targetFramework=".NETStandard2.0">
        <dependency id="Grpc.Core.Api" version="2.66.0" exclude="Build,Analyzers" />
        <dependency id="Google.Apis.Auth" version="1.68.0" exclude="Build,Analyzers" />
      </group>
    </dependencies>
    <frameworkAssemblies>
      <frameworkAssembly assemblyName="Microsoft.CSharp" targetFramework=".NETFramework4.6.2" />
      <frameworkAssembly assemblyName="System" targetFramework=".NETFramework4.6.2" />
    </frameworkAssemblies>
  </metadata>
</package>