<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<package xmlns="http://schemas.microsoft.com/packaging/2013/05/nuspec.xsd">
  <metadata>
    <id>Dapper</id>
    <version>2.1.28</version>
    <title>Dapper</title>
    <authors><PERSON>,<PERSON>,<PERSON></authors>
    <owners><PERSON>,<PERSON>,<PERSON></owners>
    <requireLicenseAcceptance>false</requireLicenseAcceptance>
    <license type="expression">Apache-2.0</license>
    <licenseUrl>https://licenses.nuget.org/Apache-2.0</licenseUrl>
    <projectUrl>https://github.com/DapperLib/Dapper</projectUrl>
    <description>A high performance Micro-ORM supporting SQL Server, MySQL, Sqlite, SqlCE, Firebird etc..</description>
    <releaseNotes>https://dapperlib.github.io/Dapper/</releaseNotes>
    <copyright>2019 Stack Exchange, Inc.</copyright>
    <tags>orm sql micro-orm</tags>
    <repository type="git" url="https://github.com/DapperLib/Dapper" commit="d7c16035d2d6314bddcbdbbf6db4f35218f3ac9d" />
    <dependencies>
      <group targetFramework=".NETFramework4.6.1" />
      <group targetFramework=".NETFramework5.0" />
      <group targetFramework=".NETFramework7.0" />
      <group targetFramework=".NETStandard2.0">
        <dependency id="System.Reflection.Emit.Lightweight" version="4.7.0" exclude="Build,Analyzers" />
      </group>
    </dependencies>
    <icon>Dapper.png</icon>
    <readme>readme.md</readme>
  </metadata>
</package>