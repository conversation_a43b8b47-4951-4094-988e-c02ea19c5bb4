﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2013/05/nuspec.xsd">
  <metadata>
    <id>Google.Cloud.Logging.Type</id>
    <version>4.0.0</version>
    <authors>Google LLC</authors>
    <license type="file">LICENSE</license>
    <licenseUrl>https://aka.ms/deprecateLicenseUrl</licenseUrl>
    <icon>NuGetIcon.png</icon>
    <projectUrl>https://github.com/googleapis/google-cloud-dotnet</projectUrl>
    <iconUrl>https://cloud.google.com/images/gcp-icon-64x64.png</iconUrl>
    <description>Version-agnostic types for the Google Cloud Logging API.</description>
    <copyright>Copyright 2022 Google LLC</copyright>
    <tags>Logging Stackdriver Google Cloud</tags>
    <repository type="git" url="https://github.com/googleapis/google-cloud-dotnet" commit="d9ebb71fa12852a0dce19d9a3cd0c6c785fca663" />
    <dependencies>
      <group targetFramework=".NETFramework4.6.2">
        <dependency id="Google.Api.CommonProtos" version="[2.5.0, 3.0.0)" exclude="Build,Analyzers" />
      </group>
      <group targetFramework=".NETStandard2.1">
        <dependency id="Google.Api.CommonProtos" version="[2.5.0, 3.0.0)" exclude="Build,Analyzers" />
      </group>
    </dependencies>
  </metadata>
</package>