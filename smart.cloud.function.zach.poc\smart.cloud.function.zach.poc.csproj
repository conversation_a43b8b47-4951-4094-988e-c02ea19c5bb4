<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0</TargetFramework>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="13.0.1" />
    <PackageReference Include="Google.Cloud.Functions.Hosting" Version="2.2.1" />
    <PackageReference Include="Google.Events.Protobuf" Version="1.5.0" />
    <PackageReference Include="Microsoft.Extensions.Http" Version="8.0.0" />
    <PackageReference Include="Serilog" Version="4.0.2" />
    <PackageReference Include="Serilog.AspNetCore" Version="8.0.3" />
    <PackageReference Include="Serilog.Sinks.GoogleCloudLogging" Version="5.0.0" />
    <PackageReference Include="Smart.Cloud.Core.Logging" Version="2.0.0" />
    <PackageReference Include="Smart.Cloud.Core.PubSub" Version="1.1.1" />
    <None Include="appsettings*.json" CopyToOutputDirectory="PreserveNewest" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\smart.cloud.function.zach.poc.Core\smart.cloud.function.zach.poc.Core.csproj" />
    <ProjectReference Include="..\smart.cloud.function.zach.poc.Infrastructure\smart.cloud.function.zach.poc.Infrastructure.csproj" />
    <ProjectReference Include="..\smart.cloud.function.zach.poc.Services\smart.cloud.function.zach.poc.Services.csproj" />
  </ItemGroup>
</Project>
