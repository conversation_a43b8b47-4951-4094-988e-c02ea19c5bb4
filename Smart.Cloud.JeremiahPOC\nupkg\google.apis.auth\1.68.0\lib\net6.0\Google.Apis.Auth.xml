<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Google.Apis.Auth</name>
    </assembly>
    <members>
        <member name="T:Google.Apis.Auth.GoogleJsonWebSignature">
            <summary>
            Google JSON Web Signature as specified in https://developers.google.com/accounts/docs/OAuth2ServiceAccount.
            </summary>
        </member>
        <member name="M:Google.Apis.Auth.GoogleJsonWebSignature.ValidateAsync(System.String,Google.Apis.Util.IClock,System.Boolean)">
            <summary>
            Validates a Google-issued Json Web Token (JWT).
            Will throw a <see cref="T:Google.Apis.Auth.InvalidJwtException"/> if the passed value is not valid JWT signed by Google.
            </summary>
            <remarks>
            <para>Follows the procedure to
            <see href="https://developers.google.com/identity/protocols/OpenIDConnect#validatinganidtoken">validate a JWT ID token</see>.
            </para>
            <para>Google certificates are cached, and refreshed once per hour. This can be overridden by setting
            <paramref name="forceGoogleCertRefresh"/> to true.</para>
            </remarks>
            <param name="jwt">The JWT to validate.</param>
            <param name="clock">Optional. The <see cref="T:Google.Apis.Util.IClock"/> to use for JWT expiration verification. Defaults to the system clock.</param>
            <param name="forceGoogleCertRefresh">Optional. If true forces new certificates to be downloaded from Google. Defaults to false.</param>
            <returns>The JWT payload, if the JWT is valid. Throws an <see cref="T:Google.Apis.Auth.InvalidJwtException"/> otherwise.</returns>
            <exception cref="T:Google.Apis.Auth.InvalidJwtException">Thrown when passed a JWT that is not a valid JWT signed by Google.</exception>
        </member>
        <member name="T:Google.Apis.Auth.GoogleJsonWebSignature.ValidationSettings">
            <summary>
            Settings used when validating a JSON Web Signature.
            </summary>
        </member>
        <member name="M:Google.Apis.Auth.GoogleJsonWebSignature.ValidationSettings.#ctor">
            <summary>
            Create a new instance.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.GoogleJsonWebSignature.ValidationSettings.Audience">
            <summary>
            The trusted audience client IDs; or <c>null</c> to suppress audience validation.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.GoogleJsonWebSignature.ValidationSettings.HostedDomain">
            <summary>
            The required GSuite domain of the user; or <c>null</c> to suppress hosted domain validation.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.GoogleJsonWebSignature.ValidationSettings.Clock">
            <summary>
            Optional. The <see cref="T:Google.Apis.Util.IClock"/> to use for JWT expiration verification. Defaults to the system clock.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.GoogleJsonWebSignature.ValidationSettings.ForceGoogleCertRefresh">
            <summary>
            Optional. If true forces new certificates to be downloaded from Google. Defaults to false.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.GoogleJsonWebSignature.ValidationSettings.IssuedAtClockTolerance">
            <summary>
            Clock tolerance for the issued-at check.
            Causes a JWT to pass validation up to this duration before it is really valid;
            this is to allow for possible local-client clock skew. Defaults to 30 seconds.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.GoogleJsonWebSignature.ValidationSettings.ExpirationTimeClockTolerance">
            <summary>
            Clock tolerance for the expiration check.
            Causes a JWT to pass validation up to this duration after it really expired;
            this is to allow for possible local-client clock skew. Defaults to zero seconds.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.GoogleJsonWebSignature.ValidationSettings.CertificateCache">
            <summary>
            CertificateCache for testing purposes.
            If null, the default CertificateCache
            <see cref="F:Google.Apis.Auth.SignedTokenVerification.s_certificateCache"/> will
            be used.
            </summary>
        </member>
        <member name="M:Google.Apis.Auth.GoogleJsonWebSignature.ValidateAsync(System.String,Google.Apis.Auth.GoogleJsonWebSignature.ValidationSettings)">
            <summary>
            Validates a Google-issued Json Web Token (JWT).
            Will throw a <see cref="T:Google.Apis.Auth.InvalidJwtException"/> if the specified JWT fails any validation check.
            </summary>
            <remarks>
            <para>Follows the procedure to
            <see href="https://developers.google.com/identity/protocols/OpenIDConnect#validatinganidtoken">validate a JWT ID token</see>.
            </para>
            <para>
            Issued-at validation and expiry validation is performed using the clock on this local client,
            so local clock inaccuracies can lead to incorrect validation results.
            Use <see cref="P:Google.Apis.Auth.GoogleJsonWebSignature.ValidationSettings.IssuedAtClockTolerance"/> and <see cref="P:Google.Apis.Auth.GoogleJsonWebSignature.ValidationSettings.ExpirationTimeClockTolerance"/>
            to allow for local clock inaccuracy
            <c>IssuedAtClockTolerance</c> defaults to 30 seconds; it is very unlikely a JWT will be issued that isn't already valid.
            <c>ExpirationTimeClockTolerance</c> defaults to zero seconds; in some use-cases it may be useful to set this to a negative
            value to help ensure that passing local validation means it will pass server validation.
            Regardless of whether local validation passed, code must always correctly handle an invalid JWT error
            from the server.
            </para>
            <para>Google certificates are cached, and refreshed once per hour. This can be overridden by setting
            <see cref="P:Google.Apis.Auth.GoogleJsonWebSignature.ValidationSettings.ForceGoogleCertRefresh"/> to true.</para>
            </remarks>
            <param name="jwt">The JWT to validate.</param>
            <param name="validationSettings">Specifies how to carry out the validation.</param>
            <returns>The payload of the verified token.</returns>
            <exception cref="T:Google.Apis.Auth.InvalidJwtException">If the token does not pass verification.</exception>
        </member>
        <member name="T:Google.Apis.Auth.GoogleJsonWebSignature.Header">
            <summary>
            The header as specified in https://developers.google.com/accounts/docs/OAuth2ServiceAccount#formingheader.
            </summary>
        </member>
        <member name="T:Google.Apis.Auth.GoogleJsonWebSignature.Payload">
            <summary>
            The payload as specified in 
            https://developers.google.com/accounts/docs/OAuth2ServiceAccount#formingclaimset,
            https://developers.google.com/identity/protocols/OpenIDConnect, and
            https://openid.net/specs/openid-connect-core-1_0.html#StandardClaims
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.GoogleJsonWebSignature.Payload.Scope">
            <summary>
            A space-delimited list of the permissions the application requests or <c>null</c>.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.GoogleJsonWebSignature.Payload.Prn">
            <summary>
            The email address of the user for which the application is requesting delegated access.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.GoogleJsonWebSignature.Payload.HostedDomain">
            <summary>
            The hosted GSuite domain of the user. Provided only if the user belongs to a hosted domain.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.GoogleJsonWebSignature.Payload.Email">
            <summary>
            The user's email address. This may not be unique and is not suitable for use as a primary key.
            Provided only if your scope included the string "email".
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.GoogleJsonWebSignature.Payload.EmailVerified">
            <summary>
            True if the user's e-mail address has been verified; otherwise false.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.GoogleJsonWebSignature.Payload.Name">
            <summary>
            The user's full name, in a displayable form. Might be provided when:
            (1) The request scope included the string "profile"; or
            (2) The ID token is returned from a token refresh.
            When name claims are present, you can use them to update your app's user records.
            Note that this claim is never guaranteed to be present.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.GoogleJsonWebSignature.Payload.GivenName">
            <summary>
            Given name(s) or first name(s) of the End-User. Note that in some cultures, people can have multiple given names;
            all can be present, with the names being separated by space characters.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.GoogleJsonWebSignature.Payload.FamilyName">
            <summary>
            Surname(s) or last name(s) of the End-User. Note that in some cultures,
            people can have multiple family names or no family name;
            all can be present, with the names being separated by space characters.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.GoogleJsonWebSignature.Payload.Picture">
            <summary>
            The URL of the user's profile picture. Might be provided when:
            (1) The request scope included the string "profile"; or
            (2) The ID token is returned from a token refresh.
            When picture claims are present, you can use them to update your app's user records.
            Note that this claim is never guaranteed to be present.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.GoogleJsonWebSignature.Payload.Locale">
            <summary>
            End-User's locale, represented as a BCP47 [RFC5646] language tag.
            This is typically an ISO 639-1 Alpha-2 [ISO639‑1] language code in lowercase and an
            ISO 3166-1 Alpha-2 [ISO3166‑1] country code in uppercase, separated by a dash.
            For example, en-US or fr-CA.
            </summary>
        </member>
        <member name="T:Google.Apis.Auth.InvalidJwtException">
            <summary>
            An exception that is thrown when a Json Web Token (JWT) is invalid.
            </summary>
        </member>
        <member name="M:Google.Apis.Auth.InvalidJwtException.#ctor(System.String)">
            <summary>
            Initializes a new InvalidJwtException instanc e with the specified error message.
            </summary>
            <param name="message">The error message that explains why the JWT was invalid.</param>
        </member>
        <member name="T:Google.Apis.Auth.JsonWebSignature">
            <summary>
            JSON Web Signature (JWS) implementation as specified in 
            http://tools.ietf.org/html/draft-ietf-jose-json-web-signature-11.
            </summary>
        </member>
        <member name="M:Google.Apis.Auth.JsonWebSignature.VerifySignedTokenAsync(System.String,Google.Apis.Auth.SignedTokenVerificationOptions,System.Threading.CancellationToken)">
            <summary>
            Verifies that the given token is a valid, not expired, signed token.
            </summary>
            <param name="signedJwt">The token to verify.</param>
            <param name="options">The options to use for verification.
            May be null in which case default options will be used.</param>
            <param name="cancellationToken">The cancellation token for the operation.</param>
            <returns>The payload contained by the token.</returns>
            <exception cref="T:Google.Apis.Auth.InvalidJwtException">If the token is invalid or expired.</exception>
        </member>
        <member name="M:Google.Apis.Auth.JsonWebSignature.VerifySignedTokenAsync``1(System.String,Google.Apis.Auth.SignedTokenVerificationOptions,System.Threading.CancellationToken)">
            <summary>
            Verifies that the given token is a valid, not expired, signed token.
            </summary>
            <param name="signedJwt">The token to verify.</param>
            <param name="options">The options to use for verification.
            May be null in which case default options will be used.</param>
            <param name="cancellationToken">The cancellation token for the operation.</param>
            <returns>The payload contained by the token.</returns>
            <exception cref="T:Google.Apis.Auth.InvalidJwtException">If the token is invalid or expired.</exception>
            <typeparam name="TPayload">The type of the payload to return, so user code can validate
            additional claims. Should extend <see cref="T:Google.Apis.Auth.JsonWebSignature.Payload"/>. Payload information will be deserialized
            using <see cref="P:Google.Apis.Json.NewtonsoftJsonSerializer.Instance"/>.</typeparam>
        </member>
        <member name="T:Google.Apis.Auth.JsonWebSignature.Header">
            <summary>
            Header as specified in http://tools.ietf.org/html/draft-ietf-jose-json-web-signature-11#section-4.1.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.JsonWebSignature.Header.Algorithm">
            <summary>
            Gets or set the algorithm header parameter that identifies the cryptographic algorithm used to secure 
            the JWS or <c>null</c>.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.JsonWebSignature.Header.JwkUrl">
            <summary>
            Gets or sets the JSON Web Key URL header parameter that is an absolute URL that refers to a resource 
            for a set of JSON-encoded public keys, one of which corresponds to the key that was used to digitally 
            sign the JWS or <c>null</c>.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.JsonWebSignature.Header.Jwk">
            <summary>
            Gets or sets JSON Web Key header parameter that is a public key that corresponds to the key used to 
            digitally sign the JWS or <c>null</c>.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.JsonWebSignature.Header.KeyId">
            <summary>
            Gets or sets key ID header parameter that is a hint indicating which specific key owned by the signer 
            should be used to validate the digital signature or <c>null</c>.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.JsonWebSignature.Header.X509Url">
            <summary>
            Gets or sets X.509 URL header parameter that is an absolute URL that refers to a resource for the X.509
            public key certificate or certificate chain corresponding to the key used to digitally sign the JWS or 
            <c>null</c>.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.JsonWebSignature.Header.X509Thumbprint">
            <summary>
            Gets or sets X.509 certificate thumb print header parameter that provides a base64url encoded SHA-1 
            thumb-print (a.k.a. digest) of the DER encoding of an X.509 certificate that can be used to match the 
            certificate or <c>null</c>.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.JsonWebSignature.Header.X509Certificate">
            <summary>
            Gets or sets X.509 certificate chain header parameter contains the X.509 public key certificate or 
            certificate chain corresponding to the key used to digitally sign the JWS or <c>null</c>.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.JsonWebSignature.Header.critical">
            <summary>
            Gets or sets array listing the header parameter names that define extensions that are used in the JWS 
            header that MUST be understood and processed or <c>null</c>.
            </summary>
        </member>
        <member name="T:Google.Apis.Auth.JsonWebSignature.Payload">
            <summary>JWS Payload.</summary>
        </member>
        <member name="T:Google.Apis.Auth.JsonWebToken">
            <summary>
            JSON Web Token (JWT) implementation as specified in 
            http://tools.ietf.org/html/draft-ietf-oauth-json-web-token-08.
            </summary>
        </member>
        <member name="T:Google.Apis.Auth.JsonWebToken.Header">
            <summary>
            JWT Header as specified in http://tools.ietf.org/html/draft-ietf-oauth-json-web-token-08#section-5.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.JsonWebToken.Header.Type">
            <summary>
            Gets or sets type header parameter used to declare the type of this object or <c>null</c>.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.JsonWebToken.Header.ContentType">
            <summary>
            Gets or sets content type header parameter used to declare structural information about the JWT or 
            <c>null</c>.
            </summary>
        </member>
        <member name="T:Google.Apis.Auth.JsonWebToken.Payload">
            <summary>
            JWT Payload as specified in http://tools.ietf.org/html/draft-ietf-oauth-json-web-token-08#section-4.1.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.JsonWebToken.Payload.Issuer">
            <summary>
            Gets or sets issuer claim that identifies the principal that issued the JWT or <c>null</c>.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.JsonWebToken.Payload.Subject">
            <summary>
            Gets or sets subject claim identifying the principal that is the subject of the JWT or <c>null</c>.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.JsonWebToken.Payload.Audience">
            <summary>
            Gets or sets audience claim that identifies the audience that the JWT is intended for (should either be
            a string or list) or <c>null</c>.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.JsonWebToken.Payload.TargetAudience">
            <summary>
            Gets or sets the target audience claim that identifies the audience that an OIDC token generated from
            this JWT is intended for. Maybe be null. Multiple target audiences are not supported.
            <c>null</c>.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.JsonWebToken.Payload.ExpirationTimeSeconds">
            <summary>
            Gets or sets expiration time claim that identifies the expiration time (in seconds) on or after which 
            the token MUST NOT be accepted for processing or <c>null</c>.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.JsonWebToken.Payload.NotBeforeTimeSeconds">
            <summary>
            Gets or sets not before claim that identifies the time (in seconds) before which the token MUST NOT be
            accepted for processing or <c>null</c>.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.JsonWebToken.Payload.IssuedAtTimeSeconds">
            <summary>
            Gets or sets issued at claim that identifies the time (in seconds) at which the JWT was issued or 
            <c>null</c>.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.JsonWebToken.Payload.JwtId">
            <summary>
            Gets or sets JWT ID claim that provides a unique identifier for the JWT or <c>null</c>.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.JsonWebToken.Payload.Nonce">
            <summary>
            The nonce value specified by the client during the authorization request.
            Must be present if a nonce was specified in the authorization request, otherwise this will not be present.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.JsonWebToken.Payload.Type">
            <summary>
            Gets or sets type claim that is used to declare a type for the contents of this JWT Claims Set or 
            <c>null</c>.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.JsonWebToken.Payload.AudienceAsList">
            <summary>Gets the audience property as a list.</summary>
        </member>
        <member name="T:Google.Apis.Auth.OAuth2.AccessTokenCredential">
            <summary>
            Represents a credential that simply wraps an access token.
            The origin of said access token is not relevant, but that means
            that the credential cannot refresh the access token when it has expired.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.AccessTokenCredential.QuotaProject">
            <inheritdoc/>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.AccessTokenCredential.Google#Apis#Auth#OAuth2#IGoogleCredential#HasExplicitScopes">
            <inheritdoc/>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.AccessTokenCredential.Google#Apis#Auth#OAuth2#IGoogleCredential#SupportsExplicitScopes">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.AccessTokenCredential.Google#Apis#Auth#OAuth2#IGoogleCredential#GetUniverseDomainAsync(System.Threading.CancellationToken)">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.AccessTokenCredential.Google#Apis#Auth#OAuth2#IGoogleCredential#GetUniverseDomain">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.AccessTokenCredential.Google#Apis#Auth#OAuth2#IGoogleCredential#WithQuotaProject(System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.AccessTokenCredential.Google#Apis#Auth#OAuth2#IGoogleCredential#MaybeWithScopes(System.Collections.Generic.IEnumerable{System.String})">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.AccessTokenCredential.Google#Apis#Auth#OAuth2#IGoogleCredential#WithUserForDomainWideDelegation(System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.AccessTokenCredential.Google#Apis#Auth#OAuth2#IGoogleCredential#WithHttpClientFactory(Google.Apis.Http.IHttpClientFactory)">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.AccessTokenCredential.Google#Apis#Auth#OAuth2#IGoogleCredential#WithUniverseDomain(System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.AccessTokenCredential.GetAccessTokenWithHeadersForRequestAsync(System.String,System.Threading.CancellationToken)">
            <inheritdoc />
        </member>
        <member name="T:Google.Apis.Auth.OAuth2.AccessTokenWithHeaders">
            <summary>
            Represents an access token that can be used to authorize a request.
            The token might be accompanied by extra information that should be sent
            in the form of headers.
            </summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.AccessTokenWithHeaders.#ctor(System.String,System.Collections.Generic.IReadOnlyDictionary{System.String,System.Collections.Generic.IReadOnlyList{System.String}})">
            <summary>
            Constructs an <see cref="T:Google.Apis.Auth.OAuth2.AccessTokenWithHeaders"/> based on a given token and headers.
            </summary>
            <param name="token">The token to build this instance for. May be null.</param>
            <param name="headers">The collection of headers that may accompany the token. May be null.</param>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.AccessTokenWithHeaders.AccessToken">
            <summary>
            An access token that can be used to authorize a request.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.AccessTokenWithHeaders.Headers">
            <summary>
            Extra headers, if any, that should be included in the request.
            </summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.AccessTokenWithHeaders.AddHeaders(System.Net.Http.Headers.HttpRequestHeaders)">
            <summary>
             Adds the headers in this object to the given header collection.
            </summary>
            <param name="requestHeaders">The header collection to add the headers to.</param>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.AccessTokenWithHeaders.AddHeaders(System.Net.Http.HttpRequestMessage)">
            <summary>
             Adds the headers in this object to the given request.
            </summary>
            <param name="request">The request to add the headers to.</param>
        </member>
        <member name="T:Google.Apis.Auth.OAuth2.AccessTokenWithHeaders.Builder">
            <summary>
            Builder class for <see cref="T:Google.Apis.Auth.OAuth2.AccessTokenWithHeaders"/> to simplify common scenarios.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.AccessTokenWithHeaders.Builder.QuotaProject">
            <summary>
            The GCP project ID used for quota and billing purposes. May be null.
            </summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.AccessTokenWithHeaders.Builder.Build(System.String)">
            <summary>
            Builds and instance of <see cref="T:Google.Apis.Auth.OAuth2.AccessTokenWithHeaders"/> with the given
            token and the value set on this builder.
            </summary>
            <param name="token">The token to build the <see cref="T:Google.Apis.Auth.OAuth2.AccessTokenWithHeaders"/> for.</param>
            <returns>An <see cref="T:Google.Apis.Auth.OAuth2.AccessTokenWithHeaders"/>.</returns>
        </member>
        <member name="T:Google.Apis.Auth.OAuth2.AuthorizationCodeInstalledApp">
            <summary>
            Thread-safe OAuth 2.0 authorization code flow for an installed application that persists end-user credentials.
            </summary>
            <remarks>
            Incremental authorization (https://developers.google.com/+/web/api/rest/oauth) is currently not supported
            for Installed Apps.
            </remarks>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.AuthorizationCodeInstalledApp.#ctor(Google.Apis.Auth.OAuth2.Flows.IAuthorizationCodeFlow,Google.Apis.Auth.OAuth2.ICodeReceiver)">
            <summary>
            Constructs a new authorization code installed application with the given flow and code receiver.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.AuthorizationCodeInstalledApp.Flow">
            <summary>Gets the authorization code flow.</summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.AuthorizationCodeInstalledApp.CodeReceiver">
            <summary>Gets the code receiver which is responsible for receiving the authorization code.</summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.AuthorizationCodeInstalledApp.AuthorizeAsync(System.String,System.Threading.CancellationToken)">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.AuthorizationCodeInstalledApp.ShouldRequestAuthorizationCode(Google.Apis.Auth.OAuth2.Responses.TokenResponse)">
            <summary>
            Determines the need for retrieval of a new authorization code, based on the given token and the 
            authorization code flow.
            </summary>
        </member>
        <member name="T:Google.Apis.Auth.OAuth2.AuthorizationCodeInstalledApp.NoOpPckeAuthorizationFlow">
            <summary>
            Helper class to wrap non PKCE flows so that <see cref="T:Google.Apis.Auth.OAuth2.AuthorizationCodeInstalledApp"/>
            does not need to know whether its flow supports PKCE or not.
            </summary>
        </member>
        <member name="T:Google.Apis.Auth.OAuth2.AwsExternalAccountCredential">
            <summary>
            AWS credentials as described in
            https://google.aip.dev/auth/4117#determining-the-subject-token-in-aws.
            </summary>
        </member>
        <member name="T:Google.Apis.Auth.OAuth2.AwsExternalAccountCredential.AwsMetadataServerClient">
            <summary>
            Helper class for querying the AWS Metadata Server.
            It will fetch and use the sesion token if required.
            </summary>
        </member>
        <member name="T:Google.Apis.Auth.OAuth2.AwsExternalAccountCredential.AwsRegion">
            <summary>
            Helper class to obtain the AWS region.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.AwsExternalAccountCredential.AwsRegion.Region">
            <summary>
            The region obtained from stripping the last character of the zone value
            return by the metadata server. For instance, if the metadata server returned
            zone us-east-1d, then this value will be us-east-1.
            Will never be null, but may be empty if the metadata server returned a single
            character value.
            </summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.AwsExternalAccountCredential.AwsRegion.FetchAsync(Google.Apis.Auth.OAuth2.AwsExternalAccountCredential.AwsMetadataServerClient,System.String)">
            <summary>
            Fetches the AWS instance region as per https://google.aip.dev/auth/4117#determining-the-subject-token-in-aws.
            </summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.AwsExternalAccountCredential.AwsRegion.MaybeFromEnvVars">
            <summary>
            Attempts to fetch the region from environment variables.
            Returns null if the environment variables are not set.
            </summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.AwsExternalAccountCredential.AwsRegion.MaybeFromMetadataAsync(Google.Apis.Auth.OAuth2.AwsExternalAccountCredential.AwsMetadataServerClient,System.String)">
            <summary>
            Attempts to fetch the region from the metadata server.
            Returns null if the region URL is null or empty.
            </summary>
        </member>
        <member name="T:Google.Apis.Auth.OAuth2.AwsExternalAccountCredential.AwsSecurityCredentials">
            <summary>
            Represents AWS security credentials which are used to sign
            the subject token.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.AwsExternalAccountCredential.AwsSecurityCredentials.AccessKeyId">
            <summary>
            The access key ID. Won't be null or empty.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.AwsExternalAccountCredential.AwsSecurityCredentials.SecretAccessKey">
            <summary>
            The secret access key. Won't be null or empty.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.AwsExternalAccountCredential.AwsSecurityCredentials.Token">
            <summary>
            The credential token. May be null but won't be empty.
            </summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.AwsExternalAccountCredential.AwsSecurityCredentials.FetchAsync(Google.Apis.Auth.OAuth2.AwsExternalAccountCredential.AwsMetadataServerClient,System.String)">
            <summary>
            Fetches the AWS security credentials as per https://google.aip.dev/auth/4117#determining-the-subject-token-in-aws.
            </summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.AwsExternalAccountCredential.AwsSecurityCredentials.MaybeFromEnvVars">
            <summary>
            Attempts to fetch the security credentials from environment variables.
            Returns null if the environment variables are not set.
            </summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.AwsExternalAccountCredential.AwsSecurityCredentials.MaybeFromMetadataAsync(Google.Apis.Auth.OAuth2.AwsExternalAccountCredential.AwsMetadataServerClient,System.String)">
            <summary>
            Attempts to fetch the security credentials from the metadata server.
            Returns null if the credential URL is null or empty.
            </summary>
        </member>
        <member name="T:Google.Apis.Auth.OAuth2.AwsExternalAccountCredential.AwsSecurityCredentials.AwsSecurityCredentialsResponse">
            <summary>
            Partial representation of a metadata server security credentials response as defined by
            https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/iam-roles-for-amazon-ec2.html#instance-metadata-security-credentials
            </summary>
        </member>
        <member name="T:Google.Apis.Auth.OAuth2.AwsExternalAccountCredential.AwsSignedSubjectToken">
            <summary>
            Partial implementation of AWS signed request, enough to support signing
            of a, usually, AWS GetCallerIdentity request. The signed request is sent
            to Google's subject token service as the subject token to be exchanged for
            access tokens. Google STS triggers the request as specified by the signed
            request to verify the callers identity.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.AwsExternalAccountCredential.Initializer.RegionUrl">
            <summary>
            Metadata server URL used to obtained the region that should be included as part of
            the subject token.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.AwsExternalAccountCredential.Initializer.RegionalCredentialVerificationUrl">
            <summary>
            STS server will use this URL to validate the subject token included
            on the STS request. This URL will be included as part of the subject token.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.AwsExternalAccountCredential.Initializer.SecurityCredentialsUrl">
            <summary>
            Metadata server URL from which to obtain the security credentials that will
            be used to sign the subject token.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.AwsExternalAccountCredential.Initializer.ImdsV2SessionTokenUrl">
            <summary>
            If present, a session token fetched from this URL should be used when making
            requests to the metadata server.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.AwsExternalAccountCredential.RegionUrl">
            <summary>
            Metadata server URL used to obtained the region that should be included as part of
            the subject token.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.AwsExternalAccountCredential.RegionalCredentialVerificationUrl">
            <summary>
            STS server will use this URL to validate the subject token included
            on the STS request. This URL will be included as part of the subject token.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.AwsExternalAccountCredential.SecurityCredentialsUrl">
            <summary>
            Metadata server URL from which to obtain the security credentials that will
            be used to sign the subject token.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.AwsExternalAccountCredential.ImdsV2SessionTokenUrl">
            <summary>
            If present, a session token fetched from this URL should be used when making
            requests to the metadata server.
            </summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.AwsExternalAccountCredential.WithoutImpersonationConfigurationImpl">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.AwsExternalAccountCredential.GetSubjectTokenAsyncImpl(System.Threading.CancellationToken)">
            <inheritdoc/>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.AwsExternalAccountCredential.Google#Apis#Auth#OAuth2#IGoogleCredential#QuotaProject">
            <inheritdoc/>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.AwsExternalAccountCredential.Google#Apis#Auth#OAuth2#IGoogleCredential#HasExplicitScopes">
            <inheritdoc/>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.AwsExternalAccountCredential.Google#Apis#Auth#OAuth2#IGoogleCredential#SupportsExplicitScopes">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.AwsExternalAccountCredential.Google#Apis#Auth#OAuth2#IGoogleCredential#GetUniverseDomainAsync(System.Threading.CancellationToken)">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.AwsExternalAccountCredential.Google#Apis#Auth#OAuth2#IGoogleCredential#GetUniverseDomain">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.AwsExternalAccountCredential.Google#Apis#Auth#OAuth2#IGoogleCredential#WithQuotaProject(System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.AwsExternalAccountCredential.Google#Apis#Auth#OAuth2#IGoogleCredential#MaybeWithScopes(System.Collections.Generic.IEnumerable{System.String})">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.AwsExternalAccountCredential.Google#Apis#Auth#OAuth2#IGoogleCredential#WithUserForDomainWideDelegation(System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.AwsExternalAccountCredential.Google#Apis#Auth#OAuth2#IGoogleCredential#WithHttpClientFactory(Google.Apis.Http.IHttpClientFactory)">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.AwsExternalAccountCredential.Google#Apis#Auth#OAuth2#IGoogleCredential#WithUniverseDomain(System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.AwsExternalAccountCredential.GetNonEmptyEnvVarValue(System.String)">
            <summary>
            Returns the value of the given environment variable. Returns null if the
            variable is unset or if it's set to the empty string.
            </summary>
        </member>
        <member name="T:Google.Apis.Auth.OAuth2.AwsCanonicalRequestUriExtensions">
            <summary>
            Helper class to use with some of the formatting required for AWS
            canonical requests:
            https://docs.aws.amazon.com/general/latest/gr/sigv4-create-canonical-request.html
            </summary>
        </member>
        <member name="T:Google.Apis.Auth.OAuth2.BearerToken">
            <summary>
            OAuth 2.0 helper for accessing protected resources using the Bearer token as specified in
            http://tools.ietf.org/html/rfc6750.
            </summary>
        </member>
        <member name="T:Google.Apis.Auth.OAuth2.BearerToken.AuthorizationHeaderAccessMethod">
            <summary>
            Thread-safe OAuth 2.0 method for accessing protected resources using the Authorization header as specified 
            in http://tools.ietf.org/html/rfc6750#section-2.1.
            </summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.BearerToken.AuthorizationHeaderAccessMethod.Intercept(System.Net.Http.HttpRequestMessage,System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.BearerToken.AuthorizationHeaderAccessMethod.GetAccessToken(System.Net.Http.HttpRequestMessage)">
            <inheritdoc/>
        </member>
        <member name="T:Google.Apis.Auth.OAuth2.BearerToken.QueryParameterAccessMethod">
            <summary>
            Obsolete.
            Thread-safe OAuth 2.0 method for accessing protected resources using an <c>access_token</c> query parameter
            as specified in http://tools.ietf.org/html/rfc6750#section-2.3.
            This access method is being made obsolete. Please read here for more up to date information:
            `https://developers.google.com/identity/protocols/oauth2/index.html#4.-send-the-access-token-to-an-api.`.
            Please use <see cref="T:Google.Apis.Auth.OAuth2.BearerToken.AuthorizationHeaderAccessMethod"/> instead.
            </summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.BearerToken.QueryParameterAccessMethod.Intercept(System.Net.Http.HttpRequestMessage,System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.BearerToken.QueryParameterAccessMethod.GetAccessToken(System.Net.Http.HttpRequestMessage)">
            <inheritdoc/>
        </member>
        <member name="T:Google.Apis.Auth.OAuth2.ClientSecrets">
            <summary>Client credential details for installed and web applications.</summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.ClientSecrets.ClientId">
            <summary>Gets or sets the client identifier.</summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.ClientSecrets.ClientSecret">
            <summary>Gets or sets the client Secret.</summary>
        </member>
        <member name="T:Google.Apis.Auth.OAuth2.ComputeCredential">
            <summary>
            Google OAuth 2.0 credential for accessing protected resources using an access token. The Google OAuth 2.0 
            Authorization Server supports server-to-server interactions such as those between a web application and Google
            Cloud Storage. The requesting application has to prove its own identity to gain access to an API, and an 
            end-user doesn't have to be involved. 
            <para>
            More details about Compute Engine authentication is available at:
            https://cloud.google.com/compute/docs/authentication.
            </para>
            </summary>
        </member>
        <member name="F:Google.Apis.Auth.OAuth2.ComputeCredential.MetadataServerUrl">
            <summary>The metadata server url. This can be overridden (for the purposes of Compute environment detection and
            auth token retrieval) using the GCE_METADATA_HOST environment variable.</summary>
        </member>
        <member name="F:Google.Apis.Auth.OAuth2.ComputeCredential.isRunningOnComputeEngineCache">
            <summary>Caches result from first call to <c>IsRunningOnComputeEngine</c> </summary>
        </member>
        <member name="F:Google.Apis.Auth.OAuth2.ComputeCredential.MetadataServerPingTimeoutInMilliseconds">
            <summary>
            Originally 1000ms was used without a retry. This proved inadequate; even 2000ms without
            a retry occasionally failed. We have observed that after a timeout, the next attempt
            succeeds very quickly (sub-50ms) which suggests that this should be fine.
            </summary>
        </member>
        <member name="F:Google.Apis.Auth.OAuth2.ComputeCredential.MetadataFlavor">
            <summary>The Metadata flavor header name.</summary>
        </member>
        <member name="F:Google.Apis.Auth.OAuth2.ComputeCredential.GoogleMetadataHeader">
            <summary>The Metadata header response indicating Google.</summary>
        </member>
        <member name="F:Google.Apis.Auth.OAuth2.ComputeCredential._defaultServiceAccountEmailCache">
            <summary>
            Caches the task that fetches the default service account email from the metadata server.
            The default service account email can be cached because changing the service
            account associated to a Compute instance requires a machine shutdown.
            </summary>
        </member>
        <member name="F:Google.Apis.Auth.OAuth2.ComputeCredential._authenticatedHttpClient">
            <summary>
            HttpClient used to call APIs internally authenticated as this ComputeCredential.
            For instance, to perform IAM API calls for signing blobs of data.
            </summary>
            <remarks>Lazy to build one HtppClient only if it is needed.</remarks>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.ComputeCredential.OidcTokenUrl">
            <summary>
            Gets the OIDC Token URL.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.ComputeCredential.ExplicitUniverseDomain">
            <summary>
            The explicitly set universe domain.
            May be null, in which case the universe domain will be fetched from the metadata server.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.ComputeCredential.Google#Apis#Auth#OAuth2#IGoogleCredential#HasExplicitScopes">
            <inheritdoc/>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.ComputeCredential.Google#Apis#Auth#OAuth2#IGoogleCredential#SupportsExplicitScopes">
            <inheritdoc/>
        </member>
        <member name="T:Google.Apis.Auth.OAuth2.ComputeCredential.Initializer">
            <summary>
            An initializer class for the Compute credential. It uses <see cref="P:Google.Apis.Auth.OAuth2.GoogleAuthConsts.EffectiveComputeTokenUrl"/>
            as the token server URL (optionally overriding the host using the GCE_METADATA_HOST environment variable).
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.ComputeCredential.Initializer.OidcTokenUrl">
            <summary>
            Gets the OIDC Token URL.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.ComputeCredential.Initializer.UniverseDomain">
            <summary>
            The universe domain this credential belongs to.
            May be null, in which case the GCE universe domain will be used.
            </summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.ComputeCredential.Initializer.#ctor">
            <summary>Constructs a new initializer using the default compute token URL
            and the default OIDC token URL.</summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.ComputeCredential.Initializer.#ctor(System.String)">
            <summary>Constructs a new initializer using the given token URL
            and the default OIDC token URL.</summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.ComputeCredential.Initializer.#ctor(System.String,System.String)">
            <summary>Constructs a new initializer using the given token URL
            and OIDC token URL (optionally overriding the host using the GCE_METADATA_HOST environment variable).</summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.ComputeCredential.#ctor">
            <summary>Constructs a new Compute credential instance.</summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.ComputeCredential.#ctor(Google.Apis.Auth.OAuth2.ComputeCredential.Initializer)">
            <summary>Constructs a new Compute credential instance.</summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.ComputeCredential.Google#Apis#Auth#OAuth2#IGoogleCredential#GetUniverseDomainAsync(System.Threading.CancellationToken)">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.ComputeCredential.Google#Apis#Auth#OAuth2#IGoogleCredential#GetUniverseDomain">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.ComputeCredential.Google#Apis#Auth#OAuth2#IGoogleCredential#WithQuotaProject(System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.ComputeCredential.Google#Apis#Auth#OAuth2#IGoogleCredential#MaybeWithScopes(System.Collections.Generic.IEnumerable{System.String})">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.ComputeCredential.Google#Apis#Auth#OAuth2#IGoogleCredential#WithUserForDomainWideDelegation(System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.ComputeCredential.Google#Apis#Auth#OAuth2#IGoogleCredential#WithHttpClientFactory(Google.Apis.Http.IHttpClientFactory)">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.ComputeCredential.Google#Apis#Auth#OAuth2#IGoogleCredential#WithUniverseDomain(System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.ComputeCredential.GetDefaultServiceAccountEmailAsync(System.Threading.CancellationToken)">
            <summary>
            Returns a task whose result, when completed, is the default service account email associated to
            this Compute credential.
            </summary>
            <remarks>
            <para>
            This value is cached, because for changing the default service account associated to a
            Compute VM, the machine needs to be turned off. This means that the operation is only
            asynchronous when calling for the first time.
            </para>
            <para>
            Note that if, when fetching this value, an exception is thrown, the exception is cached and
            will be rethrown by the task returned by any future call to this method.
            You can create a new <see cref="T:Google.Apis.Auth.OAuth2.ComputeCredential"/> instance if that happens so fetching
            the service account default email is re-attempted.
            </para>
            </remarks>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.ComputeCredential.RequestAccessTokenAsync(System.Threading.CancellationToken)">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.ComputeCredential.GetOidcTokenAsync(Google.Apis.Auth.OAuth2.OidcTokenOptions,System.Threading.CancellationToken)">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.ComputeCredential.SignBlobAsync(System.Byte[],System.Threading.CancellationToken)">
            <summary>
            Signs the provided blob using the private key associated with the service account
            this ComputeCredential represents.
            </summary>
            <param name="blob">The blob to sign.</param>
            <param name="cancellationToken">Cancellation token to cancel the operation.</param>
            <returns>The base64 encoded signature.</returns>
            <exception cref="T:System.Net.Http.HttpRequestException">When the signing request fails.</exception>
            <exception cref="T:Newtonsoft.Json.JsonException">When the signing response is not valid JSON.</exception>
            <remarks>
            The private key associated with the Compute service account is not known locally
            by a ComputeCredential. Signing happens by executing a request to the IAM Credentials API
            which increases latency and counts towards IAM Credentials API quotas. Aditionally, the first
            time a ComputeCredential is used to sign data, a request to the metadata server is made to
            to obtain the email of the default Compute service account.
            </remarks>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.ComputeCredential.IsRunningOnComputeEngine">
            <summary>
            Detects if application is running on Google Compute Engine. This is achieved by attempting to contact
            GCE metadata server, that is only available on GCE. The check is only performed the first time you
            call this method, subsequent invocations used cached result of the first call.
            </summary>
        </member>
        <member name="T:Google.Apis.Auth.OAuth2.DefaultCredentialProvider">
            <summary>
            Provides the Application Default Credential from the environment. 
            An instance of this class represents the per-process state used to get and cache 
            the credential and allows overriding the state and environment for testing purposes.
            </summary>
        </member>
        <member name="F:Google.Apis.Auth.OAuth2.DefaultCredentialProvider.CredentialEnvironmentVariable">
            <summary>
            Environment variable override which stores the default application credentials file path.
            </summary>
        </member>
        <member name="F:Google.Apis.Auth.OAuth2.DefaultCredentialProvider.WellKnownCredentialsFile">
            <summary>Well known file which stores the default application credentials.</summary>
        </member>
        <member name="F:Google.Apis.Auth.OAuth2.DefaultCredentialProvider.AppdataEnvironmentVariable">
            <summary>Environment variable which contains the Application Data settings.</summary>
        </member>
        <member name="F:Google.Apis.Auth.OAuth2.DefaultCredentialProvider.HomeEnvironmentVariable">
            <summary>Environment variable which contains the location of home directory on UNIX systems.</summary>
        </member>
        <member name="F:Google.Apis.Auth.OAuth2.DefaultCredentialProvider.CloudSDKConfigDirectoryWindows">
            <summary>GCloud configuration directory in Windows, relative to %APPDATA%.</summary>
        </member>
        <member name="F:Google.Apis.Auth.OAuth2.DefaultCredentialProvider.HelpPermalink">
            <summary>Help link to the application default credentials feature.</summary>
        </member>
        <member name="F:Google.Apis.Auth.OAuth2.DefaultCredentialProvider.CloudSDKConfigDirectoryUnix">
            <summary>GCloud configuration directory on Linux/Mac, relative to $HOME.</summary>
        </member>
        <member name="F:Google.Apis.Auth.OAuth2.DefaultCredentialProvider.cachedCredentialTask">
            <summary>Caches result from first call to <c>GetApplicationDefaultCredentialAsync</c> </summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.DefaultCredentialProvider.#ctor">
            <summary>Constructs a new default credential provider.</summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.DefaultCredentialProvider.GetDefaultCredentialAsync">
            <summary>
            Returns the Application Default Credentials. Subsequent invocations return cached value from
            first invocation.
            See <see cref="M:Google.Apis.Auth.OAuth2.GoogleCredential.GetApplicationDefaultAsync"/> for details.
            </summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.DefaultCredentialProvider.CreateDefaultCredentialAsync">
            <summary>Creates a new default credential.</summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.DefaultCredentialProvider.CreateDefaultCredentialFromStream(System.IO.Stream)">
            <summary>Creates a default credential from a stream that contains JSON credential data.</summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.DefaultCredentialProvider.CreateDefaultCredentialFromStreamAsync(System.IO.Stream,System.Threading.CancellationToken)">
            <summary>Creates a default credential from a stream that contains JSON credential data.</summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.DefaultCredentialProvider.CreateDefaultCredentialFromJson(System.String)">
            <summary>Creates a default credential from a string that contains JSON credential data.</summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.DefaultCredentialProvider.CreateDefaultCredentialFromParameters(Google.Apis.Auth.OAuth2.JsonCredentialParameters)">
            <summary>Creates a default credential from JSON data.</summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.DefaultCredentialProvider.CreateUserCredentialFromParameters(Google.Apis.Auth.OAuth2.JsonCredentialParameters)">
            <summary>Creates a user credential from JSON data.</summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.DefaultCredentialProvider.CreateServiceAccountCredentialFromParameters(Google.Apis.Auth.OAuth2.JsonCredentialParameters)">
            <summary>Creates a <see cref="T:Google.Apis.Auth.OAuth2.ServiceAccountCredential"/> from JSON data.</summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.DefaultCredentialProvider.CreateExternalCredentialFromParameters(Google.Apis.Auth.OAuth2.JsonCredentialParameters)">
            <summary>
            Creates an external account credential from JSON data.
            </summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.DefaultCredentialProvider.GetWellKnownCredentialFilePath">
            <summary> 
            Returns platform-specific well known credential file path. This file is created by 
            <a href="https://cloud.google.com/sdk/gcloud/reference/auth/login">gcloud auth login</a>
            </summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.DefaultCredentialProvider.GetEnvironmentVariable(System.String)">
            <summary>
            Gets the environment variable. 
            This method is protected so it could be overriden for testing purposes only.
            </summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.DefaultCredentialProvider.GetStream(System.String)">
            <summary>
            Opens file as a stream.
            This method is protected so it could be overriden for testing purposes only.
            </summary>
        </member>
        <member name="T:Google.Apis.Auth.OAuth2.SubjectTokenException">
            <summary>
            Exception thrown when the subject token cannot be obtained for a given
            external account credential.
            </summary>
        </member>
        <member name="T:Google.Apis.Auth.OAuth2.ExternalAccountCredential">
            <summary>
            Base class for external account credentials.
            </summary>
        </member>
        <member name="T:Google.Apis.Auth.OAuth2.ExternalAccountCredential.Initializer">
            <summary>
            Initializer for <see cref="T:Google.Apis.Auth.OAuth2.ExternalAccountCredential"/>.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.ExternalAccountCredential.Initializer.Audience">
            <summary>
            The STS audience which contains the resource name for the
            workload identity pool or the workforce pool
            and the provider identifier in that pool.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.ExternalAccountCredential.Initializer.SubjectTokenType">
            <summary>
            The STS subject token type based on the OAuth 2.0 token exchange spec.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.ExternalAccountCredential.Initializer.ServiceAccountImpersonationUrl">
            <summary>
            This is the URL for the service account impersonation request.
            If this is not set, the STS-returned access token
            should be directly used without impersonation.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.ExternalAccountCredential.Initializer.WorkforcePoolUserProject">
            <summary>
            The GCP project number to be used for Workforce Identity Pools
            external credentials.
            </summary>
            <remarks>
            If this external account credential represents a Workforce Identity Pool
            enabled identity and this values is not specified, then an API key needs to be
            used alongside this credential to call Google APIs.
            </remarks>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.ExternalAccountCredential.Initializer.ClientId">
            <summary>
            The Client ID.
            </summary>
            <remarks>
            Client ID and client secret are currently only required if the token info endpoint
            needs to be called with the generated GCP access token.
            When provided, STS will be called with additional basic authentication using
            ClientId as username and ClientSecret as password.
            </remarks>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.ExternalAccountCredential.Initializer.ClientSecret">
            <summary>
            The client secret.
            </summary>
            <remarks>
            Client ID and client secret are currently only required if the token info endpoint
            needs to be called with the generated GCP access token.
            When provided, STS will be called with additional basic authentication using
            ClientId as username and ClientSecret as password.
            </remarks>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.ExternalAccountCredential.Initializer.UniverseDomain">
            <summary>
            The universe domain this credential belongs to.
            May be null, in which case the default universe domain will be used.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.ExternalAccountCredential.Audience">
            <summary>
            The STS audience which contains the resource name for the
            workload identity pool or the workforce pool
            and the provider identifier in that pool.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.ExternalAccountCredential.SubjectTokenType">
            <summary>
            The STS subject token type based on the OAuth 2.0 token exchange spec.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.ExternalAccountCredential.ServiceAccountImpersonationUrl">
            <summary>
            This is the URL for the service account impersonation request.
            If this is not set, the STS-returned access token
            should be directly used without impersonation.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.ExternalAccountCredential.WorkforcePoolUserProject">
            <summary>
            The GCP project number to be used for Workforce Pools
            external credentials.
            </summary>
            <remarks>
            If this external account credential represents a Workforce Pool
            enabled identity and this values is not specified, then an API key needs to be
            used alongside this credential to call Google APIs.
            </remarks>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.ExternalAccountCredential.ClientId">
            <summary>
            The Client ID.
            </summary>
            <remarks>
            Client ID and Client secret are currently only required if the token info endpoint
            needs to be called with the generated GCP access token.
            When provided, STS will be called with additional basic authentication using
            ClientId as username and ClientSecret as password.
            </remarks>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.ExternalAccountCredential.ClientSecret">
            <summary>
            The client secret.
            </summary>
            <remarks>
            Client ID and Client secret are currently only required if the token info endpoint
            needs to be called with the generated GCP access token.
            When provided, STS will be called with additional basic authentication using
            ClientId as username and ClientSecret as password.
            </remarks>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.ExternalAccountCredential.UniverseDomain">
            <summary>
            The universe domain this credential belogns to.
            Won't be null.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.ExternalAccountCredential.SupportsExplicitScopes">
            <summary>
            Returns true if this credential allows explicit scopes to be set
            via this library.
            Returns false otherwise.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.ExternalAccountCredential.WithoutImpersonationConfiguration">
            <summary>
            If <see cref="P:Google.Apis.Auth.OAuth2.ExternalAccountCredential.ServiceAccountImpersonationUrl"/> is set, returns a <see cref="T:Google.Apis.Auth.OAuth2.GoogleCredential"/> based on this
            one, but with <see cref="P:Google.Apis.Auth.OAuth2.ExternalAccountCredential.ServiceAccountImpersonationUrl"/> set to null. Otherwise returns a <see cref="T:Google.Apis.Auth.OAuth2.GoogleCredential"/>
            based on this one.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.ExternalAccountCredential.ImplicitlyImpersonated">
            <summary>
            If <see cref="P:Google.Apis.Auth.OAuth2.ExternalAccountCredential.ServiceAccountImpersonationUrl"/> is set, returns an <see cref="T:Google.Apis.Auth.OAuth2.ImpersonatedCredential"/>
            whose source credential is <see cref="P:Google.Apis.Auth.OAuth2.ExternalAccountCredential.WithoutImpersonationConfiguration"/>.
            Otherwise returns null.
            </summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.ExternalAccountCredential.WithoutImpersonationConfigurationImpl">
            <summary>
            If <see cref="P:Google.Apis.Auth.OAuth2.ExternalAccountCredential.ServiceAccountImpersonationUrl"/> is set, returns a <see cref="T:Google.Apis.Auth.OAuth2.GoogleCredential"/> based on this
            one, but with <see cref="P:Google.Apis.Auth.OAuth2.ExternalAccountCredential.ServiceAccountImpersonationUrl"/> set to null. Otherwise returns a <see cref="T:Google.Apis.Auth.OAuth2.GoogleCredential"/>
            based on this one.
            </summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.ExternalAccountCredential.ImplicitlyImpersonatedImpl">
            <summary>
            If <see cref="P:Google.Apis.Auth.OAuth2.ExternalAccountCredential.ServiceAccountImpersonationUrl"/> is set, returns an <see cref="T:Google.Apis.Auth.OAuth2.ImpersonatedCredential"/>
            whose source credential is <see cref="P:Google.Apis.Auth.OAuth2.ExternalAccountCredential.WithoutImpersonationConfiguration"/>.
            Otherwise returns null.
            </summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.ExternalAccountCredential.GetSubjectTokenAsyncImpl(System.Threading.CancellationToken)">
            <summary>
            Gets the subject token to be exchanged for the access token.
            </summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.ExternalAccountCredential.RequestAccessTokenAsync(System.Threading.CancellationToken)">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.ExternalAccountCredential.WithUserForDomainWideDelegation(System.String)">
            <summary>
            Throws <see cref="T:System.InvalidOperationException"/> as <see cref="T:Google.Apis.Auth.OAuth2.ExternalAccountCredential"/> does not
            support domain wide delegation.
            </summary>
        </member>
        <member name="T:Google.Apis.Auth.OAuth2.FileSourcedExternalAccountCredential">
            <summary>
            File-sourced credentials as described in
            https://google.aip.dev/auth/4117#determining-the-subject-token-in-file-sourced-credentials.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.FileSourcedExternalAccountCredential.Initializer.SubjectTokenFilePath">
            <summary>
            The file from which to obtain the subject token.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.FileSourcedExternalAccountCredential.Initializer.SubjectTokenJsonFieldName">
            <summary>
            If set, the subject token file content will be parsed as JSON and the
            value in the field with name <see cref="P:Google.Apis.Auth.OAuth2.FileSourcedExternalAccountCredential.Initializer.SubjectTokenJsonFieldName"/>
            will be returned as the subject token.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.FileSourcedExternalAccountCredential.SubjectTokenFilePath">
            <summary>
            The file path from which to obtain the subject token.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.FileSourcedExternalAccountCredential.SubjectTokenJsonFieldName">
            <summary>
            If set, the subject token file content will be parsed as JSON and the
            value in the field with name <see cref="P:Google.Apis.Auth.OAuth2.FileSourcedExternalAccountCredential.SubjectTokenJsonFieldName"/>
            will be returned as the subject token.
            </summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.FileSourcedExternalAccountCredential.WithoutImpersonationConfigurationImpl">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.FileSourcedExternalAccountCredential.GetSubjectTokenAsyncImpl(System.Threading.CancellationToken)">
            <inheritdoc/>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.FileSourcedExternalAccountCredential.Google#Apis#Auth#OAuth2#IGoogleCredential#QuotaProject">
            <inheritdoc/>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.FileSourcedExternalAccountCredential.Google#Apis#Auth#OAuth2#IGoogleCredential#HasExplicitScopes">
            <inheritdoc/>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.FileSourcedExternalAccountCredential.Google#Apis#Auth#OAuth2#IGoogleCredential#SupportsExplicitScopes">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.FileSourcedExternalAccountCredential.Google#Apis#Auth#OAuth2#IGoogleCredential#GetUniverseDomainAsync(System.Threading.CancellationToken)">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.FileSourcedExternalAccountCredential.Google#Apis#Auth#OAuth2#IGoogleCredential#GetUniverseDomain">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.FileSourcedExternalAccountCredential.Google#Apis#Auth#OAuth2#IGoogleCredential#WithQuotaProject(System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.FileSourcedExternalAccountCredential.Google#Apis#Auth#OAuth2#IGoogleCredential#MaybeWithScopes(System.Collections.Generic.IEnumerable{System.String})">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.FileSourcedExternalAccountCredential.Google#Apis#Auth#OAuth2#IGoogleCredential#WithUserForDomainWideDelegation(System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.FileSourcedExternalAccountCredential.Google#Apis#Auth#OAuth2#IGoogleCredential#WithHttpClientFactory(Google.Apis.Http.IHttpClientFactory)">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.FileSourcedExternalAccountCredential.Google#Apis#Auth#OAuth2#IGoogleCredential#WithUniverseDomain(System.String)">
            <inheritdoc/>
        </member>
        <member name="T:Google.Apis.Auth.OAuth2.Flows.AuthorizationCodeFlow">
            <summary>
            Thread-safe OAuth 2.0 authorization code flow that manages and persists end-user credentials.
            <para>
            This is designed to simplify the flow in which an end-user authorizes the application to access their protected
            data, and then the application has access to their data based on an access token and a refresh token to refresh 
            that access token when it expires.
            </para>
            </summary>
        </member>
        <member name="T:Google.Apis.Auth.OAuth2.Flows.AuthorizationCodeFlow.Initializer">
            <summary>An initializer class for the authorization code flow. </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Flows.AuthorizationCodeFlow.Initializer.AccessMethod">
            <summary>
            Gets or sets the method for presenting the access token to the resource server.
            The default value is
            <see cref="T:Google.Apis.Auth.OAuth2.BearerToken.AuthorizationHeaderAccessMethod"/>.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Flows.AuthorizationCodeFlow.Initializer.TokenServerUrl">
            <summary>Gets the token server URL.</summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Flows.AuthorizationCodeFlow.Initializer.AuthorizationServerUrl">
            <summary>Gets or sets the authorization server URL.</summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Flows.AuthorizationCodeFlow.Initializer.ClientSecrets">
            <summary>Gets or sets the client secrets which includes the client identifier and its secret.</summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Flows.AuthorizationCodeFlow.Initializer.ClientSecretsStream">
            <summary>
            Gets or sets the client secrets stream which contains the client identifier and its secret.
            </summary>
            <remarks>The AuthorizationCodeFlow constructor is responsible for disposing the stream.</remarks>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Flows.AuthorizationCodeFlow.Initializer.DataStore">
            <summary>Gets or sets the data store used to store the token response.</summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Flows.AuthorizationCodeFlow.Initializer.Scopes">
            <summary>
            Gets or sets the scopes which indicate the API access your application is requesting.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Flows.AuthorizationCodeFlow.Initializer.HttpClientFactory">
            <summary>
            Gets or sets the factory for creating <see cref="T:System.Net.Http.HttpClient"/> instance.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Flows.AuthorizationCodeFlow.Initializer.DefaultExponentialBackOffPolicy">
            <summary>
            Get or sets the exponential back-off policy. Default value is  <c>UnsuccessfulResponse503</c>, which 
            means that exponential back-off is used on 503 abnormal HTTP responses.
            If the value is set to <c>None</c>, no exponential back-off policy is used, and it's up to user to
            configure the <see cref="T:Google.Apis.Http.ConfigurableMessageHandler"/> in an
            <see cref="T:Google.Apis.Http.IConfigurableHttpClientInitializer"/> to set a specific back-off
            implementation (using <see cref="T:Google.Apis.Http.BackOffHandler"/>).
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Flows.AuthorizationCodeFlow.Initializer.Clock">
            <summary>
            Gets or sets the clock. The clock is used to determine if the token has expired, if so we will try to
            refresh it. The default value is <see cref="F:Google.Apis.Util.SystemClock.Default"/>.
            </summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.Flows.AuthorizationCodeFlow.Initializer.#ctor(System.String,System.String)">
            <summary>Constructs a new initializer.</summary>
            <param name="authorizationServerUrl">Authorization server URL</param>
            <param name="tokenServerUrl">Token server URL</param>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.Flows.AuthorizationCodeFlow.Initializer.#ctor(Google.Apis.Auth.OAuth2.Flows.AuthorizationCodeFlow)">
            <summary>
            Constructs a new initializer from the given <see cref="T:Google.Apis.Auth.OAuth2.Flows.AuthorizationCodeFlow"/>
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Flows.AuthorizationCodeFlow.TokenServerUrl">
            <summary>Gets the token server URL.</summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Flows.AuthorizationCodeFlow.AuthorizationServerUrl">
            <summary>Gets the authorization code server URL.</summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Flows.AuthorizationCodeFlow.ClientSecrets">
            <summary>Gets the client secrets which includes the client identifier and its secret.</summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Flows.AuthorizationCodeFlow.DataStore">
            <summary>Gets the data store used to store the credentials.</summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Flows.AuthorizationCodeFlow.Scopes">
            <summary>Gets the scopes which indicate the API access your application is requesting.</summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Flows.AuthorizationCodeFlow.HttpClient">
            <summary>Gets the HTTP client used to make authentication requests to the server.</summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.Flows.AuthorizationCodeFlow.#ctor(Google.Apis.Auth.OAuth2.Flows.AuthorizationCodeFlow.Initializer)">
            <summary>Constructs a new flow using the initializer's properties.</summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.Flows.AuthorizationCodeFlow.Google#Apis#Auth#OAuth2#Flows#IHttpAuthorizationFlow#WithHttpClientFactory(Google.Apis.Http.IHttpClientFactory)">
            <inheritdoc/>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Flows.AuthorizationCodeFlow.AccessMethod">
            <inheritdoc/>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Flows.AuthorizationCodeFlow.Clock">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.Flows.AuthorizationCodeFlow.LoadTokenAsync(System.String,System.Threading.CancellationToken)">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.Flows.AuthorizationCodeFlow.DeleteTokenAsync(System.String,System.Threading.CancellationToken)">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.Flows.AuthorizationCodeFlow.CreateAuthorizationCodeRequest(System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.Flows.AuthorizationCodeFlow.ExchangeCodeForTokenAsync(System.String,System.String,System.String,System.Threading.CancellationToken)">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.Flows.AuthorizationCodeFlow.CreateAuthorizationCodeTokenRequest(System.String,System.String,System.String)">
            <summary>
            Creates a <see cref="T:Google.Apis.Auth.OAuth2.Requests.AuthorizationCodeTokenRequest"/> for the given parameters.
            </summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.Flows.AuthorizationCodeFlow.ExchangeCodeForTokenAsync(System.String,Google.Apis.Auth.OAuth2.Requests.AuthorizationCodeTokenRequest,System.Threading.CancellationToken)">
            <summary>
            Executes <paramref name="authorizationCodeTokenReq"/> and stores and returns the received token.
            </summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.Flows.AuthorizationCodeFlow.RefreshTokenAsync(System.String,System.String,System.Threading.CancellationToken)">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.Flows.AuthorizationCodeFlow.RevokeTokenAsync(System.String,System.String,System.Threading.CancellationToken)">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.Flows.AuthorizationCodeFlow.ShouldForceTokenRetrieval">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.Flows.AuthorizationCodeFlow.StoreTokenAsync(System.String,Google.Apis.Auth.OAuth2.Responses.TokenResponse,System.Threading.CancellationToken)">
            <summary>Stores the token in the <see cref="P:Google.Apis.Auth.OAuth2.Flows.AuthorizationCodeFlow.DataStore"/>.</summary>
            <param name="userId">User identifier.</param>
            <param name="token">Token to store.</param>
            <param name="taskCancellationToken">Cancellation token to cancel operation.</param>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.Flows.AuthorizationCodeFlow.FetchTokenAsync(System.String,Google.Apis.Auth.OAuth2.Requests.TokenRequest,System.Threading.CancellationToken)">
            <summary>Retrieve a new token from the server using the specified request.</summary>
            <param name="userId">User identifier.</param>
            <param name="request">Token request.</param>
            <param name="taskCancellationToken">Cancellation token to cancel operation.</param>
            <returns>Token response with the new access token.</returns>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.Flows.AuthorizationCodeFlow.Dispose">
            <inheritdoc/>
        </member>
        <member name="T:Google.Apis.Auth.OAuth2.Flows.GoogleAuthorizationCodeFlow">
            <summary>
            Google specific authorization code flow which inherits from <see cref="T:Google.Apis.Auth.OAuth2.Flows.AuthorizationCodeFlow"/>.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Flows.GoogleAuthorizationCodeFlow.ProjectId">
            <summary>
            The project ID associated with the credential using this flow.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Flows.GoogleAuthorizationCodeFlow.RevokeTokenUrl">
            <summary>Gets the token revocation URL.</summary>
        </member>
        <member name="F:Google.Apis.Auth.OAuth2.Flows.GoogleAuthorizationCodeFlow.includeGrantedScopes">
            <summary>Gets the include granted scopes indicator.
            Do not use, use <see cref="P:Google.Apis.Auth.OAuth2.Flows.GoogleAuthorizationCodeFlow.IncludeGrantedScopes"/> instead.</summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Flows.GoogleAuthorizationCodeFlow.IncludeGrantedScopes">
            <summary>Gets the include granted scopes indicator.</summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Flows.GoogleAuthorizationCodeFlow.LoginHint">
            <summary>Gets the login_hint.</summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Flows.GoogleAuthorizationCodeFlow.Prompt">
            <summary>
            Gets the prompt for consent behaviour.
            Value can be <c>null</c>, <c>"none"</c>, <c>"consent"</c>, or <c>"select_account"</c>.
            See <a href="https://developers.google.com/identity/protocols/OpenIDConnect#prompt">OpenIDConnect documentation</a>
            for details.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Flows.GoogleAuthorizationCodeFlow.Nonce">
            <summary>Gets the nonce.</summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Flows.GoogleAuthorizationCodeFlow.UserDefinedQueryParams">
            <summary>Gets the user defined query parameters.</summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.Flows.GoogleAuthorizationCodeFlow.#ctor(Google.Apis.Auth.OAuth2.Flows.GoogleAuthorizationCodeFlow.Initializer)">
            <summary>Constructs a new Google authorization code flow.</summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.Flows.GoogleAuthorizationCodeFlow.Google#Apis#Auth#OAuth2#Flows#IHttpAuthorizationFlow#WithHttpClientFactory(Google.Apis.Http.IHttpClientFactory)">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.Flows.GoogleAuthorizationCodeFlow.CreateAuthorizationCodeRequest(System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.Flows.GoogleAuthorizationCodeFlow.RevokeTokenAsync(System.String,System.String,System.Threading.CancellationToken)">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.Flows.GoogleAuthorizationCodeFlow.ShouldForceTokenRetrieval">
            <inheritdoc/>
        </member>
        <member name="T:Google.Apis.Auth.OAuth2.Flows.GoogleAuthorizationCodeFlow.Initializer">
            <summary>An initializer class for Google authorization code flow. </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Flows.GoogleAuthorizationCodeFlow.Initializer.ProjectId">
            <summary>
            The project ID associated with the credential using this flow.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Flows.GoogleAuthorizationCodeFlow.Initializer.RevokeTokenUrl">
            <summary>Gets or sets the token revocation URL.</summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Flows.GoogleAuthorizationCodeFlow.Initializer.IncludeGrantedScopes">
            <summary>
            Gets or sets the optional indicator for including granted scopes for incremental authorization.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Flows.GoogleAuthorizationCodeFlow.Initializer.LoginHint">
            <summary>Gets or sets the login_hint.</summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Flows.GoogleAuthorizationCodeFlow.Initializer.Prompt">
            <summary>
            Gets or sets the prompt for consent behaviour.
            Value can be <c>null</c>, <c>"none"</c>, <c>"consent"</c>, or <c>"select_account"</c>.
            See <a href="https://developers.google.com/identity/protocols/OpenIDConnect#prompt">OpenIDConnect documentation</a>
            for details.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Flows.GoogleAuthorizationCodeFlow.Initializer.Nonce">
            <summary>Gets or sets the nonce.</summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Flows.GoogleAuthorizationCodeFlow.Initializer.UserDefinedQueryParams">
            <summary>Gets or sets the optional user defined query parameters.</summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.Flows.GoogleAuthorizationCodeFlow.Initializer.#ctor">
            <summary>
            Constructs a new initializer. Sets Authorization server URL to 
            <see cref="F:Google.Apis.Auth.OAuth2.GoogleAuthConsts.OidcAuthorizationUrl"/>, and Token server URL to 
            <see cref="F:Google.Apis.Auth.OAuth2.GoogleAuthConsts.OidcTokenUrl"/>.
            </summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.Flows.GoogleAuthorizationCodeFlow.Initializer.#ctor(Google.Apis.Auth.OAuth2.Flows.GoogleAuthorizationCodeFlow)">
            <summary>
            Constructs a new initializer from the given <see cref="T:Google.Apis.Auth.OAuth2.Flows.GoogleAuthorizationCodeFlow"/>.
            </summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.Flows.GoogleAuthorizationCodeFlow.Initializer.#ctor(System.String,System.String,System.String)">
            <summary>Constructs a new initializer.</summary>
            <param name="authorizationServerUrl">Authorization server URL</param>
            <param name="tokenServerUrl">Token server URL</param>
            <param name="revokeTokenUrl">Revocation server URL</param>
            <remarks>
            This is mainly for internal testing at Google, where we occasionally need
            to use alternative oauth endpoints. This is not for general use.
            </remarks>
        </member>
        <member name="T:Google.Apis.Auth.OAuth2.Flows.IAuthorizationCodeFlow">
            <summary>OAuth 2.0 authorization code flow that manages and persists end-user credentials.</summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Flows.IAuthorizationCodeFlow.AccessMethod">
            <summary>Gets the method for presenting the access token to the resource server.</summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Flows.IAuthorizationCodeFlow.Clock">
            <summary>Gets the clock.</summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Flows.IAuthorizationCodeFlow.DataStore">
            <summary>Gets the data store used to store the credentials.</summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.Flows.IAuthorizationCodeFlow.LoadTokenAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Asynchronously loads the user's token using the flow's
            <see cref="T:Google.Apis.Util.Store.IDataStore"/>.
            </summary>
            <param name="userId">User identifier</param>
            <param name="taskCancellationToken">Cancellation token to cancel operation</param>
            <returns>Token response</returns>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.Flows.IAuthorizationCodeFlow.DeleteTokenAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Asynchronously deletes the user's token using the flow's
            <see cref="T:Google.Apis.Util.Store.IDataStore"/>.
            </summary>
            <param name="userId">User identifier.</param>
            <param name="taskCancellationToken">Cancellation token to cancel operation.</param>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.Flows.IAuthorizationCodeFlow.CreateAuthorizationCodeRequest(System.String)">
            <summary>Creates an authorization code request with the specified redirect URI.</summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.Flows.IAuthorizationCodeFlow.ExchangeCodeForTokenAsync(System.String,System.String,System.String,System.Threading.CancellationToken)">
            <summary>Asynchronously exchanges an authorization code for an access token.</summary>
            <param name="userId">User identifier.</param>
            <param name="code">Authorization code received from the authorization server.</param>
            <param name="redirectUri">Redirect URI which is used in the token request.</param>
            <param name="taskCancellationToken">Cancellation token to cancel operation.</param>
            <returns>Token response which contains the access token.</returns>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.Flows.IAuthorizationCodeFlow.RefreshTokenAsync(System.String,System.String,System.Threading.CancellationToken)">
            <summary>Asynchronously refreshes an access token using a refresh token.</summary>
            <param name="userId">User identifier.</param>
            <param name="refreshToken">Refresh token which is used to get a new access token.</param>
            <param name="taskCancellationToken">Cancellation token to cancel operation.</param>
            <returns>Token response which contains the access token and the input refresh token.</returns>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.Flows.IAuthorizationCodeFlow.RevokeTokenAsync(System.String,System.String,System.Threading.CancellationToken)">
            <summary>
            Asynchronously revokes the specified token. This method disconnects the user's account from the OAuth 2.0
            application. It should be called upon removing the user account from the site.</summary>
            <remarks>
            If revoking the token succeeds, the user's credential is removed from the data store and the user MUST
            authorize the application again before the application can access the user's private resources.
            </remarks>
            <param name="userId">User identifier.</param>
            <param name="token">Access token to be revoked.</param>
            <param name="taskCancellationToken">Cancellation token to cancel operation.</param>
            <returns><c>true</c> if the token was revoked successfully.</returns>        
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.Flows.IAuthorizationCodeFlow.ShouldForceTokenRetrieval">
            <summary>
            Indicates if a new token needs to be retrieved and stored regardless of normal circumstances.
            </summary>
        </member>
        <member name="T:Google.Apis.Auth.OAuth2.Flows.IHttpAuthorizationFlow">
            <summary>
            Authorization flow that performs HTTP operations, for instance,
            for obtaining or refreshing tokens.
            </summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.Flows.IHttpAuthorizationFlow.WithHttpClientFactory(Google.Apis.Http.IHttpClientFactory)">
            <summary>
            Return a new instance of the same type as this but that uses the
            given HTTP client factory.
            </summary>
            <param name="httpClientFactory">The http client factory to be used by the new instance.
            May be null, in which case the default <see cref="T:Google.Apis.Http.HttpClientFactory"/> will be used.</param>
            <returns>A new instance with the same type as this but that will use <paramref name="httpClientFactory"/>
            to obtain an <see cref="T:Google.Apis.Http.ConfigurableHttpClient"/> to be used for token related operations.</returns>
        </member>
        <member name="T:Google.Apis.Auth.OAuth2.Flows.IPkceAuthorizationCodeFlow">
            <summary>
            Authorization flow that supports Proof Key for Code Exchange (PKCE)
            as described in https://www.rfc-editor.org/rfc/rfc7636.
            </summary>
            <remarks>
            If you are writing your own authorization flow to be used with <see cref="T:Google.Apis.Auth.OAuth2.AuthorizationCodeInstalledApp"/>
            make sure you implement this interface if you need to support PKCE.
            See https://developers.google.com/identity/protocols/oauth2/native-app for how Google supports PKCE.
            </remarks>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.Flows.IPkceAuthorizationCodeFlow.CreateAuthorizationCodeRequest(System.String,System.String@)">
            <summary>
            Creates an authorization code request with the specified redirect URI.
            </summary>
            <param name="redirectUri">
            The redirect URI for the authorization code request.
            </param>
            <param name="codeVerifier">
            The code verifier associated to the code challenge that should be included
            in the returned <see cref="T:Google.Apis.Auth.OAuth2.Requests.AuthorizationCodeRequestUrl"/>. Note this is an <code>out</code> parameter.
            </param>
            <returns>An <see cref="T:Google.Apis.Auth.OAuth2.Requests.AuthorizationCodeRequestUrl"/> subclass instance that includes the code challenge
            and code challenge method associated with <paramref name="codeVerifier"/>.</returns>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.Flows.IPkceAuthorizationCodeFlow.ExchangeCodeForTokenAsync(System.String,System.String,System.String,System.String,System.Threading.CancellationToken)">
            <summary>Asynchronously exchanges an authorization code for an access token.</summary>
            <param name="userId">User identifier.</param>
            <param name="code">Authorization code received from the authorization server.</param>
            <param name="codeVerifier">
            The PKCE code verifier to include in the exchange request.
            When called by the authentication library, this will be the same value specified by the
            <code>codeVerifier</code> out parameter in an earlier call to CreateAuthorizationCodeRequest.
            </param>
            <param name="redirectUri">Redirect URI which is used in the token request.</param>
            <param name="taskCancellationToken">Cancellation token to cancel operation.</param>
            <returns>Token response which contains the access token.</returns>
        </member>
        <member name="T:Google.Apis.Auth.OAuth2.Flows.PkceGoogleAuthorizationCodeFlow">
            <summary>
            Google authorization flow implementation that supports PKCE as described in https://www.rfc-editor.org/rfc/rfc7636
            and https://developers.google.com/identity/protocols/oauth2/native-app.
            </summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.Flows.PkceGoogleAuthorizationCodeFlow.#ctor(Google.Apis.Auth.OAuth2.Flows.GoogleAuthorizationCodeFlow.Initializer)">
            <summary>
            Creates a new instance from the given initializer.
            </summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.Flows.PkceGoogleAuthorizationCodeFlow.CreateAuthorizationCodeRequest(System.String,System.String@)">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.Flows.PkceGoogleAuthorizationCodeFlow.ExchangeCodeForTokenAsync(System.String,System.String,System.String,System.String,System.Threading.CancellationToken)">
            <inheritdoc/>
        </member>
        <member name="T:Google.Apis.Auth.OAuth2.GoogleAuthConsts">
            <summary>
            Google OAuth2 constants.
            Canonical source for these URLs is: https://accounts.google.com/.well-known/openid-configuration
            </summary>
        </member>
        <member name="F:Google.Apis.Auth.OAuth2.GoogleAuthConsts.AuthorizationUrl">
            <summary>The authorization code server URL.</summary>
        </member>
        <member name="F:Google.Apis.Auth.OAuth2.GoogleAuthConsts.OidcAuthorizationUrl">
            <summary>The OpenID Connect authorization code server URL.</summary>
            <remarks>
            Use of this <see cref="F:Google.Apis.Auth.OAuth2.GoogleAuthConsts.OidcAuthorizationUrl"/> is not 100% compatible with using
            <see cref="F:Google.Apis.Auth.OAuth2.GoogleAuthConsts.AuthorizationUrl"/>, so they are two distinct URLs.
            Internally within this library only this more up-to-date <see cref="F:Google.Apis.Auth.OAuth2.GoogleAuthConsts.OidcAuthorizationUrl"/> is used.
            </remarks>
        </member>
        <member name="F:Google.Apis.Auth.OAuth2.GoogleAuthConsts.ApprovalUrl">
            <summary>The approval URL (used in the Windows solution as a callback).</summary>
        </member>
        <member name="F:Google.Apis.Auth.OAuth2.GoogleAuthConsts.TokenUrl">
            <summary>The authorization token server URL.</summary>
        </member>
        <member name="F:Google.Apis.Auth.OAuth2.GoogleAuthConsts.OidcTokenUrl">
            <summary>The OpenID Connect authorization token server URL.</summary>
            <remarks>
            Use of this <see cref="F:Google.Apis.Auth.OAuth2.GoogleAuthConsts.OidcTokenUrl"/> is not 100% compatible with using
            <see cref="F:Google.Apis.Auth.OAuth2.GoogleAuthConsts.TokenUrl"/>, so they are two distinct URLs.
            Internally within this library only this more up-to-date <see cref="F:Google.Apis.Auth.OAuth2.GoogleAuthConsts.OidcTokenUrl"/> is used.
            </remarks>
        </member>
        <member name="F:Google.Apis.Auth.OAuth2.GoogleAuthConsts.ComputeTokenUrl">
            <summary>The Compute Engine authorization token server URL</summary>
            <remarks>IP address instead of name to avoid DNS resolution</remarks>
        </member>
        <member name="F:Google.Apis.Auth.OAuth2.GoogleAuthConsts.RevokeTokenUrl">
            <summary>The path to the Google revocation endpoint.</summary>
        </member>
        <member name="F:Google.Apis.Auth.OAuth2.GoogleAuthConsts.JsonWebKeySetUrl">
            <summary>The OpenID Connect Json Web Key Set (jwks) URL.</summary>
        </member>
        <member name="F:Google.Apis.Auth.OAuth2.GoogleAuthConsts.IapKeySetUrl">
            <summary>The IAP Json Web Key Set (jwks) URL.</summary>
        </member>
        <member name="F:Google.Apis.Auth.OAuth2.GoogleAuthConsts.LocalhostRedirectUri">
            <summary>Installed application localhost redirect URI.</summary>
        </member>
        <member name="F:Google.Apis.Auth.OAuth2.GoogleAuthConsts.IamServiceAccountEndpointCommonPrefixFormat">
            <summary>IAM access token endpoint for service account.</summary>
        </member>
        <member name="F:Google.Apis.Auth.OAuth2.GoogleAuthConsts.IamAccessTokenVerb">
            <summary>IAM access token verb.</summary>
        </member>
        <member name="F:Google.Apis.Auth.OAuth2.GoogleAuthConsts.IamAccessTokenEndpointFormatString">
            <summary>IAM access token endpoint format string. To use it insert the service account email.</summary>
        </member>
        <member name="F:Google.Apis.Auth.OAuth2.GoogleAuthConsts.IamSignEndpointFormatString">
            <summary>IAM signBlob endpoint format string. To use it insert the service account email.</summary>
        </member>
        <member name="F:Google.Apis.Auth.OAuth2.GoogleAuthConsts.IamIdTokenEndpointFormatString">
            <summary>IAM ID token endpoint format string. To use it insert the service account email.</summary>
        </member>
        <member name="F:Google.Apis.Auth.OAuth2.GoogleAuthConsts.IamScope">
            <summary>Scope needed for source credential in impersonated credential.</summary>
        </member>
        <member name="F:Google.Apis.Auth.OAuth2.GoogleAuthConsts.QuotaProjectEnvironmentVariable">
            <summary>
            Name of the environment variable that will be checked for an ambient quota project ID.
            If set, this value will be applied to Application Default Credentials.
            </summary>
        </member>
        <member name="F:Google.Apis.Auth.OAuth2.GoogleAuthConsts.DefaultUniverseDomain">
            <summary>
            The default universe domain.
            </summary>
        </member>
        <member name="F:Google.Apis.Auth.OAuth2.GoogleAuthConsts.UniverseDomainKey">
            <summary>
            Key for a universe domain in a <see cref="T:System.Net.Http.HttpRequestMessage"/> options.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.GoogleAuthConsts.EnvironmentQuotaProject">
            <summary>
            The non empty value set on <see cref="F:Google.Apis.Auth.OAuth2.GoogleAuthConsts.QuotaProjectEnvironmentVariable"/>, if any;
            null otherwise.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.GoogleAuthConsts.EffectiveComputeTokenUrl">
            <summary>
            The effective Compute Engine authorization token server URL.
            This takes account of the GCE_METADATA_HOST environment variable.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.GoogleAuthConsts.EffectiveComputeOidcTokenUrl">
            <summary>
            The effective Compute Engine authorization token server URL for OIDC. This requires an audience parameter to be added.
            This takes account of the GCE_METADATA_HOST environment variable.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.GoogleAuthConsts.EffectiveComputeDefaultServiceAccountEmailUrl">
            <summary>
            The effective Compute Engine default service account email URL.
            This takes account of the GCE_METADATA_HOST environment variable.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.GoogleAuthConsts.EffectiveComputeUniverDomainUrl">
            <summary>
            The effective Compute Engine universe domain URL.
            This takes account of the GCE_METADATA_HOST environment variable.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.GoogleAuthConsts.EffectiveMetadataServerUrl">
            <summary>
            The effective Compute Engine metadata token server URL (with no path).
            This takes account of the GCE_METADATA_HOST environment variable.
            </summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.GoogleAuthConsts.CheckIsDefaultUniverseDomain(System.String,System.String)">
            <summary>
            Throws <see cref="T:System.InvalidOperationException"/> with <paramref name="message"/>
            if <paramref name="universeDomain"/> is not the default universe domain.
            </summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.GoogleAuthConsts.CheckIsDefaultUniverseDomain(System.String,System.Boolean,System.String)">
            <summary>
            Throws <see cref="T:System.InvalidOperationException"/> with <paramref name="message"/>
            if <paramref name="universeDomain"/> is not the default universe domain and
            <paramref name="condition"/> is true.
            </summary>
        </member>
        <member name="T:Google.Apis.Auth.OAuth2.GoogleClientSecrets">
            <summary>
            OAuth 2.0 client secrets model as specified in https://cloud.google.com/console/.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.GoogleClientSecrets.Installed">
            <summary>Gets or sets the details for installed applications.</summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.GoogleClientSecrets.Web">
            <summary>Gets or sets the details for web applications.</summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.GoogleClientSecrets.Secrets">
            <summary>Gets the client secrets which contains the client identifier and client secret. </summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.GoogleClientSecrets.Load(System.IO.Stream)">
            <summary>Loads the Google client secret from the input stream.</summary>
            <remarks>This method has been made obsolete in favour of <see cref="M:Google.Apis.Auth.OAuth2.GoogleClientSecrets.FromStream(System.IO.Stream)"/>
            which only differs in name.</remarks>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.GoogleClientSecrets.FromStream(System.IO.Stream)">
            <summary>Loads the Google client secret from the input stream.</summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.GoogleClientSecrets.FromStreamAsync(System.IO.Stream,System.Threading.CancellationToken)">
            <summary>Asynchronously loads the Google client secret from the input stream.</summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.GoogleClientSecrets.FromFile(System.String)">
            <summary>Loads the Google client secret from a JSON file.</summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.GoogleClientSecrets.FromFileAsync(System.String,System.Threading.CancellationToken)">
            <summary>Asynchronously loads the Google client secret from a JSON file.</summary>
        </member>
        <member name="T:Google.Apis.Auth.OAuth2.GoogleCredential">
            <summary>
            Credential for authorizing calls using OAuth 2.0.
            It is a convenience wrapper that allows handling of different types of 
            credentials (like <see cref="T:Google.Apis.Auth.OAuth2.ServiceAccountCredential"/>, <see cref="T:Google.Apis.Auth.OAuth2.ComputeCredential"/>
            or <see cref="T:Google.Apis.Auth.OAuth2.UserCredential"/>) in a unified way.
            <para>
            See <see cref="M:Google.Apis.Auth.OAuth2.GoogleCredential.GetApplicationDefaultAsync(System.Threading.CancellationToken)"/> for the credential retrieval logic.
            </para>
            </summary>
        </member>
        <member name="F:Google.Apis.Auth.OAuth2.GoogleCredential.defaultCredentialProvider">
            <summary>Provider implements the logic for creating the application default credential.</summary>
        </member>
        <member name="F:Google.Apis.Auth.OAuth2.GoogleCredential.credential">
            <summary>The underlying credential being wrapped by this object.</summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.GoogleCredential.#ctor(Google.Apis.Auth.OAuth2.IGoogleCredential)">
            <summary>Creates a new <c>GoogleCredential</c>.</summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.GoogleCredential.GetApplicationDefaultAsync">
            <summary>
            Returns the Application Default Credentials which are ambient credentials that identify and authorize
            the whole application. See <see cref="M:Google.Apis.Auth.OAuth2.GoogleCredential.GetApplicationDefaultAsync(System.Threading.CancellationToken)"/> for more details.
            </summary>
            <returns>A task which completes with the application default credentials.</returns>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.GoogleCredential.GetApplicationDefaultAsync(System.Threading.CancellationToken)">
            <summary>
            <para>Returns the Application Default Credentials which are ambient credentials that identify and authorize
            the whole application.</para>
            <para>The ambient credentials are determined as following order:</para>
            <list type="number">
            <item>
            <description>
            The environment variable GOOGLE_APPLICATION_CREDENTIALS is checked. If this variable is specified, it
            should point to a file that defines the credentials. The simplest way to get a credential for this purpose
            is to create a service account using the
            <a href="https://console.developers.google.com">Google Developers Console</a> in the section APIs &amp;
            Auth, in the sub-section Credentials. Create a service account or choose an existing one and select
            Generate new JSON key. Set the environment variable to the path of the JSON file downloaded.
            </description>
            </item>
            <item>
            <description>
            If you have installed the Google Cloud SDK on your machine and have run the command
            <a href="https://cloud.google.com/sdk/gcloud/reference/auth/login">GCloud Auth Login</a>, your identity can
            be used as a proxy to test code calling APIs from that machine.
            </description>
            </item>
            <item>
            <description>
            If you are running in Google Compute Engine production, the built-in service account associated with the
            virtual machine instance will be used.
            </description>
            </item>
            <item>
            <description>
            If all previous steps have failed, <c>InvalidOperationException</c> is thrown.
            </description>
            </item>
            </list>
            </summary>
            <remarks>
            If the cancellation token is cancelled while the underlying operation is loading Application Default Credentials,
            the underlying operation will still be used for any further requests. No actual work is cancelled via this cancellation
            token; it just allows the returned task to transition to a cancelled state.
            </remarks>
            <param name="cancellationToken">Cancellation token for the operation.</param>
            <returns>A task which completes with the application default credentials.</returns>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.GoogleCredential.GetApplicationDefault">
            <summary>
            <para>Synchronously returns the Application Default Credentials which are ambient credentials that identify and authorize
            the whole application. See <see cref="M:Google.Apis.Auth.OAuth2.GoogleCredential.GetApplicationDefaultAsync(System.Threading.CancellationToken)"/> for details on application default credentials.</para>
            <para>This method will block until the credentials are available (or an exception is thrown).
            It is highly preferable to call <see cref="M:Google.Apis.Auth.OAuth2.GoogleCredential.GetApplicationDefaultAsync(System.Threading.CancellationToken)"/> where possible.</para>
            </summary>
            <returns>The application default credentials.</returns>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.GoogleCredential.FromStream(System.IO.Stream)">
            <summary>
            Loads credential from stream containing JSON credential data.
            <para>
            The stream can contain a Service Account key file in JSON format from the Google Developers
            Console or a stored user credential using the format supported by the Cloud SDK.
            </para>
            </summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.GoogleCredential.FromStreamAsync(System.IO.Stream,System.Threading.CancellationToken)">
            <summary>
            Loads credential from stream containing JSON credential data.
            <para>
            The stream can contain a Service Account key file in JSON format from the Google Developers
            Console or a stored user credential using the format supported by the Cloud SDK.
            </para>
            </summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.GoogleCredential.FromFile(System.String)">
            <summary>
            Loads credential from the specified file containing JSON credential data.
            <para>
            The file can contain a Service Account key file in JSON format from the Google Developers
            Console or a stored user credential using the format supported by the Cloud SDK.
            </para>
            </summary>
            <param name="path">The path to the credential file.</param>
            <returns>The loaded credentials.</returns>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.GoogleCredential.FromFileAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Loads credential from the specified file containing JSON credential data.
            <para>
            The file can contain a Service Account key file in JSON format from the Google Developers
            Console or a stored user credential using the format supported by the Cloud SDK.
            </para>
            </summary>
            <param name="path">The path to the credential file.</param>
            <param name="cancellationToken">Cancellation token for the operation.</param>
            <returns>The loaded credentials.</returns>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.GoogleCredential.FromJson(System.String)">
            <summary>
            Loads credential from a string containing JSON credential data.
            <para>
            The string can contain a Service Account key file in JSON format from the Google Developers
            Console or a stored user credential using the format supported by the Cloud SDK.
            </para>
            </summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.GoogleCredential.FromJsonParameters(Google.Apis.Auth.OAuth2.JsonCredentialParameters)">
            <summary>
            Loads a credential from JSON credential parameters. Fields are a union of credential fields
            for all supported types. <see cref="T:Google.Apis.Auth.OAuth2.JsonCredentialParameters"/> for more detailed information
            about supported types and corresponding fields.
            </summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.GoogleCredential.FromAccessToken(System.String,Google.Apis.Auth.OAuth2.IAccessMethod)">
            <summary>
            Create a <see cref="T:Google.Apis.Auth.OAuth2.GoogleCredential"/> directly from the provided access token.
            The access token will not be automatically refreshed.
            </summary>
            <param name="accessToken">The access token to use within this credential.</param>
            <param name="accessMethod">Optional. The <see cref="T:Google.Apis.Auth.OAuth2.IAccessMethod"/> to use within this credential.
            If <c>null</c>, will default to <see cref="T:Google.Apis.Auth.OAuth2.BearerToken.AuthorizationHeaderAccessMethod"/>.</param>
            <returns>A credential based on the provided access token.</returns>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.GoogleCredential.FromComputeCredential(Google.Apis.Auth.OAuth2.ComputeCredential)">
            <summary>
            Create a <see cref="T:Google.Apis.Auth.OAuth2.GoogleCredential"/> from a <see cref="T:Google.Apis.Auth.OAuth2.ComputeCredential"/>.
            In general, do not use this method. Call <see cref="M:Google.Apis.Auth.OAuth2.GoogleCredential.GetApplicationDefault"/> or
            <see cref="M:Google.Apis.Auth.OAuth2.GoogleCredential.GetApplicationDefaultAsync(System.Threading.CancellationToken)"/>, which will provide the most suitable
            credentials for the current platform.
            </summary>
            <param name="computeCredential">Optional. The compute credential to use in the returned <see cref="T:Google.Apis.Auth.OAuth2.GoogleCredential"/>.
            If <c>null</c>, then a new <see cref="T:Google.Apis.Auth.OAuth2.ComputeCredential"/> will be instantiated, using the default
            <see cref="T:Google.Apis.Auth.OAuth2.ComputeCredential.Initializer"/>.</param>
            <returns>A <see cref="T:Google.Apis.Auth.OAuth2.GoogleCredential"/> with an underlying <see cref="T:Google.Apis.Auth.OAuth2.ComputeCredential"/>.</returns>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.GoogleCredential.IsCreateScopedRequired">
            <summary>
            <para>
            Returns <c>true</c> only if this credential supports explicit scopes to be set
            via this library but no explicit scopes have been set.
            A credential with explicit scopes set
            may be created by calling <see cref="M:Google.Apis.Auth.OAuth2.GoogleCredential.CreateScoped(System.Collections.Generic.IEnumerable{System.String})"/>.
            </para>
            <para>
            For accessing Google services, credentials need to be scoped. Credentials 
            have some default scoping, but this library supports explicit scopes to be set
            for certain credentials.
            </para>
            <list type="number">
            <item>
            <description>
            <see cref="T:Google.Apis.Auth.OAuth2.ComputeCredential"/> is scoped by default but in some environments it may be scoped
            explicitly, for instance when running on GKE with Workload Identity or on AppEngine Flex.
            It's possible to create a <see cref="T:Google.Apis.Auth.OAuth2.ComputeCredential"/> with explicit scopes set by calling
            <see cref="M:Google.Apis.Auth.OAuth2.GoogleCredential.CreateScoped(System.Collections.Generic.IEnumerable{System.String})"/>. If running on an environment that does not
            accept explicit scoping, for instance GCE where scopes are set on the VM, explicit scopes
            will be ignored.
            </description>
            </item>
            <item>
            <description>
            <see cref="T:Google.Apis.Auth.OAuth2.UserCredential"/> is scoped by default, as scopes were obtained during the consent
            screen. It's not possible to change the default scopes of a <see cref="T:Google.Apis.Auth.OAuth2.UserCredential"/>.
            </description>
            </item>
            <item>
            <description>
            <see cref="T:Google.Apis.Auth.OAuth2.ServiceAccountCredential"/> is not scoped by default but when used without
            explicit scopes to access a Google service, the service's default scopes will be assumed.
            It's possible to create a <see cref="T:Google.Apis.Auth.OAuth2.ServiceAccountCredential"/> with explicit scopes set
            by calling <see cref="M:Google.Apis.Auth.OAuth2.GoogleCredential.CreateScoped(System.Collections.Generic.IEnumerable{System.String})"/>
            </description>
            </item>
            <item>
            <description>
            <see cref="T:Google.Apis.Auth.OAuth2.ImpersonatedCredential"/> is not scoped by default but when used without
            explicit scopes to access a Google service, the service's default scopes will be assumed.
            Note that the scopes of an <see cref="P:Google.Apis.Auth.OAuth2.ImpersonatedCredential.SourceCredential"/> have no
            bearings on the <see cref="T:Google.Apis.Auth.OAuth2.ImpersonatedCredential"/> scopes.
            It's possible to create an <see cref="T:Google.Apis.Auth.OAuth2.ImpersonatedCredential"/> with explicit scopes set
            by calling <see cref="M:Google.Apis.Auth.OAuth2.GoogleCredential.CreateScoped(System.Collections.Generic.IEnumerable{System.String})"/>
            </description>
            </item>
            </list>
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.GoogleCredential.QuotaProject">
            <summary>
            The ID of the project associated to this credential for the purposes of
            quota calculation and billing. May be null.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.GoogleCredential.UnderlyingCredential">
            <summary>
            Gets the underlying credential instance being wrapped.
            </summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.GoogleCredential.GetUniverseDomainAsync(System.Threading.CancellationToken)">
            <summary>
            Returns the universe domain this credential belongs to.
            </summary>
            <remarks>
            For most credential types, this operation is synchronous and will always
            return a completed task.
            For <see cref="T:Google.Apis.Auth.OAuth2.ComputeCredential"/>, the universe domain is obtained from the
            metadata server, which requires an HTTP call. This value is obtained only once,
            the first time it is requested for any instance of <see cref="T:Google.Apis.Auth.OAuth2.ComputeCredential"/>.
            Once the universe has been fetched this method will always return a completed task.
            The task's result will never be null.
            Note that each <paramref name="cancellationToken"/> will only apply to the call
            that provided it and not to subsequent calls. For instance, even if the first call
            to <see cref="M:Google.Apis.Auth.OAuth2.GoogleCredential.GetUniverseDomainAsync(System.Threading.CancellationToken)"/> is cancelled, subsequent
            calls may still succeed.
            </remarks>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.GoogleCredential.GetUniverseDomain">
            <summary>
            Returns the universe domain this credential belongs to.
            </summary>
            <remarks>
            Because <see cref="M:Google.Apis.Auth.OAuth2.GoogleCredential.GetUniverseDomainAsync(System.Threading.CancellationToken)"/> is truly async only once, at most, in the lifetime
            of an application, this method exists for convenience.
            It can always be safely used for all credential types except for <see cref="T:Google.Apis.Auth.OAuth2.ComputeCredential"/>.
            For <see cref="T:Google.Apis.Auth.OAuth2.ComputeCredential"/>, the universe domain is obtained from the
            metadata server, which requires an HTTP call. This value is obtained only once,
            the first time it is requested for any instance of <see cref="T:Google.Apis.Auth.OAuth2.ComputeCredential"/>.
            That first time, this method may block while waiting for the HTTP call to complete.
            After that, this method will always be safe to use.
            Will never return null.
            </remarks>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.GoogleCredential.CreateScoped(System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            If this library supports setting explicit scopes on this credential,
            this method will creates a copy of the credential with the specified scopes.
            Otherwise, it returns the same instance.
            See <see cref="P:Google.Apis.Auth.OAuth2.GoogleCredential.IsCreateScopedRequired"/> for more information.
            </summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.GoogleCredential.CreateScoped(System.String[])">
            <summary>
            If the credential supports scopes, creates a copy with the specified scopes. Otherwise, it returns the same
            instance.
            </summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.GoogleCredential.CreateWithUser(System.String)">
            <summary>
            If the credential supports Domain Wide Delegation, this method creates a copy of the credential
            with the specified user.
            Otherwise, it throws <see cref="T:System.InvalidOperationException"/>.
            At the moment only <see cref="T:Google.Apis.Auth.OAuth2.ServiceAccountCredential"/> supports Domain Wide Delegation.
            </summary>
            <param name="user">The user that the returned credential will be a delegate for.</param>
            <returns>A copy of this credential with the user set to <paramref name="user"/>.</returns>
            <exception cref="T:System.InvalidOperationException">When the credential type doesn't support
            Domain Wide Delegation.</exception>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.GoogleCredential.CreateWithQuotaProject(System.String)">
            <summary>
            Creates a copy of this credential with the specified quota project.
            </summary>
            <param name="quotaProject">The quota project to use for the copy. May be null.</param>
            <returns>A copy of this credential with <see cref="P:Google.Apis.Auth.OAuth2.GoogleCredential.QuotaProject"/> set to <paramref name="quotaProject"/>.</returns>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.GoogleCredential.CreateWithEnvironmentQuotaProject">
            <summary>
            Creates a copy of this credential with the ambient quota project as set in
            <see cref="F:Google.Apis.Auth.OAuth2.GoogleAuthConsts.QuotaProjectEnvironmentVariable"/>.
            If <see cref="F:Google.Apis.Auth.OAuth2.GoogleAuthConsts.QuotaProjectEnvironmentVariable"/> is not set, or if
            it is set to the empty value, this method returns this instance.
            </summary>
            <remarks>
            The ADC quota project value will be overwritten only if the environment variable is present
            and set to a non-empty value.
            If the environment variable is not present or if it is present but unset, the credential
            returned will maintain whatever quota project value it already had, i.e. the credential's
            quota project value will not be unset.
            </remarks>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.GoogleCredential.CreateWithHttpClientFactory(Google.Apis.Http.IHttpClientFactory)">
            <summary>
            Creates a copy of this credential with the specified HTTP client factory.
            </summary>
            <param name="factory">The HTTP client factory to be used by the new credential.
            May be null, in which case the default <see cref="T:Google.Apis.Http.HttpClientFactory"/> will be used.</param>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.GoogleCredential.CreateWithUniverseDomain(System.String)">
            <summary>
            If the credential supports custom universe domains this method will create a copy of the
            credential with the specified universe domain set.
            Otherwise, it throws <see cref="T:System.InvalidOperationException"/>.
            </summary>
            <param name="universeDomain">The universe domain to use for the credential.
            May be null, in which case the default universe domain will be used.</param>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.GoogleCredential.GetOidcTokenAsync(Google.Apis.Auth.OAuth2.OidcTokenOptions,System.Threading.CancellationToken)">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.GoogleCredential.SignBlobAsync(System.Byte[],System.Threading.CancellationToken)">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.GoogleCredential.Impersonate(Google.Apis.Auth.OAuth2.ImpersonatedCredential.Initializer)">
            <summary>
            Allows this credential to impersonate the <see cref="P:Google.Apis.Auth.OAuth2.ImpersonatedCredential.Initializer.TargetPrincipal"/>.
            Only <see cref="T:Google.Apis.Auth.OAuth2.ServiceAccountCredential"/> and <see cref="T:Google.Apis.Auth.OAuth2.UserCredential"/> support impersonation,
            so this method will throw <see cref="T:System.InvalidOperationException"/> if this credential's
            <see cref="P:Google.Apis.Auth.OAuth2.GoogleCredential.UnderlyingCredential"/> is not of one of those supported types.
            </summary>
            <param name="initializer">Initializer containing the configuration for the impersonated credential.</param>
            <remarks>
            For impersonation, a credential needs to be scoped to https://www.googleapis.com/auth/iam. When using a
            <see cref="T:Google.Apis.Auth.OAuth2.ServiceAccountCredential"/> as the source credential, this is not a problem, since the credential
            can be scoped on demand. When using a <see cref="T:Google.Apis.Auth.OAuth2.UserCredential"/> the credential needs to have been obtained
            with the required scope, else, when attempting and impersonated request, you'll receive an authorization error.
            </remarks>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.GoogleCredential.FromServiceAccountCredential(Google.Apis.Auth.OAuth2.ServiceAccountCredential)">
            <summary>Creates a <c>GoogleCredential</c> wrapping a <see cref="T:Google.Apis.Auth.OAuth2.ServiceAccountCredential"/>.</summary>
        </member>
        <member name="T:Google.Apis.Auth.OAuth2.GoogleWebAuthorizationBroker">
            <summary>A helper utility to manage the authorization code flow.</summary>
            <remarks>
            This class is only suitable for client-side use, as it starts a local browser that requires
            user interaction.
            Do not use this class when executing on a web server, or any cases where the authenticating
            end-user is not able to do directly interact with a launched browser.
            </remarks>
        </member>
        <member name="F:Google.Apis.Auth.OAuth2.GoogleWebAuthorizationBroker.Folder">
            <summary>The folder which is used by the <see cref="T:Google.Apis.Util.Store.FileDataStore"/>.</summary>
            <remarks>
            The reason that this is not 'private const' is that a user can change it and store the credentials in a
            different location.
            </remarks>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.GoogleWebAuthorizationBroker.AuthorizeAsync(Google.Apis.Auth.OAuth2.ClientSecrets,System.Collections.Generic.IEnumerable{System.String},System.String,System.Threading.CancellationToken,Google.Apis.Util.Store.IDataStore,Google.Apis.Auth.OAuth2.ICodeReceiver)">
            <summary>
            Asynchronously authorizes the specified user.
            Requires user interaction; see <see cref="T:Google.Apis.Auth.OAuth2.GoogleWebAuthorizationBroker"/> remarks for more details.
            </summary>
            <remarks>
            In case no data store is specified, <see cref="T:Google.Apis.Util.Store.FileDataStore"/> will be used by 
            default.
            </remarks>
            <param name="clientSecrets">The client secrets.</param>
            <param name="scopes">
            The scopes which indicate the Google API access your application is requesting.
            </param>
            <param name="user">The user to authorize.</param>
            <param name="taskCancellationToken">Cancellation token to cancel an operation.</param>
            <param name="dataStore">The data store, if not specified a file data store will be used.</param>
            <param name="codeReceiver">The code receiver, if not specified a local server code receiver will be used.</param>
            <returns>User credential.</returns>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.GoogleWebAuthorizationBroker.AuthorizeAsync(System.IO.Stream,System.Collections.Generic.IEnumerable{System.String},System.String,System.Threading.CancellationToken,Google.Apis.Util.Store.IDataStore,Google.Apis.Auth.OAuth2.ICodeReceiver)">
            <summary>
            Asynchronously authorizes the specified user.
            Requires user interaction; see <see cref="T:Google.Apis.Auth.OAuth2.GoogleWebAuthorizationBroker"/> remarks for more details.
            </summary>
            <remarks>
            In case no data store is specified, <see cref="T:Google.Apis.Util.Store.FileDataStore"/> will be used by 
            default.
            </remarks>
            <param name="clientSecretsStream">
            The client secrets stream. The authorization code flow constructor is responsible for disposing the stream.
            </param>
            <param name="scopes">
            The scopes which indicate the Google API access your application is requesting.
            </param>
            <param name="user">The user to authorize.</param>
            <param name="taskCancellationToken">Cancellation token to cancel an operation.</param>
            <param name="dataStore">The data store, if not specified a file data store will be used.</param>
            <param name="codeReceiver">The code receiver, if not specified a local server code receiver will be used.</param>
            <returns>User credential.</returns>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.GoogleWebAuthorizationBroker.ReauthorizeAsync(Google.Apis.Auth.OAuth2.UserCredential,System.Threading.CancellationToken,Google.Apis.Auth.OAuth2.ICodeReceiver)">
            <summary>
            Asynchronously reauthorizes the user. This method should be called if the users want to authorize after 
            they revoked the token.
            Requires user interaction; see <see cref="T:Google.Apis.Auth.OAuth2.GoogleWebAuthorizationBroker"/> remarks for more details.
            </summary>
            <param name="userCredential">The current user credential. Its <see cref="P:Google.Apis.Auth.OAuth2.UserCredential.Token"/> will be
            updated. </param>
            <param name="taskCancellationToken">Cancellation token to cancel an operation.</param>
            <param name="codeReceiver">The code receiver, if not specified a local server code receiver will be used.</param>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.GoogleWebAuthorizationBroker.AuthorizeAsync(Google.Apis.Auth.OAuth2.Flows.GoogleAuthorizationCodeFlow.Initializer,System.Collections.Generic.IEnumerable{System.String},System.String,System.Threading.CancellationToken,Google.Apis.Util.Store.IDataStore,Google.Apis.Auth.OAuth2.ICodeReceiver)">
            <summary>
            The core logic for asynchronously authorizing the specified user.
            Requires user interaction; see <see cref="T:Google.Apis.Auth.OAuth2.GoogleWebAuthorizationBroker"/> remarks for more details.
            </summary>
            <param name="initializer">The authorization code initializer.</param>
            <param name="scopes">
            The scopes which indicate the Google API access your application is requesting.
            </param>
            <param name="user">The user to authorize.</param>
            <param name="taskCancellationToken">Cancellation token to cancel an operation.</param>
            <param name="dataStore">The data store, if not specified a file data store will be used.</param>
            <param name="codeReceiver">The code receiver, if not specified a local server code receiver will be used.</param>
            <returns>User credential.</returns>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.GoogleWebAuthorizationBroker.AuthorizeAsync(Google.Apis.Auth.OAuth2.Flows.GoogleAuthorizationCodeFlow.Initializer,System.Collections.Generic.IEnumerable{System.String},System.String,System.Boolean,System.Threading.CancellationToken,Google.Apis.Util.Store.IDataStore,Google.Apis.Auth.OAuth2.ICodeReceiver)">
            <summary>
            The core logic for asynchronously authorizing the specified user.
            Requires user interaction; see <see cref="T:Google.Apis.Auth.OAuth2.GoogleWebAuthorizationBroker"/> remarks for more details.
            </summary>
            <param name="initializer">The authorization code initializer.</param>
            <param name="scopes">
            The scopes which indicate the Google API access your application is requesting.
            </param>
            <param name="user">The user to authorize.</param>
            <param name="usePkce">
            If true, PKCE will be used by the authorization flow. Note that using PKCE is recommended for security reasons.
            See https://developers.google.com/identity/protocols/oauth2/native-app for more information.
            </param>
            <param name="taskCancellationToken">Cancellation token to cancel an operation.</param>
            <param name="dataStore">The data store, if not specified a file data store will be used.</param>
            <param name="codeReceiver">The code receiver, if not specified a local server code receiver will be used.</param>
            <returns>User credential.</returns>
        </member>
        <member name="T:Google.Apis.Auth.OAuth2.HttpRequestMessageExtensions">
            <summary>
            Extension methods for <see cref="T:System.Net.Http.HttpRequestMessage"/>.
            </summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.HttpRequestMessageExtensions.SetOption``1(System.Net.Http.HttpRequestMessage,System.String,``0)">
            <summary>
            Sets the given key/value pair as a request option.
            </summary>
            <remarks>
            This method exist mostly to handle the fact that HttpRequestMessage.Options are only available
            from .NET 5 and up.
            </remarks>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.HttpRequestMessageExtensions.TryGetOption``1(System.Net.Http.HttpRequestMessage,System.String,``0@)">
            <summary>
            Gets the value associated with the given key on the request options.
            </summary>
            <remarks>
            This method exist mostly to handle the fact that HttpRequestMessage.Options are only available
            from .NET 5 and up.
            </remarks>
        </member>
        <member name="T:Google.Apis.Auth.OAuth2.IAccessMethod">
            <summary>
            Method of presenting the access token to the resource server as specified in 
            http://tools.ietf.org/html/rfc6749#section-7
            </summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.IAccessMethod.Intercept(System.Net.Http.HttpRequestMessage,System.String)">
            <summary>
            Intercepts a HTTP request right before the HTTP request executes by providing the access token.
            </summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.IAccessMethod.GetAccessToken(System.Net.Http.HttpRequestMessage)">
            <summary>
            Retrieves the original access token in the HTTP request, as provided in the <see cref="M:Google.Apis.Auth.OAuth2.IAccessMethod.Intercept(System.Net.Http.HttpRequestMessage,System.String)"/>
            method.
            </summary>
        </member>
        <member name="T:Google.Apis.Auth.OAuth2.IAuthorizationCodeInstalledApp">
            <summary>
            Authorization code flow for an installed application that persists end-user credentials.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.IAuthorizationCodeInstalledApp.Flow">
            <summary>Gets the authorization code flow.</summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.IAuthorizationCodeInstalledApp.CodeReceiver">
            <summary>Gets the code receiver.</summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.IAuthorizationCodeInstalledApp.AuthorizeAsync(System.String,System.Threading.CancellationToken)">
            <summary>Asynchronously authorizes the installed application to access user's protected data.</summary>
            <param name="userId">User identifier</param>
            <param name="taskCancellationToken">Cancellation token to cancel an operation</param>
            <returns>The user's credential</returns>
        </member>
        <member name="T:Google.Apis.Auth.OAuth2.IBlobSigner">
            <summary>
            Represents a data blob signer. 
            </summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.IBlobSigner.SignBlobAsync(System.Byte[],System.Threading.CancellationToken)">
            <summary>
            Returns the base64 encoded signature of the given blob.
            </summary>
            <param name="blob">The blob to sign.</param>
            <param name="cancellationToken">The cancellation token.</param>
            <returns>The base64 encoded signature.</returns>
        </member>
        <member name="T:Google.Apis.Auth.OAuth2.ICodeReceiver">
            <summary>OAuth 2.0 verification code receiver.</summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.ICodeReceiver.RedirectUri">
            <summary>Gets the redirected URI.</summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.ICodeReceiver.ReceiveCodeAsync(Google.Apis.Auth.OAuth2.Requests.AuthorizationCodeRequestUrl,System.Threading.CancellationToken)">
            <summary>Receives the authorization code.</summary>
            <param name="url">The authorization code request URL</param>
            <param name="taskCancellationToken">Cancellation token</param>
            <returns>The authorization code response</returns>
        </member>
        <member name="T:Google.Apis.Auth.OAuth2.ICredential">
            <summary>
            The main interface to represent credential in the client library.
            Service account, User account and Compute credential inherit from this interface
            to provide access token functionality. In addition this interface inherits from
            <see cref="T:Google.Apis.Http.IConfigurableHttpClientInitializer"/> to be able to hook to http requests.
            More details are available in the specific implementations.
            </summary>
        </member>
        <member name="T:Google.Apis.Auth.OAuth2.IGoogleCredential">
            <summary>
            Represents a Google credential. Defines functionality that
            credential types that can be used as an underlying credential in <see cref="T:Google.Apis.Auth.OAuth2.GoogleCredential"/>
            should implement in contrast to <see cref="T:Google.Apis.Auth.OAuth2.ICredential"/> that defines public functionality.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.IGoogleCredential.QuotaProject">
            <summary>
            The ID of the project associated to this credential for the purposes of
            quota calculation and billing. May be null.
            </summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.IGoogleCredential.WithQuotaProject(System.String)">
            <summary>
            Returns a new instance of the same type as this but with the
            given quota project value.
            </summary>
            <param name="quotaProject">The quota project value for the new instance.</param>
            <returns>A new instance with the same type as this but with <see cref="P:Google.Apis.Auth.OAuth2.IGoogleCredential.QuotaProject"/>
            set to <paramref name="quotaProject"/>.</returns>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.IGoogleCredential.HasExplicitScopes">
            <summary>
            Returns true if this credential scopes have been explicitly set via this library.
            Returns false otherwise.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.IGoogleCredential.SupportsExplicitScopes">
            <summary>
            Returns true if this credential allows explicit scopes to be set
            via this library.
            Returns false otherwise.
            </summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.IGoogleCredential.GetUniverseDomainAsync(System.Threading.CancellationToken)">
            <summary>
            Returns the universe domain this credential belongs to.
            </summary>
            <remarks>
            For most credential types, this operation is synchronous and will always
            return a completed task.
            For <see cref="T:Google.Apis.Auth.OAuth2.ComputeCredential"/>, the universe domain is obtained from the
            metadata server, which requires an HTTP call. This value is obtained only once,
            the first time it is requested for any instance of <see cref="T:Google.Apis.Auth.OAuth2.ComputeCredential"/>.
            Once the universe has been fetched this method will always return a completed task.
            The task's result will never be null.
            Note that each <paramref name="cancellationToken"/> will only apply to the call
            that provided it and not to subsequent calls. For instance, even if the first call
            to <see cref="M:Google.Apis.Auth.OAuth2.IGoogleCredential.GetUniverseDomainAsync(System.Threading.CancellationToken)"/> is cancelled, subsequent
            calls may still succeed.
            </remarks>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.IGoogleCredential.GetUniverseDomain">
            <summary>
            Returns the universe domain this credential belongs to.
            </summary>
            <remarks>
            Because <see cref="M:Google.Apis.Auth.OAuth2.IGoogleCredential.GetUniverseDomainAsync(System.Threading.CancellationToken)"/> is truly async only once, at most, in the lifetime
            of an application, this method exists for convenience.
            It can always be safely used for all credential types except for <see cref="T:Google.Apis.Auth.OAuth2.ComputeCredential"/>.
            For <see cref="T:Google.Apis.Auth.OAuth2.ComputeCredential"/>, the universe domain is obtained from the
            metadata server, which requires an HTTP call. This value is obtained only once,
            the first time it is requested for any instance of <see cref="T:Google.Apis.Auth.OAuth2.ComputeCredential"/>.
            That first time, this method may block while waiting for the HTTP call to complete.
            After that, this method will always be safe to use.
            Will never return null.
            </remarks>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.IGoogleCredential.MaybeWithScopes(System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            If the credential supports scopes, creates a copy with the specified scopes. Otherwise, it returns the same
            instance.
            </summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.IGoogleCredential.WithUserForDomainWideDelegation(System.String)">
            <summary>
            If the credential supports domain wide delegation this method will create a copy of the
            credential with the specified user set.
            Otherwise, it throws <see cref="T:System.InvalidOperationException"/>.
            </summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.IGoogleCredential.WithHttpClientFactory(Google.Apis.Http.IHttpClientFactory)">
            <summary>
            Return a new instance of the same type as this but that uses the
            given HTTP client factory.
            </summary>
            <param name="httpClientFactory">The http client factory to be used by the new instance.
            May be null in which case the default <see cref="T:Google.Apis.Http.HttpClientFactory"/> will be used.</param>
            <returns>A new instance with the same type as this but that will use <paramref name="httpClientFactory"/>
            to obtain an <see cref="T:Google.Apis.Http.ConfigurableHttpClient"/> to be used for token and other operations.</returns>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.IGoogleCredential.WithUniverseDomain(System.String)">
            <summary>
            If the credential supports custom universe domains this method will create a copy of the
            credential with the specified universe domain set.
            Otherwise, it throws <see cref="T:System.InvalidOperationException"/>.
            </summary>
            <param name="universeDomain">The universe domain to use for the credential.
            May be null, in which case the default universe domain will be used.</param>
        </member>
        <member name="T:Google.Apis.Auth.OAuth2.ImpersonatedCredential">
            <summary>
            Allows a service account or user credential to impersonate a service account.
            See https://cloud.google.com/iam/docs/creating-short-lived-service-account-credentials
            and https://cloud.google.com/iam/docs/impersonating-service-accounts
            for more information.
            </summary>
        </member>
        <member name="T:Google.Apis.Auth.OAuth2.ImpersonatedCredential.Initializer">
            <summary>An initializer class for the impersonated credential. </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.ImpersonatedCredential.Initializer.TargetPrincipal">
            <summary>
            Gets the service account to impersonate.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.ImpersonatedCredential.Initializer.DelegateAccounts">
            <summary>
            Gets the chained list of delegate service accounts. May be null or empty.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.ImpersonatedCredential.Initializer.Lifetime">
            <summary>
            Gets or sets for how long the delegated credential should be valid.
            Defaults to 1 hour or 3600 seconds.
            </summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.ImpersonatedCredential.Initializer.#ctor(System.String)">
            <summary>Constructs a new initializer.</summary>
            <param name="targetPrincipal">The principal that will be impersonated. Must not be null, as it will be used
            to build the URL to obtaing the impersonated access token from.</param>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.ImpersonatedCredential.Initializer.#ctor(System.String,System.String)">
            <summary>
            Constructus a new initializer.
            </summary>
            <param name="customTokenUrl">The URL to obtain the impersonated access token from.</param>
            <param name="maybeTargetPrincipal">The target principal, if known, that will be impersonated. May be null.</param>
            <remarks>Because the <paramref name="customTokenUrl"/> is all that is needed for obtaining the impersonated
            access token, <paramref name="maybeTargetPrincipal"/> is just informational when the
            <see cref="M:Google.Apis.Auth.OAuth2.ImpersonatedCredential.Initializer.#ctor(System.String,System.String)"/> constructor overload is used.</remarks>
        </member>
        <member name="F:Google.Apis.Auth.OAuth2.ImpersonatedCredential._oidcTokenUrlCache">
            <summary>
            The id token URL.
            If this credential does not have a custom access token URL, the id token is supported through the IAM API.
            The id token URL is built using the universe domain and the target principal.
            </summary>
        </member>
        <member name="F:Google.Apis.Auth.OAuth2.ImpersonatedCredential._signBlobUrlCache">
            <summary>
            The blob signing URL.
            If this credential does not have a custom access token URL, blob signing is supported through the IAM API.
            The blob signing URL is built using the universe domain and the target principal.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.ImpersonatedCredential.SourceCredential">
            <summary>
            Gets the source credential used to acquire the impersonated credentials.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.ImpersonatedCredential.TargetPrincipal">
            <summary>
            Gets the service account to impersonate.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.ImpersonatedCredential.DelegateAccounts">
            <summary>
            Gets the chained list of delegate service accounts. May be empty.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.ImpersonatedCredential.Lifetime">
            <summary>
            Gets the lifetime of the delegated credential.
            This is how long the delegated credential should be valid from the time
            of the first request made with this credential.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.ImpersonatedCredential.HasCustomTokenUrlCache">
            <summary>
            Whether the effective access token URL is custom or not.
            If the impersonated credential has a custom access token URL we don't know how the OIDC URL and blob signing
            URL may look like, so we cannot support those operations.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.ImpersonatedCredential.EffectiveTokenUrlCache">
            <summary>
            The effective token URL to be used by this credential, which may be a custom token URL
            or the IAM API access token endpoint URL which is built using the universe domain and the
            target principal of this credential.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.ImpersonatedCredential.Google#Apis#Auth#OAuth2#IGoogleCredential#HasExplicitScopes">
            <inheritdoc/>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.ImpersonatedCredential.Google#Apis#Auth#OAuth2#IGoogleCredential#SupportsExplicitScopes">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.ImpersonatedCredential.#ctor(Google.Apis.Auth.OAuth2.ImpersonatedCredential.Initializer)">
            <summary>Constructs a new impersonated credential using the given initializer.</summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.ImpersonatedCredential.Google#Apis#Auth#OAuth2#IGoogleCredential#GetUniverseDomainAsync(System.Threading.CancellationToken)">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.ImpersonatedCredential.Google#Apis#Auth#OAuth2#IGoogleCredential#GetUniverseDomain">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.ImpersonatedCredential.Google#Apis#Auth#OAuth2#IGoogleCredential#WithQuotaProject(System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.ImpersonatedCredential.Google#Apis#Auth#OAuth2#IGoogleCredential#MaybeWithScopes(System.Collections.Generic.IEnumerable{System.String})">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.ImpersonatedCredential.Google#Apis#Auth#OAuth2#IGoogleCredential#WithUserForDomainWideDelegation(System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.ImpersonatedCredential.Google#Apis#Auth#OAuth2#IGoogleCredential#WithHttpClientFactory(Google.Apis.Http.IHttpClientFactory)">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.ImpersonatedCredential.Google#Apis#Auth#OAuth2#IGoogleCredential#WithUniverseDomain(System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.ImpersonatedCredential.RequestAccessTokenAsync(System.Threading.CancellationToken)">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.ImpersonatedCredential.GetOidcTokenAsync(Google.Apis.Auth.OAuth2.OidcTokenOptions,System.Threading.CancellationToken)">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.ImpersonatedCredential.SignBlobAsync(System.Byte[],System.Threading.CancellationToken)">
            <summary>
            Signs the provided blob using the private key associated with the impersonated service account.
            </summary>
            <param name="blob">The blob to sign.</param>
            <param name="cancellationToken">Cancellation token to cancel operation.</param>
            <returns>The base64 encoded signature.</returns>
            <exception cref="T:System.Net.Http.HttpRequestException">When signing request fails.</exception>
            <exception cref="T:Newtonsoft.Json.JsonException">When signing response is not a valid JSON.</exception>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.ImpersonatedCredential.GetEffectiveTokenUrlUncachedAsync">
            <summary>
            Returns the token URL to be used by this credential, which may be a custom token URL
            or the IAM API access token endpoint URL which is built using the universe domain and the
            target principal of this credential.
            A custom access token URL could be present in external credentials configuration.
            </summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.ImpersonatedCredential.HasCustomTokenUrlUncachedAsync">
            <summary>
            Determines whether the effective access token URL is custom or not.
            If the impersonated credential has a custom access token URL we don't know how the OIDC URL and blob signing
            URL may look like, so we cannot support those operations.
            </summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.ImpersonatedCredential.GetIdTokenUrlUncachedAsync">
            <summary>
            Gets the id token URL if this credential supports id token emission.
            Throws <see cref="T:System.InvalidOperationException"/> otherwise.
            </summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.ImpersonatedCredential.GetSignBlobUrlUncachedAsync">
            <summary>
            Get's the blob signing URL if this credential supports blob signing.
            Throws <see cref="T:System.InvalidOperationException"/> otherwise.
            </summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.ImpersonatedCredential.ThrowIfCustomTokenUrlAsync(System.Threading.CancellationToken)">
            <summary>
            If the impersonated credential has a custom access token URL we don't know how the OIDC URL and blob signing
            URL may look like, so we cannot support those operations.
            A custom access token URL could be present in external credentials configuration.
            </summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.ImpersonatedCredential.ExtractTargetPrincipal(System.String)">
            <summary>
            Attempts to extract the target principal ID from the impersonation URL which is possible if the URL looks like
            https://host/segment-1/.../segment-n/target-principal-ID:generateAccessToken.
            It's OK if we can't though as for fetching the impersonated access token we have the impersonation URL as a whole.
            It's just a nice to have, as the user may be able to execute extra operations with the impersonated credential, like
            signing a blob of fetching its OIDC token.
            </summary>
        </member>
        <member name="T:Google.Apis.Auth.OAuth2.IOidcTokenProvider">
            <summary>
            Represents an OIDC token provider.
            </summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.IOidcTokenProvider.GetOidcTokenAsync(Google.Apis.Auth.OAuth2.OidcTokenOptions,System.Threading.CancellationToken)">
            <summary>
            Returns an OIDC token for the given options.
            </summary>
            <param name="options">The options to create the token from.</param>
            <param name="cancellationToken">The cancellation token that may be used to cancel the request.</param>
            <returns>The OIDC token.</returns>
        </member>
        <member name="T:Google.Apis.Auth.OAuth2.ITokenAccess">
            <summary>
            Allows direct retrieval of access tokens to authenticate requests.
            This is necessary for workflows where you don't want to use 
            <see cref="T:Google.Apis.Services.BaseClientService"/> to access the API.
            (e.g. gRPC that implemenents the entire HTTP2 stack internally).
            </summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.ITokenAccess.GetAccessTokenForRequestAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Gets an access token to authorize a request.
            Implementations should handle automatic refreshes of the token
            if they are supported.
            The <paramref name="authUri"/> might be required by some credential types
            (e.g. the JWT access token) while other credential types
            migth just ignore it.
            </summary>
            <param name="authUri">The URI the returned token will grant access to.</param>
            <param name="cancellationToken">The cancellation token.</param>
            <returns>The access token.</returns>
        </member>
        <member name="T:Google.Apis.Auth.OAuth2.ITokenAccessWithHeaders">
            <summary>
            Allows direct retrieval of access tokens to authenticate requests.
            The access tokens obtained can be accompanied by extra information 
            that either describes the access token or is associated with it.
            This information should acompany the token as headers when the token
            is used to access a resource.
            </summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.ITokenAccessWithHeaders.GetAccessTokenWithHeadersForRequestAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Gets an access token to authorize a request.
            The token might be accompanied by extra information that should be sent
            in the form of headers.
            Implementations should handle automatic refreshes of the token
            if they are supported.
            The <paramref name="authUri"/> might be required by some credential types
            (e.g. the JWT access token) while other credential types
            migth just ignore it.
            </summary>
            <param name="authUri">The URI the returned token will grant access to.</param>
            <param name="cancellationToken">The cancellation token.</param>
            <returns>The access token with headers if any.</returns>
        </member>
        <member name="T:Google.Apis.Auth.OAuth2.JsonCredentialParameters">
            <summary>
            Holder for credential parameters read from JSON credential file.
            Fields are union of parameters for all supported credential types.
            </summary>
        </member>
        <member name="F:Google.Apis.Auth.OAuth2.JsonCredentialParameters.AuthorizedUserCredentialType">
            <summary>
            UserCredential is created by the GCloud SDK tool when the user runs
            <a href="https://cloud.google.com/sdk/gcloud/reference/auth/login">GCloud Auth Login</a>.
            </summary>
        </member>
        <member name="F:Google.Apis.Auth.OAuth2.JsonCredentialParameters.ServiceAccountCredentialType">
            <summary>
            ServiceAccountCredential is downloaded by the user from
            <a href="https://console.developers.google.com">Google Developers Console</a>.
            </summary>
        </member>
        <member name="F:Google.Apis.Auth.OAuth2.JsonCredentialParameters.ImpersonatedServiceAccountCredentialType">
            <summary>
            ImpersonatedCredential is created by the GCloud SDK tool when the user runs
            <a href="https://cloud.google.com/sdk/gcloud/reference/auth/application-default/login">GCloud Auth ADC Login</a>
            using the <a href="https://cloud.google.com/sdk/gcloud/reference#--impersonate-service-account">--impersonate-service-account</a>
            flag.
            </summary>
        </member>
        <member name="F:Google.Apis.Auth.OAuth2.JsonCredentialParameters.ExternalAccountCredentialType">
            <summary>
            See https://cloud.google.com/iam/docs/workload-identity-federation on how
            to create external account credentials.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.JsonCredentialParameters.Type">
            <summary>Type of the credential.</summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.JsonCredentialParameters.ProjectId">
            <summary>
            Project ID associated with this credential.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.JsonCredentialParameters.QuotaProject">
            <summary>
            Project ID associated with this credential for the purposes
            of quota calculations and billing.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.JsonCredentialParameters.UniverseDomain">
            <summary>
            Universe domain that this credential may be used in.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.JsonCredentialParameters.ClientId">
            <summary>
            Client Id associated with UserCredential created by
            <a href="https://cloud.google.com/sdk/gcloud/reference/auth/login">GCloud Auth Login</a>
            or with an external account credential.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.JsonCredentialParameters.ClientSecret">
            <summary>
            Client Secret associated with UserCredential created by
            <a href="https://cloud.google.com/sdk/gcloud/reference/auth/login">GCloud Auth Login</a>
            or with an external account credential.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.JsonCredentialParameters.ClientEmail">
            <summary>
            Client Email associated with ServiceAccountCredential obtained from
            <a href="https://console.developers.google.com">Google Developers Console</a>
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.JsonCredentialParameters.PrivateKey">
            <summary>
            Private Key associated with ServiceAccountCredential obtained from
            <a href="https://console.developers.google.com">Google Developers Console</a>.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.JsonCredentialParameters.PrivateKeyId">
            <summary>
            Private Key ID associated with ServiceAccountCredential obtained from
            <a href="https://console.developers.google.com">Google Developers Console</a>.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.JsonCredentialParameters.TokenUri">
            <summary>
            The token endpoint for a service account credential.
            </summary>
            <remarks>
            Note that this is different from <see cref="P:Google.Apis.Auth.OAuth2.JsonCredentialParameters.TokenUrl"/> which is the
            STS token exchange endpoint associated with an external account credential.
            </remarks>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.JsonCredentialParameters.RefreshToken">
            <summary>
            Refresh Token associated with UserCredential created by
            <a href="https://cloud.google.com/sdk/gcloud/reference/auth/login">GCloud Auth Login</a>.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.JsonCredentialParameters.ServiceAccountImpersonationUrl">
            <summary>
            This is the URL for the service account impersonation request
            associated with a source credential or with an external account credential.
            If this credential is an external account credential and this is not set,
            the STS returned access token should be directly used without impersonation.
            If this credential is not an external account credential and this is set,
            then a credential source needs to be specified.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.JsonCredentialParameters.Delegates">
            <summary>
            Delegates chain associated to the impersonated credential.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.JsonCredentialParameters.SourceCredential">
            <summary>
            The source credential associated to the impersonated credential.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.JsonCredentialParameters.Audience">
            <summary>
            The STS audience associated with an external account credential.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.JsonCredentialParameters.SubjectTokenType">
            <summary>
            The STS subject token type associated with an external account credential.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.JsonCredentialParameters.TokenUrl">
            <summary>
            The STS token exchange endpoint associated with an external account credential.
            </summary>
            <remarks>
            Note that this is different from <see cref="P:Google.Apis.Auth.OAuth2.JsonCredentialParameters.TokenUri"/> which is the
            the token endpoint for a service account credential.
            </remarks>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.JsonCredentialParameters.WorkforcePoolUserProject">
            <summary>
            The GCP project number to be used for Workforce Pools
            external credentials.
            </summary>
            <remarks>
            If this external account credential represents a Workforce Pool
            enabled identity and this values is not specified, then an API key needs to be
            used alongside this credential to call Google APIs.
            </remarks>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.JsonCredentialParameters.CredentialSourceConfig">
            <summary>
            The credential source associated with an external account credential.
            </summary>
        </member>
        <member name="T:Google.Apis.Auth.OAuth2.JsonCredentialParameters.CredentialSource">
            <summary>
            Holder for the credential source parameters associated to an external account credentials.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.JsonCredentialParameters.CredentialSource.EnvironmentId">
            <summary>
            The environment identifier for AWS external accounts.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.JsonCredentialParameters.CredentialSource.RegionUrl">
            <summary>
            For AWS credentials this is the metadata server URL used to determine the AWS region
            that should be included as part of the subject token.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.JsonCredentialParameters.CredentialSource.Url">
            <summary>
            For URL-sourced credentials this is the URL from which to obtain the subject token from.
            For AWS credentials this is the URL for the metadata server from which to obtain the
            security credentials that will be used to sign the subject token.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.JsonCredentialParameters.CredentialSource.RegionalCredentialVerificationUrl">
            <summary>
            For AWS credentials, the STS server will use this URL to validate the subject token
            included on the STS request. This URL will be included as part of the subject token.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.JsonCredentialParameters.CredentialSource.ImdsV2SessionTokenUrl">
            <summary>
            For AWS credentials, if present, a session token fetched from this URL should be used when making
            requests to the metadata server.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.JsonCredentialParameters.CredentialSource.Headers">
            <summary>
            For URL-sourced credentilas this are headers to be included on the request to obtain the subject token.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.JsonCredentialParameters.CredentialSource.File">
            <summary>
            For file-sourced credentials this is the path to the file containing the subject token.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.JsonCredentialParameters.CredentialSource.Format">
            <summary>
            For URL and file sourced credentials, indicates the format in which the subject token will be returned.
            </summary>
        </member>
        <member name="T:Google.Apis.Auth.OAuth2.JsonCredentialParameters.CredentialSource.SubjectTokenFormat">
            <summary>
            Holder for the subject token format.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.JsonCredentialParameters.CredentialSource.SubjectTokenFormat.Type">
            <summary>
            For URL and file sourced credentials, indicates the format in which the subject token is returned.
            Supported values are <code>text</code> and <code>json</code>.
            Defaults to <code>text</code>.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.JsonCredentialParameters.CredentialSource.SubjectTokenFormat.SubjectTokenFieldName">
            <summary>
            For URL and file sourced credentials, if the subject token is returned within a JSON, this indicates the
            field in which it can be found.
            </summary>
        </member>
        <member name="T:Google.Apis.Auth.OAuth2.LocalServerCodeReceiver">
            <summary>
            OAuth 2.0 verification code receiver that runs a local server on a free port and waits for a call with the 
            authorization verification code.
            </summary>
        </member>
        <member name="T:Google.Apis.Auth.OAuth2.LocalServerCodeReceiver.CallbackUriChooserStrategy">
            <summary>
            Describes the different strategies for the selection of the callback URI.
            127.0.0.1 is recommended, but can't be done in non-admin Windows 7 and 8 at least.
            </summary>
        </member>
        <member name="F:Google.Apis.Auth.OAuth2.LocalServerCodeReceiver.CallbackUriChooserStrategy.Default">
            <summary>
            Use heuristics to attempt to connect to the recommended URI 127.0.0.1
            but use localhost if that fails.
            </summary>
        </member>
        <member name="F:Google.Apis.Auth.OAuth2.LocalServerCodeReceiver.CallbackUriChooserStrategy.ForceLoopbackIp">
            <summary>
            Force 127.0.0.1 as the callback URI. No checks are performed.
            </summary>
        </member>
        <member name="F:Google.Apis.Auth.OAuth2.LocalServerCodeReceiver.CallbackUriChooserStrategy.ForceLocalhost">
            <summary>
            Force localhost as the callback URI. No checks are performed.
            </summary>
        </member>
        <member name="F:Google.Apis.Auth.OAuth2.LocalServerCodeReceiver.LoopbackCallbackPath">
            <summary>The call back request path.</summary>
        </member>
        <member name="F:Google.Apis.Auth.OAuth2.LocalServerCodeReceiver.DefaultClosePageResponse">
            <summary>Close HTML tag to return the browser so it will close itself.</summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.LocalServerCodeReceiver.#ctor">
            <summary>
            Create an instance of <see cref="T:Google.Apis.Auth.OAuth2.LocalServerCodeReceiver"/>.
            </summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.LocalServerCodeReceiver.#ctor(System.String)">
            <summary>
            Create an instance of <see cref="T:Google.Apis.Auth.OAuth2.LocalServerCodeReceiver"/>.
            </summary>
            <param name="closePageResponse">Custom close page response for this instance</param>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.LocalServerCodeReceiver.#ctor(System.String,Google.Apis.Auth.OAuth2.LocalServerCodeReceiver.CallbackUriChooserStrategy)">
            <summary>
            Create an instance of <see cref="T:Google.Apis.Auth.OAuth2.LocalServerCodeReceiver"/>.
            </summary>
            <param name="closePageResponse">Custom close page response for this instance</param>
            <param name="strategy">The strategy to use to determine the callback URI</param>
        </member>
        <member name="T:Google.Apis.Auth.OAuth2.LocalServerCodeReceiver.LimitedLocalhostHttpServer">
            <summary>
            An extremely limited HTTP server that can only do exactly what is required
            for this use-case.
            It can only serve localhost; receive a single GET request; read only the query paremters;
            send back a fixed response. Nothing else.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.LocalServerCodeReceiver.RedirectUri">
            <inheritdoc />
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.LocalServerCodeReceiver.ReceiveCodeAsync(Google.Apis.Auth.OAuth2.Requests.AuthorizationCodeRequestUrl,System.Threading.CancellationToken)">
            <inheritdoc />
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.LocalServerCodeReceiver.GetRandomUnusedPort">
            <summary>Returns a random, unused port.</summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.LocalServerCodeReceiver.OpenBrowser(System.String)">
            <summary>
            Open a browser and navigate to a URL.
            </summary>
            <param name="url">URL to navigate to</param>
            <returns>true if browser was launched successfully, false otherwise</returns>
        </member>
        <member name="F:Google.Apis.Auth.OAuth2.LocalServerCodeReceiver.CallbackUriChooser.CallbackUriTemplateLocalhost">
            <summary>Localhost callback URI, expects a port parameter.</summary>
        </member>
        <member name="F:Google.Apis.Auth.OAuth2.LocalServerCodeReceiver.CallbackUriChooser.CallbackUriTemplate127001">
            <summary>127.0.0.1 callback URI, expects a port parameter.</summary>
        </member>
        <member name="T:Google.Apis.Auth.OAuth2.OidcToken">
            <summary>
            Represents an OIDC Token.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.OidcToken.TokenResponse">
            <summary>
            The <see cref="P:Google.Apis.Auth.OAuth2.OidcToken.TokenResponse"/> this OIDC token is built from.
            </summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.OidcToken.GetAccessTokenAsync(System.Threading.CancellationToken)">
            <summary>
            Gets the access token that should be included in headers when performing
            requests with this <see cref="T:Google.Apis.Auth.OAuth2.OidcToken"/>.
            This method will refresh the access token if the current one has expired.
            </summary>
            <param name="cancellationToken">The cancellation token to use for cancelling the operation.</param>
            <returns>The valid access token associated to this <see cref="T:Google.Apis.Auth.OAuth2.OidcToken"/>.</returns>
        </member>
        <member name="T:Google.Apis.Auth.OAuth2.OidcTokenFormat">
            <summary>
            Represents the OIDC token formats supported when the token is obtained using the GCE metadata server.
            </summary>
        </member>
        <member name="F:Google.Apis.Auth.OAuth2.OidcTokenFormat.Standard">
            <summary>
            Specifies that the project and instance details should not be
            included in the payload of the JWT token returned by the GCE
            metadata server.
            </summary>
        </member>
        <member name="F:Google.Apis.Auth.OAuth2.OidcTokenFormat.Full">
            <summary>
            Specifies that the project and instance details should be
            included in the payload of the JWT token returned by the GCE
            metadata server.
            </summary>
        </member>
        <member name="F:Google.Apis.Auth.OAuth2.OidcTokenFormat.FullWithLicences">
            <summary>
            Same as <see cref="F:Google.Apis.Auth.OAuth2.OidcTokenFormat.Full"/>. License codes for images associated with the
            GCE instance the token is being obtained from will also be included in the
            payload of the JWT token returned by the GCE metadata server.
            </summary>
        </member>
        <member name="T:Google.Apis.Auth.OAuth2.OidcTokenOptions">
            <summary>
            Options used to create an <see cref="T:Google.Apis.Auth.OAuth2.OidcToken"/>.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.OidcTokenOptions.TargetAudience">
            <summary>
            The target audience the generated token should be valid for.
            Must not be null.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.OidcTokenOptions.TokenFormat">
            <summary>
            The token format of the expected OIDC token when obtained from the
            GCE metadata server.
            This value will be ignored when the token provider is other then the GCE
            metadata server.
            <see cref="T:Google.Apis.Auth.OAuth2.OidcTokenFormat"/> for the meaning of each value.
            Defaults to <see cref="F:Google.Apis.Auth.OAuth2.OidcTokenFormat.Full"/>.
            </summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.OidcTokenOptions.FromTargetAudience(System.String)">
            <summary>
            Builds new <see cref="T:Google.Apis.Auth.OAuth2.OidcTokenOptions"/> from the given target audience.
            </summary>
            <param name="targetAudience">The target audience to build these options from. Must no be null.</param>
            <returns>A new set of options that can be used with a <see cref="T:Google.Apis.Auth.OAuth2.IOidcTokenProvider"/> to obtain an <see cref="T:Google.Apis.Auth.OAuth2.OidcToken"/>.</returns>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.OidcTokenOptions.WithTargetAudience(System.String)">
            <summary>
            Builds a new set of options with the same options as this one, except for the target audience.
            </summary>
            <param name="targetAudience">The new target audience. Must not be null.</param>
            <returns>A new set of options with the given target audience.</returns>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.OidcTokenOptions.WithTokenFormat(Google.Apis.Auth.OAuth2.OidcTokenFormat)">
            <summary>
            Builds a new set of options with the same options as this one, except for the token format.
            </summary>
            <param name="tokenFormat">The new token format.</param>
            <returns>A new set of options with the given token format.</returns>
        </member>
        <member name="T:Google.Apis.Auth.OAuth2.Pkcs8.Asn1">
            <summary>
            An incomplete ASN.1 decoder, only implements what's required
            to decode a Service Credential.
            </summary>
        </member>
        <member name="T:Google.Apis.Auth.OAuth2.RequestExtensions">
            <summary>
            Extension methods for requests.
            </summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.RequestExtensions.AddCredential``1(``0,Google.Apis.Auth.OAuth2.ICredential)">
            <summary>
            Add a credential that is used for this request only.
            This will override a service-level credential (if there is one).
            Do not call more than once per request instance, as each call incrementally adds the provided credential.
            To perform identical requests but with distinct credentials, create a separate request instance for each credential.
            </summary>
            <typeparam name="T">The request type.</typeparam>
            <param name="request">The request which requires a credential. Must not be null.</param>
            <param name="credential">The credential to use for this request only. Must not be null.</param>
            <returns></returns>
        </member>
        <member name="T:Google.Apis.Auth.OAuth2.Requests.AuthorizationCodeRequestUrl">
            <summary>
            OAuth 2.0 request URL for an authorization web page to allow the end user to authorize the application to 
            access their protected resources and that returns an authorization code, as specified in 
            http://tools.ietf.org/html/rfc6749#section-4.1.
            </summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.Requests.AuthorizationCodeRequestUrl.#ctor(System.Uri)">
            <summary>
            Constructs a new authorization code request with the specified URI and sets response_type to <c>code</c>.
            </summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.Requests.AuthorizationCodeRequestUrl.Build">
            <summary>Creates a <see cref="T:System.Uri"/> which is used to request the authorization code.</summary>
        </member>
        <member name="T:Google.Apis.Auth.OAuth2.Requests.AuthorizationCodeTokenRequest">
            <summary>
             OAuth 2.0 request for an access token using an authorization code as specified in 
             http://tools.ietf.org/html/rfc6749#section-4.1.3.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Requests.AuthorizationCodeTokenRequest.Code">
            <summary>Gets or sets the authorization code received from the authorization server.</summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Requests.AuthorizationCodeTokenRequest.RedirectUri">
            <summary>
            Gets or sets the redirect URI parameter matching the redirect URI parameter in the authorization request.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Requests.AuthorizationCodeTokenRequest.CodeVerifier">
            <summary>
            Gets or sets the code verifier matching the code challenge in the authorization request.
            See https://developers.google.com/identity/protocols/oauth2/native-app#exchange-authorization-code
            for more information.
            </summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.Requests.AuthorizationCodeTokenRequest.#ctor">
            <summary>
            Constructs a new authorization code token request and sets grant_type to <c>authorization_code</c>.
            </summary>
        </member>
        <member name="T:Google.Apis.Auth.OAuth2.Requests.AuthorizationRequestUrl">
            <summary>
            OAuth 2.0 request URL for an authorization web page to allow the end user to authorize the application to 
            access their protected resources, as specified in http://tools.ietf.org/html/rfc6749#section-3.1.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Requests.AuthorizationRequestUrl.ResponseType">
            <summary>
            Gets or sets the response type which must be <c>code</c> for requesting an authorization code or 
            <c>token</c> for requesting an access token (implicit grant), or space separated registered extension 
            values. See http://tools.ietf.org/html/rfc6749#section-3.1.1 for more details
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Requests.AuthorizationRequestUrl.ClientId">
            <summary>Gets or sets the client identifier.</summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Requests.AuthorizationRequestUrl.RedirectUri">
            <summary>
            Gets or sets the URI that the authorization server directs the resource owner's user-agent back to the 
            client after a successful authorization grant, as specified in 
            http://tools.ietf.org/html/rfc6749#section-3.1.2 or <c>null</c> for none.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Requests.AuthorizationRequestUrl.Scope">
            <summary>
            Gets or sets space-separated list of scopes, as specified in http://tools.ietf.org/html/rfc6749#section-3.3
            or <c>null</c> for none.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Requests.AuthorizationRequestUrl.State">
            <summary>
            Gets or sets the state (an opaque value used by the client to maintain state between the request and 
            callback, as mentioned in http://tools.ietf.org/html/rfc6749#section-3.1.2.2 or <c>null</c> for none.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Requests.AuthorizationRequestUrl.AuthorizationServerUrl">
            <summary>Gets the authorization server URI.</summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.Requests.AuthorizationRequestUrl.#ctor(System.Uri)">
            <summary>Constructs a new authorization request with the specified URI.</summary>
            <param name="authorizationServerUrl">Authorization server URI</param>
        </member>
        <member name="T:Google.Apis.Auth.OAuth2.Requests.GoogleAssertionTokenRequest">
            <summary>
            Service account assertion token request as specified in 
            https://developers.google.com/accounts/docs/OAuth2ServiceAccount#makingrequest.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Requests.GoogleAssertionTokenRequest.Assertion">
            <summary>Gets or sets the JWT (including signature).</summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.Requests.GoogleAssertionTokenRequest.#ctor">
            <summary>
            Constructs a new refresh code token request and sets grant_type to 
            <c>urn:ietf:params:oauth:grant-type:jwt-bearer</c>.
            </summary>
        </member>
        <member name="T:Google.Apis.Auth.OAuth2.Requests.GoogleAuthorizationCodeRequestUrl">
            <summary>
            Google-specific implementation of the OAuth 2.0 URL for an authorization web page to allow the end user to 
            authorize the application to access their protected resources and that returns an authorization code, as 
            specified in https://developers.google.com/accounts/docs/OAuth2WebServer.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Requests.GoogleAuthorizationCodeRequestUrl.AccessType">
            <summary>
            Gets or sets the access type. Set <c>online</c> to request on-line access or <c>offline</c> to request 
            off-line access or <c>null</c> for the default behavior. The default value is <c>offline</c>.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Requests.GoogleAuthorizationCodeRequestUrl.Prompt">
            <summary>
            Gets of sets prompt for consent behaviour.
            Value can be <c>null</c>, <c>"none"</c>, <c>"consent"</c>, or <c>"select_account"</c>.
            See <a href="https://developers.google.com/identity/protocols/OpenIDConnect#prompt">OpenIDConnect documentation</a>
            for details.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Requests.GoogleAuthorizationCodeRequestUrl.ApprovalPrompt">
            <summary>
            Gets or sets prompt for consent behavior <c>auto</c> to request auto-approval or<c>force</c> to force the 
            approval UI to show, or <c>null</c> for the default behavior.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Requests.GoogleAuthorizationCodeRequestUrl.LoginHint">
            <summary>
            Gets or sets the login hint. Sets <c>email address</c> or sub <c>identifier</c>.
            When your application knows which user it is trying to authenticate, it may provide this parameter as a
            hint to the Authentication Server. Passing this hint will either pre-fill the email box on the sign-in form
            or select the proper multi-login session, thereby simplifying the login flow.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Requests.GoogleAuthorizationCodeRequestUrl.IncludeGrantedScopes">
            <summary>
            Gets or sets the include granted scopes to determine if this authorization request should use
            incremental authorization (https://developers.google.com/+/web/api/rest/oauth#incremental-auth).
            If true and the authorization request is granted, the authorization will include any previous 
            authorizations granted to this user/application combination for other scopes.
            </summary>
            <remarks>Currently unsupported for installed apps.</remarks>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Requests.GoogleAuthorizationCodeRequestUrl.Nonce">
            <summary>
            Gets or sets the nonce;
            a random value generated by your app that enables replay protection.
            See https://developers.google.com/identity/protocols/OpenIDConnect for more details.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Requests.GoogleAuthorizationCodeRequestUrl.CodeChallenge">
            <summary>
            Gets or sets the code challenge.
            See https://developers.google.com/identity/protocols/oauth2/native-app#create-the-code-challenge
            for more information.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Requests.GoogleAuthorizationCodeRequestUrl.CodeChallengeMethod">
            <summary>
            Gets or sets the code challenge method.
            See https://developers.google.com/identity/protocols/oauth2/native-app#create-the-code-challenge
            for more information.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Requests.GoogleAuthorizationCodeRequestUrl.UserDefinedQueryParams">
            <summary>
            Gets or sets a collection of user defined query parameters to facilitate any not explicitly supported
            by the library which will be included in the resultant authentication URL.
            </summary>
            <remarks>
            The name of this parameter is used only for the constructor and will not end up in the resultant query
            string.
            </remarks>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.Requests.GoogleAuthorizationCodeRequestUrl.#ctor(System.Uri)">
            <summary>
            Constructs a new authorization code request with the given authorization server URL. This constructor sets
            the <see cref="P:Google.Apis.Auth.OAuth2.Requests.GoogleAuthorizationCodeRequestUrl.AccessType"/> to <c>offline</c>.
            </summary>
        </member>
        <member name="T:Google.Apis.Auth.OAuth2.Requests.GoogleRevokeTokenRequest">
            <summary>
            Google OAuth 2.0 request to revoke an access token as specified in 
            https://developers.google.com/accounts/docs/OAuth2WebServer#tokenrevoke.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Requests.GoogleRevokeTokenRequest.RevokeTokenUrl">
            <summary>Gets the URI for token revocation.</summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Requests.GoogleRevokeTokenRequest.Token">
            <summary>Gets or sets the token to revoke.</summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.Requests.GoogleRevokeTokenRequest.Build">
            <summary>Creates a <see cref="T:System.Uri"/> which is used to request the authorization code.</summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Requests.IamSignBlobRequest.DelegateAccounts">
            <summary>
            Gets or sets the chained list of delegate service accounts.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Requests.IamSignBlobRequest.Payload">
            <summary>
            Gets or sets the payload to be signed.
            </summary>
        </member>
        <member name="T:Google.Apis.Auth.OAuth2.Requests.ImpersonationAccessTokenRequest">
            <summary>
            Access token request for impersonated credential as specified in https://cloud.google.com/iam/docs/creating-short-lived-service-account-credentials#sa-credentials-oauth.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Requests.ImpersonationAccessTokenRequest.Scopes">
            <summary>
            Gets or sets the scopes to request during the authorization grant.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Requests.ImpersonationAccessTokenRequest.Lifetime">
            <summary>
            Gets or sets how long the delegated credential should be valid. Its format is the number of
            seconds followed by a letter "s", for example "300s".
            </summary>
        </member>
        <member name="T:Google.Apis.Auth.OAuth2.Requests.ImpersonationOIdCTokenRequest">
            <summary>
            OIDC token request for impersonated credential as specified in https://cloud.google.com/iam/docs/creating-short-lived-service-account-credentials#sa-credentials-oauth.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Requests.ImpersonationOIdCTokenRequest.Audience">
            <summary>
            Gets or sets the audience of the requested OIDC token.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Requests.ImpersonationOIdCTokenRequest.IncludeEmail">
            <summary>
            Gets or sets whether email address should be included in the requested OIDC token.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Requests.ImpersonationRequest.DelegateAccounts">
            <summary>
            Gets or sets the chained list of delegate service accounts.
            </summary>
        </member>
        <member name="T:Google.Apis.Auth.OAuth2.Requests.RefreshTokenRequest">
            <summary>
            OAuth 2.0 request to refresh an access token using a refresh token as specified in 
            http://tools.ietf.org/html/rfc6749#section-6.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Requests.RefreshTokenRequest.RefreshToken">
            <summary>Gets or sets the Refresh token issued to the client.</summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.Requests.RefreshTokenRequest.#ctor">
            <summary>
            Constructs a new refresh code token request and sets grant_type to <c>refresh_token</c>.
            </summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.Requests.RequestExtensions.PostJsonAsync(System.Object,System.Net.Http.HttpClient,System.String,System.Threading.CancellationToken)">
            <summary>
            Serializes <paramref name="request"/> to JSON and posts it to <paramref name="url"/>.
            </summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.Requests.RequestExtensions.PostJsonAsync``1(System.Object,System.Net.Http.HttpClient,System.String,System.Threading.CancellationToken)">
            <summary>
            Serializes <paramref name="request"/> to JSON and posts it to <paramref name="url"/>.
            Deserializes the JSON response into <typeparamref name="TResponse"/>.
            </summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.Requests.RequestExtensions.PostJsonAsync(System.Object,System.Net.Http.HttpClient,System.String,Google.Apis.Util.IClock,Google.Apis.Logging.ILogger,System.Threading.CancellationToken)">
            <summary>
            Serializes <paramref name="request"/> to JSON and posts it to <paramref name="url"/>.
            Builds a <see cref="T:Google.Apis.Auth.OAuth2.Responses.TokenResponse"/> instance from the HTTP response.
            <see cref="M:Google.Apis.Auth.OAuth2.Responses.TokenResponse.FromHttpResponseAsync(System.Net.Http.HttpResponseMessage,Google.Apis.Util.IClock,Google.Apis.Logging.ILogger)"/>
            for more information.
            </summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.Requests.RequestExtensions.PostFormAsync(System.Object,System.Net.Http.HttpClient,System.String,System.Net.Http.Headers.AuthenticationHeaderValue,Google.Apis.Util.IClock,Google.Apis.Logging.ILogger,System.Threading.CancellationToken)">
            <summary>
            Creates and HTTP form from <paramref name="request"/> and posts it to <paramref name="url"/>.
            If <paramref name="authenticationHeaderValue"/> is not null, its value is included as the
            Authorization header of the request.
            Builds a <see cref="T:Google.Apis.Auth.OAuth2.Responses.TokenResponse"/> instance from the HTTP response.
            <see cref="M:Google.Apis.Auth.OAuth2.Responses.TokenResponse.FromHttpResponseAsync(System.Net.Http.HttpResponseMessage,Google.Apis.Util.IClock,Google.Apis.Logging.ILogger)"/>
            for more information.
            </summary>
        </member>
        <member name="T:Google.Apis.Auth.OAuth2.Requests.StsTokenRequestBuilder">
            <summary>
            Builder for <see cref="T:Google.Apis.Auth.OAuth2.Requests.StsTokenRequest"/>.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Requests.StsTokenRequestBuilder.GrantType">
            <summary>
            Gets the grant type for this request.
            Only <code>urn:ietf:params:oauth:grant-type:token-exchange</code> is currently supported.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Requests.StsTokenRequestBuilder.Audience">
            <summary>
            The audience for which the requested token is intended. For instance:
            "//iam.googleapis.com/projects/my-project-id/locations/global/workloadIdentityPools/my-pool-id/providers/my-provider-id"
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Requests.StsTokenRequestBuilder.Scopes">
            <summary>
            The list of desired scopes for the requested token.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Requests.StsTokenRequestBuilder.RequestedTokenType">
            <summary>
            The type of the requested security token.
            Only <code>urn:ietf:params:oauth:token-type:access_token</code> is currently supported.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Requests.StsTokenRequestBuilder.SubjectToken">
            <summary>
            In terms of Google 3PI support, this is the 3PI credential.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Requests.StsTokenRequestBuilder.SubjectTokenType">
            <summary>
            The subject token type.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Requests.StsTokenRequestBuilder.ClientId">
            <summary>
            Client ID and client secret are not part of STS token exchange spec.
            But in the context of Google 3PI they are used to perform basic authorization
            for token exchange.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Requests.StsTokenRequestBuilder.ClientSecret">
            <summary>
            Client ID and client secret are not part of STS token exchange spec.
            But in the context of Google 3PI they are used to perform basic authorization
            for token exchange.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Requests.StsTokenRequestBuilder.WorkforcePoolUserProject">
            <summary>
            The GCP project number to be used for Workforce Pools
            external credentials. To be included in the request as part of options.
            </summary>
        </member>
        <member name="T:Google.Apis.Auth.OAuth2.Requests.StsTokenRequest">
            <summary>
            OAuth 2.0 subject token exchange request as defined in
            https://datatracker.ietf.org/doc/html/rfc8693#section-2.1.
            This is only a partial definition of the spec as required to support Google WIF.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Requests.StsTokenRequest.GrantType">
            <summary>
            Gets the grant type for this request.
            Only <code>urn:ietf:params:oauth:grant-type:token-exchange</code> is currently supported.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Requests.StsTokenRequest.Audience">
            <summary>
            The audience for which the requested token is intended. For instance:
            "//iam.googleapis.com/projects/my-project-id/locations/global/workloadIdentityPools/my-pool-id/providers/my-provider-id"
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Requests.StsTokenRequest.Scope">
            <summary>
            The space-delimited list of desired scopes for the requested token as defined in
            http://tools.ietf.org/html/rfc6749#section-3.3. 
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Requests.StsTokenRequest.RequestedTokenType">
            <summary>
            The type of the requested security token.
            Only <code>urn:ietf:params:oauth:token-type:access_token</code> is currently supported.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Requests.StsTokenRequest.SubjectToken">
            <summary>
            In terms of Google 3PI support, this is the 3PI credential.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Requests.StsTokenRequest.SubjectTokenType">
            <summary>
            The subject token type.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Requests.StsTokenRequest.GoogleOptions">
            <summary>
            Google specific STS token request options.
            May be null.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Requests.StsTokenRequest.AuthenticationHeader">
            <summary>
            Authentication header to be included in the request.
            May be null.
            </summary>
        </member>
        <member name="T:Google.Apis.Auth.OAuth2.Requests.TokenRequest">
            <summary>
            OAuth 2.0 request for an access token as specified in http://tools.ietf.org/html/rfc6749#section-4.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Requests.TokenRequest.Scope">
            <summary>
            Gets or sets space-separated list of scopes as specified in http://tools.ietf.org/html/rfc6749#section-3.3.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Requests.TokenRequest.GrantType">
            <summary>
            Gets or sets the Grant type. Sets <c>authorization_code</c> or <c>password</c> or <c>client_credentials</c> 
            or <c>refresh_token</c> or absolute URI of the extension grant type.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Requests.TokenRequest.ClientId">
            <summary>Gets or sets the client Identifier.</summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Requests.TokenRequest.ClientSecret">
            <summary>Gets or sets the client Secret.</summary>
        </member>
        <member name="T:Google.Apis.Auth.OAuth2.Requests.TokenRequestExtenstions">
            <summary>Extension methods to <see cref="T:Google.Apis.Auth.OAuth2.Requests.TokenRequest"/>.</summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.Requests.TokenRequestExtenstions.ExecuteAsync(Google.Apis.Auth.OAuth2.Requests.TokenRequest,System.Net.Http.HttpClient,System.String,System.Threading.CancellationToken,Google.Apis.Util.IClock)">
            <summary>
            Executes the token request in order to receive a 
            <see cref="T:Google.Apis.Auth.OAuth2.Responses.TokenResponse"/>. In case the token server returns an 
            error, a <see cref="T:Google.Apis.Auth.OAuth2.Responses.TokenResponseException"/> is thrown.
            </summary>
            <param name="request">The token request.</param>
            <param name="httpClient">The HTTP client used to create an HTTP request.</param>
            <param name="tokenServerUrl">The token server URL.</param>
            <param name="taskCancellationToken">Cancellation token to cancel operation.</param>
            <param name="clock">The clock which is used to set the <see cref="P:Google.Apis.Auth.OAuth2.Responses.TokenResponse.Issued"/> property.</param>
            <returns>Token response with the new access token.</returns>
        </member>
        <member name="T:Google.Apis.Auth.OAuth2.Responses.AuthorizationCodeResponseUrl">
            <summary>
            Authorization Code response for the redirect URL after end user grants or denies authorization as specified 
            in http://tools.ietf.org/html/rfc6749#section-4.1.2.
            <para>
            Check that <see cref="P:Google.Apis.Auth.OAuth2.Responses.AuthorizationCodeResponseUrl.Code"/> is not <c>null</c> or empty to verify the end-user granted authorization.
            </para>
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Responses.AuthorizationCodeResponseUrl.Code">
            <summary>Gets or sets the authorization code generated by the authorization server.</summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Responses.AuthorizationCodeResponseUrl.State">
            <summary>
            Gets or sets the state parameter matching the state parameter in the authorization request.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Responses.AuthorizationCodeResponseUrl.Error">
            <summary>
            Gets or sets the error code (e.g. "invalid_request", "unauthorized_client", "access_denied", 
            "unsupported_response_type", "invalid_scope", "server_error", "temporarily_unavailable") as specified in 
            http://tools.ietf.org/html/rfc6749#section-*******.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Responses.AuthorizationCodeResponseUrl.ErrorDescription">
            <summary>
            Gets or sets the human-readable text which provides additional information used to assist the client 
            developer in understanding the error occurred.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Responses.AuthorizationCodeResponseUrl.ErrorUri">
            <summary>
            Gets or sets the URI identifying a human-readable web page with provides information about the error.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Responses.AuthorizationCodeResponseUrl.AdditionalParameters">
            <summary>
            Contains any extra parameters in the authorization code response URL query string.
            </summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.Responses.AuthorizationCodeResponseUrl.#ctor(System.Collections.Generic.IDictionary{System.String,System.String})">
            <summary>Constructs a new authorization code response URL from the specified dictionary.</summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.Responses.AuthorizationCodeResponseUrl.#ctor(System.String)">
            <summary>Constructs a new authorization code response URL from the specified query string.</summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.Responses.AuthorizationCodeResponseUrl.InitFromDictionary(System.Collections.Generic.IDictionary{System.String,System.String})">
            <summary>Initializes this instance from the input dictionary.</summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.Responses.AuthorizationCodeResponseUrl.#ctor">
            <summary>Constructs a new empty authorization code response URL.</summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Responses.IamSignBlobResponse.SignedBlob">
            <summary>Gets or sets the signed blob.</summary>
        </member>
        <member name="T:Google.Apis.Auth.OAuth2.Responses.TokenErrorResponse">
            <summary>
            OAuth 2.0 model for a unsuccessful access token response as specified in 
            http://tools.ietf.org/html/rfc6749#section-5.2.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Responses.TokenErrorResponse.Error">
            <summary>
            Gets or sets error code (e.g. "invalid_request", "invalid_client", "invalid_grant", "unauthorized_client", 
            "unsupported_grant_type", "invalid_scope") as specified in http://tools.ietf.org/html/rfc6749#section-5.2.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Responses.TokenErrorResponse.ErrorDescription">
            <summary>
            Gets or sets a human-readable text which provides additional information used to assist the client 
            developer in understanding the error occurred.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Responses.TokenErrorResponse.ErrorUri">
            <summary>
            Gets or sets the URI identifying a human-readable web page with provides information about the error.
            </summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.Responses.TokenErrorResponse.ToString">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.Responses.TokenErrorResponse.#ctor">
            <summary>Constructs a new empty token error response.</summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.Responses.TokenErrorResponse.#ctor(Google.Apis.Auth.OAuth2.Responses.AuthorizationCodeResponseUrl)">
            <summary>Constructs a new token error response from the given authorization code response.</summary>
        </member>
        <member name="T:Google.Apis.Auth.OAuth2.Responses.TokenResponse">
            <summary>
            OAuth 2.0 model for a successful access token response as specified in 
            http://tools.ietf.org/html/rfc6749#section-5.1.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Responses.TokenResponse.AccessToken">
            <summary>Gets or sets the access token issued by the authorization server.</summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Responses.TokenResponse.TokenType">
            <summary>
            Gets or sets the token type as specified in http://tools.ietf.org/html/rfc6749#section-7.1.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Responses.TokenResponse.ExpiresInSeconds">
            <summary>Gets or sets the lifetime in seconds of the access token.</summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Responses.TokenResponse.RefreshToken">
            <summary>
            Gets or sets the refresh token which can be used to obtain a new access token.
            For example, the value "3600" denotes that the access token will expire in one hour from the time the 
            response was generated.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Responses.TokenResponse.Scope">
            <summary>
            Gets or sets the scope of the access token as specified in http://tools.ietf.org/html/rfc6749#section-3.3.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Responses.TokenResponse.IdToken">
            <summary>
            Gets or sets the id_token, which is a JSON Web Token (JWT) as specified in http://tools.ietf.org/html/draft-ietf-oauth-json-web-token
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Responses.TokenResponse.Issued">
            <summary>
            The date and time that this token was issued, expressed in the system time zone.
            This property only exists for backward compatibility; it can cause inappropriate behavior around
            time zone transitions (e.g. daylight saving transitions).
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Responses.TokenResponse.IssuedUtc">
            <summary>
            The date and time that this token was issued, expressed in UTC.
            </summary>
            <remarks>
            This should be set by the CLIENT after the token was received from the server.
            </remarks>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Responses.TokenResponse.ImpersonatedAccessToken">
            <summary>Access token for impersonated credentials.</summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Responses.TokenResponse.ImpersonatedIdToken">
            <summary>ID token for impersonated credentials.</summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Responses.TokenResponse.ImpersonatedAccessTokenExpireTime">
            <summary>
            Access token expiration time for impersonated credentials. It has the RFC3339
            format: "yyyy-MM-dd'T'HH:mm:sssssssss'Z'". For example: 2020-05-13T16:00:00.045123456Z.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Responses.TokenResponse.IsStale">
            <summary>
            Returns true if the token represented by this token response should be refreshed.
            Note that this may be true for valid tokens, in which case a pre-emptive refresh is adviced
            even if the current token may be used while it continues to be valid.
            </summary>
            <remarks>
            See <see cref="M:Google.Apis.Auth.OAuth2.Responses.TokenResponse.MayBeUsed(Google.Apis.Util.IClock)"/> for information on when a token is considered valid.
            A valid token is considered stale if it's close to expiring, but not so much as to be unusable.
            </remarks>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Responses.TokenResponse.RefreshWindowStartUtc">
            <summary>
            The start of the refresh window for this token, if known. Otherwise, null.
            </summary>
            <remarks>
            At the start of token refresh window, the token is still usable, but efforts should
            be made to obtain a fresher one.
            </remarks>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Responses.TokenResponse.ExpiryWindowStartUtc">
            <summary>
            The start of the expiry window for this token, if known. Otherwise, null.
            </summary>
            <remarks>
            A token that's within its expiry window, may still be usable, but doing so
            may run into clock skew related issues.
            </remarks>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.Responses.TokenResponse.IsExpired(Google.Apis.Util.IClock)">
            <summary>
            Returns true if the token is expired or it's going to expire soon.
            </summary>
            <remarks>If a token response doens't have at least one of <see cref="P:Google.Apis.Auth.OAuth2.Responses.TokenResponse.AccessToken"/>
            or <see cref="P:Google.Apis.Auth.OAuth2.Responses.TokenResponse.IdToken"/> set then it's considered expired.
            If <see cref="P:Google.Apis.Auth.OAuth2.Responses.TokenResponse.ExpiresInSeconds"/> is null, the token is also considered expired. </remarks>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.Responses.TokenResponse.ShouldBeRefreshed(Google.Apis.Util.IClock)">
            <summary>
            Returns true if the token represented by this token response should be refreshed.
            Note that this may be true for valid tokens, in which case a pre-emptive refresh is adviced
            even if the current token may be used while it continues to be valid.
            </summary>
            <remarks>
            See <see cref="M:Google.Apis.Auth.OAuth2.Responses.TokenResponse.MayBeUsed(Google.Apis.Util.IClock)"/> for information on when a token is considered valid.
            A valid token is considered stale if it's close to expiring, but not so much as to be unusable.
            </remarks>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.Responses.TokenResponse.MayBeUsed(Google.Apis.Util.IClock)">
            <summary>
            Returns true if the token represented by this token response is valid, that is, it may be used
            for authentication and authorizations purposes.
            </summary>
            <remarks>
            A token is considered valid if all of the following are true:
            <list type="bullet">
            <item>At least one of <see cref="P:Google.Apis.Auth.OAuth2.Responses.TokenResponse.AccessToken"/> and <see cref="P:Google.Apis.Auth.OAuth2.Responses.TokenResponse.IdToken"/> is not null.</item>
            <item><see cref="P:Google.Apis.Auth.OAuth2.Responses.TokenResponse.ExpiresInSeconds"/> is not null.</item>
            <item>The token has not expired and will not expire in the very near future. That is if
            <see cref="P:Google.Apis.Auth.OAuth2.Responses.TokenResponse.IssuedUtc"/> plus <see cref="P:Google.Apis.Auth.OAuth2.Responses.TokenResponse.ExpiresInSeconds"/> is in the not so near future.</item>
            </list>
            </remarks>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.Responses.TokenResponse.FromHttpResponseAsync(System.Net.Http.HttpResponseMessage,Google.Apis.Util.IClock,Google.Apis.Logging.ILogger)">
            <summary>
            Asynchronously parses a <see cref="T:Google.Apis.Auth.OAuth2.Responses.TokenResponse"/> instance from the specified <see cref="T:System.Net.Http.HttpResponseMessage"/>.
            </summary>
            <param name="response">The http response from which to parse the token.</param>
            <param name="clock">The clock used to set the <see cref="P:Google.Apis.Auth.OAuth2.Responses.TokenResponse.Issued"/> value of the token.</param>
            <param name="logger">The logger used to output messages incase of error.</param>
            <exception cref="T:Google.Apis.Auth.OAuth2.Responses.TokenResponseException">
            The response was not successful or there is an error parsing the response into valid <see cref="T:Google.Apis.Auth.OAuth2.Responses.TokenResponse"/> instance.
            </exception>
            <returns>
            A task containing the <see cref="T:Google.Apis.Auth.OAuth2.Responses.TokenResponse"/> parsed form the response message.
            </returns>
        </member>
        <member name="T:Google.Apis.Auth.OAuth2.Responses.TokenResponseException">
            <summary>
            Token response exception which is thrown in case of receiving a token error when an authorization code or an 
            access token is expected.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Responses.TokenResponseException.Error">
            <summary>The error information.</summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Responses.TokenResponseException.StatusCode">
            <summary>HTTP status code of error, or null if unknown.</summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.Responses.TokenResponseException.#ctor(Google.Apis.Auth.OAuth2.Responses.TokenErrorResponse)">
            <summary>Constructs a new token response exception from the given error.</summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.Responses.TokenResponseException.#ctor(Google.Apis.Auth.OAuth2.Responses.TokenErrorResponse,System.Nullable{System.Net.HttpStatusCode})">
            <summary>Constructs a new token response exception from the given error nad optional HTTP status code.</summary>
        </member>
        <member name="T:Google.Apis.Auth.OAuth2.ServiceAccountCredential">
            <summary>
            Google OAuth 2.0 credential for accessing protected resources using an access token. The Google OAuth 2.0 
            Authorization Server supports server-to-server interactions such as those between a web application and Google
            Cloud Storage. The requesting application has to prove its own identity to gain access to an API, and an 
            end-user doesn't have to be involved. 
            <para>
            Take a look in https://developers.google.com/accounts/docs/OAuth2ServiceAccount for more details.
            </para>
            <para>
            Since version 1.9.3, service account credential also supports JSON Web Token access token scenario.
            In this scenario, instead of sending a signed JWT claim to a token server and exchanging it for 
            an access token, a locally signed JWT claim bound to an appropriate URI is used as an access token
            directly.
            See <see cref="M:Google.Apis.Auth.OAuth2.ServiceAccountCredential.GetAccessTokenForRequestAsync(System.String,System.Threading.CancellationToken)"/> for explanation when JWT access token
            is used and when regular OAuth2 token is used.
            </para>
            </summary>
        </member>
        <member name="T:Google.Apis.Auth.OAuth2.ServiceAccountCredential.Initializer">
            <summary>An initializer class for the service account credential. </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.ServiceAccountCredential.Initializer.Id">
            <summary>Gets the service account ID (typically an e-mail address).</summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.ServiceAccountCredential.Initializer.ProjectId">
            <summary>
            The project ID associated with this credential.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.ServiceAccountCredential.Initializer.User">
            <summary>
            Gets or sets the email address of the user the application is trying to impersonate in the service 
            account flow or <c>null</c>.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.ServiceAccountCredential.Initializer.Key">
            <summary>
            Gets or sets the key which is used to sign the request, as specified in
            https://developers.google.com/accounts/docs/OAuth2ServiceAccount#computingsignature.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.ServiceAccountCredential.Initializer.KeyId">
            <summary>
            Gets or sets the service account key ID.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.ServiceAccountCredential.Initializer.UseJwtAccessWithScopes">
            <summary>
            Gets or sets the flag preferring use of self-signed JWTs over OAuth tokens when OAuth scopes are explicitly set.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.ServiceAccountCredential.Initializer.UniverseDomain">
            <summary>
            The universe domain this credential belongs to.
            Won't be null.
            </summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.ServiceAccountCredential.Initializer.#ctor(System.String)">
            <summary>Constructs a new initializer using the given id.</summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.ServiceAccountCredential.Initializer.#ctor(System.String,System.String)">
            <summary>Constructs a new initializer using the given id and the token server URL.</summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.ServiceAccountCredential.Initializer.FromPrivateKey(System.String)">
            <summary>Extracts the <see cref="P:Google.Apis.Auth.OAuth2.ServiceAccountCredential.Initializer.Key"/> from the given PKCS8 private key.</summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.ServiceAccountCredential.Initializer.FromCertificate(System.Security.Cryptography.X509Certificates.X509Certificate2)">
            <summary>Extracts a <see cref="P:Google.Apis.Auth.OAuth2.ServiceAccountCredential.Initializer.Key"/> from the given certificate.</summary>
        </member>
        <member name="F:Google.Apis.Auth.OAuth2.ServiceAccountCredential.UnixEpoch">
            <summary>Unix epoch as a <c>DateTime</c></summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.ServiceAccountCredential.Id">
            <summary>Gets the service account ID (typically an e-mail address).</summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.ServiceAccountCredential.ProjectId">
            <summary>
            The project ID associated with this credential.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.ServiceAccountCredential.User">
            <summary>
            Gets the email address of the user the application is trying to impersonate in the service account flow 
            or <c>null</c>.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.ServiceAccountCredential.Key">
            <summary>
            Gets the key which is used to sign the request, as specified in
            https://developers.google.com/accounts/docs/OAuth2ServiceAccount#computingsignature.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.ServiceAccountCredential.KeyId">
            <summary>
            Gets the key id of the key which is used to sign the request.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.ServiceAccountCredential.UseJwtAccessWithScopes">
            <summary>
            Gets the flag indicating whether Self-Signed JWT should be used when OAuth scopes are set.
            This flag will be ignored if this credential has <see cref="P:Google.Apis.Auth.OAuth2.ServiceAccountCredential.User"/> set, meaning
            it is used with domain-wide delegation. Self-Signed JWTs won't be used in that case.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.ServiceAccountCredential.UniverseDomain">
            <summary>
            The universe domain this credential belongs to. Won't be null.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.ServiceAccountCredential.Google#Apis#Auth#OAuth2#IGoogleCredential#HasExplicitScopes">
            <inheritdoc/>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.ServiceAccountCredential.Google#Apis#Auth#OAuth2#IGoogleCredential#SupportsExplicitScopes">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.ServiceAccountCredential.#ctor(Google.Apis.Auth.OAuth2.ServiceAccountCredential.Initializer)">
            <summary>Constructs a new service account credential using the given initializer.</summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.ServiceAccountCredential.FromServiceAccountData(System.IO.Stream)">
            <summary>
            Creates a new <see cref="T:Google.Apis.Auth.OAuth2.ServiceAccountCredential"/> instance from JSON credential data.
            </summary>
            <param name="credentialData">The stream from which to read the JSON key data for a service account. Must not be null.</param>
            <exception cref="T:System.InvalidOperationException">
            The <paramref name="credentialData"/> does not contain valid JSON service account key data.
            </exception>
            <returns>The credentials parsed from the service account key data.</returns>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.ServiceAccountCredential.Google#Apis#Auth#OAuth2#IGoogleCredential#GetUniverseDomainAsync(System.Threading.CancellationToken)">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.ServiceAccountCredential.Google#Apis#Auth#OAuth2#IGoogleCredential#GetUniverseDomain">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.ServiceAccountCredential.WithUseJwtAccessWithScopes(System.Boolean)">
            <summary>
            Constructs a new instance of the <see cref="T:Google.Apis.Auth.OAuth2.ServiceAccountCredential"/> but with the
            given <see cref="P:Google.Apis.Auth.OAuth2.ServiceAccountCredential.UseJwtAccessWithScopes"/> value.
            </summary>
            <param name="useJwtAccessWithScopes">A flag preferring use of self-signed JWTs over OAuth tokens 
            when OAuth scopes are explicitly set.</param>
            <returns>A new instance of the <see cref="T:Google.Apis.Auth.OAuth2.ServiceAccountCredential"/> but with the
            given <see cref="P:Google.Apis.Auth.OAuth2.ServiceAccountCredential.UseJwtAccessWithScopes"/> value.
            </returns>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.ServiceAccountCredential.Google#Apis#Auth#OAuth2#IGoogleCredential#WithQuotaProject(System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.ServiceAccountCredential.Google#Apis#Auth#OAuth2#IGoogleCredential#MaybeWithScopes(System.Collections.Generic.IEnumerable{System.String})">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.ServiceAccountCredential.Google#Apis#Auth#OAuth2#IGoogleCredential#WithUserForDomainWideDelegation(System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.ServiceAccountCredential.Google#Apis#Auth#OAuth2#IGoogleCredential#WithHttpClientFactory(Google.Apis.Http.IHttpClientFactory)">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.ServiceAccountCredential.Google#Apis#Auth#OAuth2#IGoogleCredential#WithUniverseDomain(System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.ServiceAccountCredential.RequestAccessTokenAsync(System.Threading.CancellationToken)">
            <summary>
            Requests a new token as specified in 
            https://developers.google.com/accounts/docs/OAuth2ServiceAccount#makingrequest.
            </summary>
            <param name="taskCancellationToken">Cancellation token to cancel operation.</param>
            <returns><c>true</c> if a new token was received successfully.</returns>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.ServiceAccountCredential.GetAccessTokenForRequestAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Gets an access token to authorize a request.
            An OAuth2 access token obtained from <see cref="P:Google.Apis.Auth.OAuth2.ServiceCredential.TokenServerUrl"/> will be returned
            in the following two cases:
            1. If this credential has <see cref="P:Google.Apis.Auth.OAuth2.ServiceCredential.Scopes"/> associated, but <see cref="P:Google.Apis.Auth.OAuth2.ServiceAccountCredential.UseJwtAccessWithScopes"/>
            is false; 
            2. If this credential is used with domain-wide delegation, that is, the <see cref="P:Google.Apis.Auth.OAuth2.ServiceAccountCredential.User"/> is set;
            Otherwise, a locally signed JWT will be returned. 
            The signed JWT will contain a "scope" claim with the scopes in <see cref="P:Google.Apis.Auth.OAuth2.ServiceCredential.Scopes"/> if there are any,
            otherwise it will contain an "aud" claim with <paramref name="authUri"/>.
            A cached token is used if possible and the token is only refreshed once it's close to its expiry.
            </summary>
            <param name="authUri">The URI the returned token will grant access to. 
            Should be specified if no <see cref="P:Google.Apis.Auth.OAuth2.ServiceCredential.Scopes"/> have been specified for the credential.</param>
            <param name="cancellationToken">The cancellation token.</param>
            <returns>The access token.</returns>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.ServiceAccountCredential.GetOidcTokenAsync(Google.Apis.Auth.OAuth2.OidcTokenOptions,System.Threading.CancellationToken)">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.ServiceAccountCredential.CreateJwtAccessToken(System.String,System.DateTime,System.DateTime)">
            <summary>
            Creates a JWT access token than can be used in request headers instead of an OAuth2 token.
            This is achieved by signing a special JWT using this service account's private key.
            <param name="authUri">The URI for which the access token will be valid.</param>
            <param name="issueUtc">The issue time of the JWT.</param>
            <param name="expiryUtc">The expiry time of the JWT.</param>
            </summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.ServiceAccountCredential.CreateAssertionFromPayload(Google.Apis.Auth.JsonWebSignature.Payload)">
            <summary>
            Signs JWT token using the private key and returns the serialized assertion.
            </summary>
            <param name="payload">the JWT payload to sign.</param>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.ServiceAccountCredential.CreateSignature(System.Byte[])">
            <summary>
            Creates a base64 encoded signature for the SHA-256 hash of the specified data.
            </summary>
            <param name="data">The data to hash and sign. Must not be null.</param>
            <returns>The base-64 encoded signature.</returns>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.ServiceAccountCredential.SignBlobAsync(System.Byte[],System.Threading.CancellationToken)">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.ServiceAccountCredential.CreateSerializedHeader">
            <summary>
            Creates a serialized header as specified in 
            https://developers.google.com/accounts/docs/OAuth2ServiceAccount#formingheader.
            </summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.ServiceAccountCredential.CreatePayload">
            <summary>
            Creates a claim set as specified in 
            https://developers.google.com/accounts/docs/OAuth2ServiceAccount#formingclaimset.
            </summary>
        </member>
        <member name="T:Google.Apis.Auth.OAuth2.ServiceCredential">
            <summary>
            This type of Google OAuth 2.0 credential enables access to protected resources using an access token when
            interacting server to server. For example, a service account credential could be used to access Google Cloud
            Storage from a web application without a user's involvement.
            <para>
            <see cref="T:Google.Apis.Auth.OAuth2.ServiceAccountCredential"/> inherits from this class in order to support Service Accounts. More
            details available at: https://developers.google.com/accounts/docs/OAuth2ServiceAccount.
            <see cref="T:Google.Apis.Auth.OAuth2.ComputeCredential"/> is another example of a class that inherits from this
            class in order to support Compute credentials. For more information about Compute authentication, see:
            https://cloud.google.com/compute/docs/authentication.
            </para>
            <para>
            <see cref="T:Google.Apis.Auth.OAuth2.ExternalAccountCredential"/> inherits from this class to support both Workload Identity Federation
            and Workforce Identity Federation. You can read more about these topics in
            https://cloud.google.com/iam/docs/workload-identity-federation and
            https://cloud.google.com/iam/docs/workforce-identity-federation respectively.
            Note that in the case of Workforce Identity Federation, the external account does not represent a service account
            but a user account, so, the fact that <see cref="T:Google.Apis.Auth.OAuth2.ExternalAccountCredential"/> inherits from <see cref="T:Google.Apis.Auth.OAuth2.ServiceCredential"/>
            might be construed as misleading. In reality <see cref="T:Google.Apis.Auth.OAuth2.ServiceCredential"/> is not tied to a service account
            in terms of implementation, only in terms of name. For instance, a better name for this class might have been NoUserFlowCredential, and
            in that sense, it's correct that <see cref="T:Google.Apis.Auth.OAuth2.ExternalAccountCredential"/> inherits from <see cref="T:Google.Apis.Auth.OAuth2.ServiceCredential"/>
            even when representing a Workforce Identity Federation account.
            </para>
            </summary>
        </member>
        <member name="F:Google.Apis.Auth.OAuth2.ServiceCredential.Logger">
            <summary>Logger for this class</summary>
        </member>
        <member name="T:Google.Apis.Auth.OAuth2.ServiceCredential.Initializer">
            <summary>An initializer class for the service credential. </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.ServiceCredential.Initializer.TokenServerUrl">
            <summary>Gets the token server URL.</summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.ServiceCredential.Initializer.Clock">
            <summary>
            Gets or sets the clock used to refresh the token when it expires. The default value is
            <see cref="F:Google.Apis.Util.SystemClock.Default"/>.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.ServiceCredential.Initializer.AccessMethod">
            <summary>
            Gets or sets the method for presenting the access token to the resource server.
            The default value is <see cref="T:Google.Apis.Auth.OAuth2.BearerToken.AuthorizationHeaderAccessMethod"/>.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.ServiceCredential.Initializer.HttpClientFactory">
            <summary>
            Gets or sets the factory for creating a <see cref="T:System.Net.Http.HttpClient"/> instance.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.ServiceCredential.Initializer.DefaultExponentialBackOffPolicy">
            <summary>
            Get or sets the exponential back-off policy. Default value is  <c>UnsuccessfulResponse503</c>, which 
            means that exponential back-off is used on 503 abnormal HTTP responses.
            If the value is set to <c>None</c>, no exponential back-off policy is used, and it's up to the user to
            configure the <see cref="T:Google.Apis.Http.ConfigurableMessageHandler"/> in an
            <see cref="T:Google.Apis.Http.IConfigurableHttpClientInitializer"/> to set a specific back-off
            implementation (using <see cref="T:Google.Apis.Http.BackOffHandler"/>).
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.ServiceCredential.Initializer.QuotaProject">
            <summary>
            The ID of the project associated to this credential for the purposes of
            quota calculation and billing. May be null.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.ServiceCredential.Initializer.Scopes">
            <summary>
            Scopes to request during the authorization grant. May be null or empty.
            </summary>
            <remarks>
            If the scopes are pre-granted through the environement, like in GCE where scopes are granted to the VM,
            scopes set here will be ignored.
            </remarks>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.ServiceCredential.Initializer.HttpClientInitializers">
            <summary>
            Initializers to be sent to the <see cref="P:Google.Apis.Auth.OAuth2.ServiceCredential.Initializer.HttpClientFactory"/> to be set
            on the <see cref="P:Google.Apis.Auth.OAuth2.ServiceCredential.HttpClient"/> that will be used by the credential to perform
            token operations.
            </summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.ServiceCredential.Initializer.#ctor(System.String)">
            <summary>Constructs a new initializer using the given token server URL.</summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.ServiceCredential.TokenServerUrl">
            <summary>
            Gets the token server URL.
            </summary>
            <remarks>
            May be null for credential types that resolve token endpoints just before obtaining an access token.
            This is the case for <see cref="T:Google.Apis.Auth.OAuth2.ImpersonatedCredential"/> where the <see cref="P:Google.Apis.Auth.OAuth2.ImpersonatedCredential.SourceCredential"/>
            is a <see cref="T:Google.Apis.Auth.OAuth2.ComputeCredential"/>.
            </remarks>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.ServiceCredential.Clock">
            <summary>Gets the clock used to refresh the token if it expires.</summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.ServiceCredential.AccessMethod">
            <summary>Gets the method for presenting the access token to the resource server.</summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.ServiceCredential.HttpClient">
            <summary>Gets the HTTP client used to make authentication requests to the server.</summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.ServiceCredential.Scopes">
            <summary>
            Scopes to request during the authorization grant. May be null or empty.
            </summary>
            <remarks>
            If the scopes are pre-granted through the environment, like in GCE where scopes are granted to the VM,
            scopes set here will be ignored.
            </remarks>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.ServiceCredential.HasExplicitScopes">
            <summary>
            Returns true if this credential scopes have been explicitly set via this library.
            Returns false otherwise.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.ServiceCredential.HttpClientInitializers">
            <summary>
            Initializers to be sent to the <see cref="P:Google.Apis.Auth.OAuth2.ServiceCredential.HttpClientFactory"/> to be set
            on the <see cref="P:Google.Apis.Auth.OAuth2.ServiceCredential.HttpClient"/> that will be used by the credential to perform
            token operations.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.ServiceCredential.Token">
            <summary>Gets the token response which contains the access token.</summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.ServiceCredential.QuotaProject">
            <summary>
            The ID of the project associated to this credential for the purposes of
            quota calculation and billing. May be null.
            </summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.ServiceCredential.#ctor(Google.Apis.Auth.OAuth2.ServiceCredential.Initializer)">
            <summary>Constructs a new service account credential using the given initializer.</summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.ServiceCredential.BuildCreateHttpClientArgs">
            <summary>
            Builds HTTP client creation args from this credential settings.
            </summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.ServiceCredential.Initialize(Google.Apis.Http.ConfigurableHttpClient)">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.ServiceCredential.InterceptAsync(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.ServiceCredential.HandleResponseAsync(Google.Apis.Http.HandleUnsuccessfulResponseArgs)">
            <summary>
            Decorates unsuccessful responses, returns true if the response gets modified.
            See IHttpUnsuccessfulResponseHandler for more information. 
            </summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.ServiceCredential.GetAccessTokenForRequestAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Gets an access token to authorize a request. If the existing token expires soon, try to refresh it first.
            <seealso cref="M:Google.Apis.Auth.OAuth2.ITokenAccess.GetAccessTokenForRequestAsync(System.String,System.Threading.CancellationToken)"/>
            </summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.ServiceCredential.GetAccessTokenWithHeadersForRequestAsync(System.String,System.Threading.CancellationToken)">
            <inheritdoc />
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.ServiceCredential.RequestAccessTokenAsync(System.Threading.CancellationToken)">
            <summary>Requests a new token.</summary>
            <param name="taskCancellationToken">Cancellation token to cancel operation.</param>
            <returns><c>true</c> if a new token was received successfully.</returns>
        </member>
        <member name="T:Google.Apis.Auth.OAuth2.TokenRefreshManager">
            <summary>
            Encapsulation of token refresh behaviour. This isn't entirely how we'd design the code now (in terms of the
            callback in particular) but it fits in with the exposed API surface of ServiceCredential and UserCredential.
            </summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.TokenRefreshManager.#ctor(System.Func{System.Threading.CancellationToken,System.Threading.Tasks.Task{System.Boolean}},Google.Apis.Util.IClock,Google.Apis.Logging.ILogger)">
            <summary>
            Creates a manager which executes the given refresh action when required.
            </summary>
            <param name="refreshAction">The refresh action which will populate the Token property when successful.</param>
            <param name="clock">The clock to consult for timeouts.</param>
            <param name="logger">The logger to use to record refreshes.</param>
        </member>
        <member name="T:Google.Apis.Auth.OAuth2.UrlSourcedExternalAccountCredential">
            <summary>
            URL-sourced credentials as described in
            https://google.aip.dev/auth/4117#determining-the-subject-token-in-microsoft-azure-and-url-sourced-credentials.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.UrlSourcedExternalAccountCredential.Initializer.SubjectTokenUrl">
            <summary>
            The URL from which to obtain the subject token.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.UrlSourcedExternalAccountCredential.Initializer.Headers">
            <summary>
            Headers to include in the request for the subject token.
            May be null or empty.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.UrlSourcedExternalAccountCredential.Initializer.SubjectTokenJsonFieldName">
            <summary>
            If set, the subject token response will be parsed as JSON and the
            value in the field with name <see cref="P:Google.Apis.Auth.OAuth2.UrlSourcedExternalAccountCredential.Initializer.SubjectTokenJsonFieldName"/>
            will be returned as the subject token.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.UrlSourcedExternalAccountCredential.SubjectTokenUrl">
            <summary>
            The URL from which to obtain the subject token.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.UrlSourcedExternalAccountCredential.Headers">
            <summary>
            Headers to include in the request for the subject token.
            May be empty. Will not be null.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.UrlSourcedExternalAccountCredential.SubjectTokenJsonFieldName">
            <summary>
            If set, the subject token response will be parsed as JSON and the
            value in the field with name <see cref="P:Google.Apis.Auth.OAuth2.UrlSourcedExternalAccountCredential.SubjectTokenJsonFieldName"/>
            will be returned as the subject token.
            </summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.UrlSourcedExternalAccountCredential.WithoutImpersonationConfigurationImpl">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.UrlSourcedExternalAccountCredential.GetSubjectTokenAsyncImpl(System.Threading.CancellationToken)">
            <inheritdoc/>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.UrlSourcedExternalAccountCredential.Google#Apis#Auth#OAuth2#IGoogleCredential#QuotaProject">
            <inheritdoc/>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.UrlSourcedExternalAccountCredential.Google#Apis#Auth#OAuth2#IGoogleCredential#HasExplicitScopes">
            <inheritdoc/>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.UrlSourcedExternalAccountCredential.Google#Apis#Auth#OAuth2#IGoogleCredential#SupportsExplicitScopes">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.UrlSourcedExternalAccountCredential.Google#Apis#Auth#OAuth2#IGoogleCredential#GetUniverseDomainAsync(System.Threading.CancellationToken)">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.UrlSourcedExternalAccountCredential.Google#Apis#Auth#OAuth2#IGoogleCredential#GetUniverseDomain">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.UrlSourcedExternalAccountCredential.Google#Apis#Auth#OAuth2#IGoogleCredential#WithQuotaProject(System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.UrlSourcedExternalAccountCredential.Google#Apis#Auth#OAuth2#IGoogleCredential#MaybeWithScopes(System.Collections.Generic.IEnumerable{System.String})">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.UrlSourcedExternalAccountCredential.Google#Apis#Auth#OAuth2#IGoogleCredential#WithUserForDomainWideDelegation(System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.UrlSourcedExternalAccountCredential.Google#Apis#Auth#OAuth2#IGoogleCredential#WithHttpClientFactory(Google.Apis.Http.IHttpClientFactory)">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.UrlSourcedExternalAccountCredential.Google#Apis#Auth#OAuth2#IGoogleCredential#WithUniverseDomain(System.String)">
            <inheritdoc/>
        </member>
        <member name="T:Google.Apis.Auth.OAuth2.UserCredential">
            <summary>
            OAuth 2.0 credential for accessing protected resources using an access token, as well as optionally refreshing 
            the access token when it expires using a refresh token.
            </summary>
        </member>
        <member name="F:Google.Apis.Auth.OAuth2.UserCredential.Logger">
            <summary>Logger for this class.</summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.UserCredential.Token">
            <summary>Gets or sets the token response which contains the access token.</summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.UserCredential.Flow">
            <summary>Gets the authorization code flow.</summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.UserCredential.UserId">
            <summary>Gets the user identity.</summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.UserCredential.QuotaProject">
            <inheritdoc/>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.UserCredential.Google#Apis#Auth#OAuth2#IGoogleCredential#HasExplicitScopes">
            <inheritdoc/>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.UserCredential.Google#Apis#Auth#OAuth2#IGoogleCredential#SupportsExplicitScopes">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.UserCredential.#ctor(Google.Apis.Auth.OAuth2.Flows.IAuthorizationCodeFlow,System.String,Google.Apis.Auth.OAuth2.Responses.TokenResponse)">
            <summary>Constructs a new credential instance.</summary>
            <param name="flow">Authorization code flow.</param>
            <param name="userId">User identifier.</param>
            <param name="token">An initial token for the user.</param>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.UserCredential.#ctor(Google.Apis.Auth.OAuth2.Flows.IAuthorizationCodeFlow,System.String,Google.Apis.Auth.OAuth2.Responses.TokenResponse,System.String)">
            <summary>Constructs a new credential instance.</summary>
            <param name="flow">Authorization code flow.</param>
            <param name="userId">User identifier.</param>
            <param name="token">An initial token for the user.</param>
            <param name="quotaProjectId">The ID of the project associated 
            to this credential for the purposes of quota calculation and billing. Can be null.</param>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.UserCredential.Google#Apis#Auth#OAuth2#IGoogleCredential#GetUniverseDomainAsync(System.Threading.CancellationToken)">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.UserCredential.Google#Apis#Auth#OAuth2#IGoogleCredential#GetUniverseDomain">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.UserCredential.Google#Apis#Auth#OAuth2#IGoogleCredential#WithQuotaProject(System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.UserCredential.Google#Apis#Auth#OAuth2#IGoogleCredential#MaybeWithScopes(System.Collections.Generic.IEnumerable{System.String})">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.UserCredential.Google#Apis#Auth#OAuth2#IGoogleCredential#WithUserForDomainWideDelegation(System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.UserCredential.Google#Apis#Auth#OAuth2#IGoogleCredential#WithHttpClientFactory(Google.Apis.Http.IHttpClientFactory)">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.UserCredential.Google#Apis#Auth#OAuth2#IGoogleCredential#WithUniverseDomain(System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.UserCredential.InterceptAsync(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
            <summary>
            Default implementation is to try to refresh the access token if there is no access token or if we are 1 
            minute away from expiration. If token server is unavailable, it will try to use the access token even if 
            has expired. If successful, it will call <see cref="M:Google.Apis.Auth.OAuth2.IAccessMethod.Intercept(System.Net.Http.HttpRequestMessage,System.String)"/>.
            </summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.UserCredential.HandleResponseAsync(Google.Apis.Http.HandleUnsuccessfulResponseArgs)">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.UserCredential.Initialize(Google.Apis.Http.ConfigurableHttpClient)">
            <inheritdoc/>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.UserCredential.GetAccessTokenForRequestAsync(System.String,System.Threading.CancellationToken)">
            <inheritdoc />
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.UserCredential.GetAccessTokenWithHeadersForRequestAsync(System.String,System.Threading.CancellationToken)">
            <inheritdoc />
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.UserCredential.RefreshTokenAsync(System.Threading.CancellationToken)">
            <summary>
            Refreshes the token by calling to
            <see cref="M:Google.Apis.Auth.OAuth2.Flows.IAuthorizationCodeFlow.RefreshTokenAsync(System.String,System.String,System.Threading.CancellationToken)"/>.
            Then it updates the <see cref="T:Google.Apis.Auth.OAuth2.Responses.TokenResponse"/> with the new token instance.
            </summary>
            <param name="taskCancellationToken">Cancellation token to cancel an operation.</param>
            <returns><c>true</c> if the token was refreshed.</returns>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.UserCredential.RevokeTokenAsync(System.Threading.CancellationToken)">
            <summary>
            Asynchronously revokes the token by calling
            <see cref="M:Google.Apis.Auth.OAuth2.Flows.IAuthorizationCodeFlow.RevokeTokenAsync(System.String,System.String,System.Threading.CancellationToken)"/>.
            </summary>
            <param name="taskCancellationToken">Cancellation token to cancel an operation.</param>
            <returns><c>true</c> if the token was revoked successfully.</returns>
        </member>
        <member name="T:Google.Apis.Auth.OAuth2.Web.AuthorizationCodeWebApp">
            <summary>
            Thread safe OAuth 2.0 authorization code flow for a web application that persists end-user credentials.
            </summary>
        </member>
        <member name="F:Google.Apis.Auth.OAuth2.Web.AuthorizationCodeWebApp.StateKey">
            <summary>
            The state key. As part of making the request for authorization code we save the original request to verify 
            that this server create the original request.
            </summary>
        </member>
        <member name="F:Google.Apis.Auth.OAuth2.Web.AuthorizationCodeWebApp.StateRandomLength">
            <summary>The length of the random number which will be added to the end of the state parameter.</summary>
        </member>
        <member name="T:Google.Apis.Auth.OAuth2.Web.AuthorizationCodeWebApp.AuthResult">
            <summary>
            AuthResult which contains the user's credentials if it was loaded successfully from the store. Otherwise
            it contains the redirect URI for the authorization server.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Web.AuthorizationCodeWebApp.AuthResult.Credential">
            <summary>
            Gets or sets the user's credentials or <c>null</c> in case the end user needs to authorize.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Web.AuthorizationCodeWebApp.AuthResult.RedirectUri">
            <summary>
            Gets or sets the redirect URI to for the user to authorize against the authorization server or 
            <c>null</c> in case the <see cref="T:Google.Apis.Auth.OAuth2.UserCredential"/> was loaded from the data 
            store.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Web.AuthorizationCodeWebApp.Flow">
            <summary>Gets the authorization code flow.</summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Web.AuthorizationCodeWebApp.RedirectUri">
            <summary>Gets the OAuth2 callback redirect URI.</summary>
        </member>
        <member name="P:Google.Apis.Auth.OAuth2.Web.AuthorizationCodeWebApp.State">
            <summary>Gets the state which is used to navigate back to the page that started the OAuth flow.</summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.Web.AuthorizationCodeWebApp.#ctor(Google.Apis.Auth.OAuth2.Flows.IAuthorizationCodeFlow,System.String,System.String)">
            <summary>
            Constructs a new authorization code installed application with the given flow and code receiver.
            </summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.Web.AuthorizationCodeWebApp.AuthorizeAsync(System.String,System.Threading.CancellationToken)">
            <summary>Asynchronously authorizes the web application to access user's protected data.</summary>
            <param name="userId">User identifier</param>
            <param name="taskCancellationToken">Cancellation token to cancel an operation</param>
            <returns>
            Auth result object which contains the user's credential or redirect URI for the authorization server
            </returns>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.Web.AuthorizationCodeWebApp.ShouldRequestAuthorizationCode(Google.Apis.Auth.OAuth2.Responses.TokenResponse)">
            <summary>
            Determines the need for retrieval of a new authorization code, based on the given token and the 
            authorization code flow.
            </summary>
        </member>
        <member name="T:Google.Apis.Auth.OAuth2.Web.AuthWebUtility">
            <summary>Auth Utility methods for web development.</summary>
        </member>
        <member name="M:Google.Apis.Auth.OAuth2.Web.AuthWebUtility.ExtracRedirectFromState(Google.Apis.Util.Store.IDataStore,System.String,System.String)">
            <summary>Extracts the redirect URI from the state OAuth2 parameter.</summary>
            <remarks>
            If the data store is not <c>null</c>, this method verifies that the state parameter which was returned
            from the authorization server is the same as the one we set before redirecting to the authorization server.
            </remarks>
            <param name="dataStore">The data store which contains the original state parameter.</param>
            <param name="userId">User identifier.</param>
            <param name="state">
            The authorization state parameter which we got back from the authorization server.
            </param>
            <returns>Redirect URI to the address which initializes the authorization code flow.</returns>
        </member>
        <member name="T:Google.Apis.Auth.SignedToken`2">
            <summary>
            Represents a signed token, could be a <see cref="T:Google.Apis.Auth.JsonWebSignature"/> or
            a <see cref="T:Google.Apis.Auth.GoogleJsonWebSignature"/> but this not only holds the payload
            and headers, but also the signature itself. It's meant to help with signed
            token verification and with obtaining token information.
            </summary>
        </member>
        <member name="T:Google.Apis.Auth.SignedTokenVerificationOptions">
            <summary>
            Options to use when verifying signed JWTs.
            </summary>
        </member>
        <member name="M:Google.Apis.Auth.SignedTokenVerificationOptions.#ctor">
            <summary>
            Creates a new instance of <see cref="T:Google.Apis.Auth.SignedTokenVerificationOptions"/>
            with default values for all options (or null for those whose default is unset).
            </summary>
        </member>
        <member name="M:Google.Apis.Auth.SignedTokenVerificationOptions.#ctor(Google.Apis.Auth.SignedTokenVerificationOptions)">
            <summary>
            Creates a new instance of <see cref="T:Google.Apis.Auth.SignedTokenVerificationOptions"/>
            by copying over all the values from <paramref name="other"/>.
            </summary>
            <param name="other">The option set to build this instance from.</param>
        </member>
        <member name="P:Google.Apis.Auth.SignedTokenVerificationOptions.TrustedAudiences">
            <summary>
            Trusted audiences for the token.
            All the audiences the token is intended for should be in the
            trusted audiences list.
            If the list is empty, the token audience won't be verified.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.SignedTokenVerificationOptions.CertificatesUrl">
            <summary>
            The URL from where to obtain certificates from.
            May be null, in which case, default certificate locations will be used:
            <list type="bullet">
            <item>For RS256 signed certificates, https://www.googleapis.com/oauth2/v3/certs will be used.</item>
            <item>For ES256 signed certificates, https://www.gstatic.com/iap/verify/public_key-jwk will be used.</item>
            </list>
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.SignedTokenVerificationOptions.TrustedIssuers">
            <summary>
            List of trusted issuers to verify the token issuer against.
            The token issuer must be contained in this list.
            May be null, in which case the token issuer won't be verified.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.SignedTokenVerificationOptions.ForceCertificateRefresh">
            <summary>
            Forces certificate refresh.
            Internal to be used only for backward compatibility.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.SignedTokenVerificationOptions.IssuedAtClockTolerance">
            <summary>
            Clock tolerance for the issued-at check.
            Causes a JWT to pass validation up to this duration before it is really valid;
            this is to allow for possible local-client clock skew.
            Defaults to zero.
            Internal to be used only for backward compatibility.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.SignedTokenVerificationOptions.ExpiryClockTolerance">
            <summary>
            Clock tolerance for the expiration check.
            Causes a JWT to pass validation up to this duration after it really expired;
            this is to allow for possible local-client clock skew.
            Defaults to zero.
            Internal to be used only for backward compatibility.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.SignedTokenVerificationOptions.Clock">
            <summary>
            Clock for testing purposes. Defaults to <see cref="F:Google.Apis.Util.SystemClock.Default"/>.
            Must not be null.
            </summary>
        </member>
        <member name="P:Google.Apis.Auth.SignedTokenVerificationOptions.CertificateCache">
            <summary>
            CertificateCache for testing purposes.
            If null, the true CertificateCache will be used.
            </summary>
        </member>
        <member name="M:Google.Apis.Auth.TaskExtensions.WithCancellationToken``1(System.Threading.Tasks.Task{``0},System.Threading.CancellationToken)">
            <summary>
            Returns a task which can be cancelled by the given cancellation token, but otherwise observes the original
            task's state. This does *not* cancel any work that the original task was doing, and should be used carefully.
            </summary>
        </member>
        <member name="M:Google.Apis.Auth.TokenEncodingHelpers.Base64UrlToString(System.String)">
            <summary>
            Decodes the provided URL safe base 64 string.
            </summary>
            <param name="base64Url">The URL safe base 64 string to decode.</param>
            <returns>The UTF8 decoded string.</returns>
        </member>
        <member name="M:Google.Apis.Auth.TokenEncodingHelpers.Base64UrlDecode(System.String)">
            <summary>
            Decodes the provided URL safe base 64 string.
            </summary>
            <param name="base64Url">The URL safe base 64 string to decode.</param>
            <returns>The UTF8 byte representation of the decoded string.</returns>
        </member>
        <member name="M:Google.Apis.Auth.TokenEncodingHelpers.UrlSafeBase64Encode(System.String)">
            <summary>Encodes the provided UTF8 string into an URL safe base64 string.</summary>
            <param name="value">Value to encode.</param>
            <returns>The URL safe base64 string.</returns>
        </member>
        <member name="M:Google.Apis.Auth.TokenEncodingHelpers.UrlSafeBase64Encode(System.Byte[])">
            <summary>Encodes the byte array into an URL safe base64 string.</summary>
            <param name="bytes">Byte array to encode.</param>
            <returns>The URL safe base64 string.</returns>
        </member>
        <member name="M:Google.Apis.Auth.TokenEncodingHelpers.UrlSafeEncode(System.String)">
            <summary>Encodes the base64 string into an URL safe string.</summary>
            <param name="base64Value">The base64 string to make URL safe.</param>
            <returns>The URL safe base64 string.</returns>
        </member>
    </members>
</doc>
