﻿using Google.Apis.Util;
using Google.Events.Protobuf.Cloud.PubSub.V1;
using Microsoft.Extensions.Logging;
using Smart.Cloud.Core.Logging;
using Smart.Cloud.Core.Logging.LogDataTypes;
using Smart.Cloud.Core.PubSub.Interfaces;
using Smart.Cloud.Core.PubSub.Services;
using Smart.Cloud.JeremiahPOC.Core.Interfaces.IServices;
using Smart.Cloud.JeremiahPOC.Core.Models;

namespace Smart.Cloud.JeremiahPOC.Services.Services;

public class ProcessMessageService : IProcessMessageService
{
    private readonly IPayloadHandlerService _payloadHandler;
    private readonly IPublishMessageService _publishMessageService;
    private readonly IApiConnectorService _apiConnectorService;
    private readonly ILogger<ProcessMessageService> _logger;

    public ProcessMessageService(IPayloadHandlerService payloadHandler,
        IPublishMessageService publishMessageService,
        IApiConnectorService apiConnectorService, 
        ILogger<ProcessMessageService> logger)
    {
        _payloadHandler = payloadHandler;
        _publishMessageService = publishMessageService;
        _apiConnectorService = apiConnectorService;
        _logger = logger;
    }

    public async Task ProcessMessage(MessagePublishedData data)
    {
        // Log the raw message data
        _logger.LogInformation("Received message: {@MessagePublishedData}", data);

        var payloadDetails = _payloadHandler.DeserializePayload<Payload>(data);

        // Log the deserialized payload
        _logger.LogInformation("Deserialized payload: {@PayloadDetails}", payloadDetails);

        // Send new pub-sub message if needed
        _ = PublishMessage(payloadDetails.Body.Payload);

        // Send request to API if needed
        if (payloadDetails.Body.Payload.EmployeeRequest.InputType.Contains("create"))
        {
            await _apiConnectorService.SendToApiPost(payloadDetails.Body.Payload.EmployeeRequest);
        }
        else
        {
            await _apiConnectorService.SendToApiGet(payloadDetails.Body.Payload.EmployeeRequest.EmployeeId);
        }
    }

    private async Task PublishMessage(Payload payload)
    {
        // Set the desired header details

        var header = _payloadHandler.CreateHeader(
            "1.0.0",
            "sender",
            "event",
            "eventType",
            "actionType",
            "actionStatus", 
            new Guid(),
            DateTimeOffset.UtcNow,
            "actor");

        var message = _payloadHandler.GenerateEventMessage(header, payload);

        await _publishMessageService.PublishMessageAsync(message, "system.demo.template.add");
    }
}