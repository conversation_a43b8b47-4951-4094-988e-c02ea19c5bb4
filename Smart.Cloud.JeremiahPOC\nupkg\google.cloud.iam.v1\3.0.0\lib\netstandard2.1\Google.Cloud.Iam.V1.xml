<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Google.Cloud.Iam.V1</name>
    </assembly>
    <members>
        <member name="T:Google.Cloud.Iam.V1.IamPolicyReflection">
            <summary>Holder for reflection information generated from google/iam/v1/iam_policy.proto</summary>
        </member>
        <member name="P:Google.Cloud.Iam.V1.IamPolicyReflection.Descriptor">
            <summary>File descriptor for google/iam/v1/iam_policy.proto</summary>
        </member>
        <member name="T:Google.Cloud.Iam.V1.SetIamPolicyRequest">
            <summary>
            Request message for `SetIamPolicy` method.
            </summary>
        </member>
        <member name="F:Google.Cloud.Iam.V1.SetIamPolicyRequest.ResourceFieldNumber">
            <summary>Field number for the "resource" field.</summary>
        </member>
        <member name="P:Google.Cloud.Iam.V1.SetIamPolicyRequest.Resource">
            <summary>
            REQUIRED: The resource for which the policy is being specified.
            See the operation documentation for the appropriate value for this field.
            </summary>
        </member>
        <member name="F:Google.Cloud.Iam.V1.SetIamPolicyRequest.PolicyFieldNumber">
            <summary>Field number for the "policy" field.</summary>
        </member>
        <member name="P:Google.Cloud.Iam.V1.SetIamPolicyRequest.Policy">
            <summary>
            REQUIRED: The complete policy to be applied to the `resource`. The size of
            the policy is limited to a few 10s of KB. An empty policy is a
            valid policy but certain Cloud Platform services (such as Projects)
            might reject them.
            </summary>
        </member>
        <member name="F:Google.Cloud.Iam.V1.SetIamPolicyRequest.UpdateMaskFieldNumber">
            <summary>Field number for the "update_mask" field.</summary>
        </member>
        <member name="P:Google.Cloud.Iam.V1.SetIamPolicyRequest.UpdateMask">
             <summary>
             OPTIONAL: A FieldMask specifying which fields of the policy to modify. Only
             the fields in the mask will be modified. If no mask is provided, the
             following default mask is used:
            
             `paths: "bindings, etag"`
             </summary>
        </member>
        <member name="P:Google.Cloud.Iam.V1.SetIamPolicyRequest.ResourceAsResourceName">
            <summary>
            <see cref="T:Google.Api.Gax.IResourceName"/>-typed view over the <see cref="P:Google.Cloud.Iam.V1.SetIamPolicyRequest.Resource"/> resource name property.
            </summary>
        </member>
        <member name="T:Google.Cloud.Iam.V1.GetIamPolicyRequest">
            <summary>
            Request message for `GetIamPolicy` method.
            </summary>
        </member>
        <member name="F:Google.Cloud.Iam.V1.GetIamPolicyRequest.ResourceFieldNumber">
            <summary>Field number for the "resource" field.</summary>
        </member>
        <member name="P:Google.Cloud.Iam.V1.GetIamPolicyRequest.Resource">
            <summary>
            REQUIRED: The resource for which the policy is being requested.
            See the operation documentation for the appropriate value for this field.
            </summary>
        </member>
        <member name="F:Google.Cloud.Iam.V1.GetIamPolicyRequest.OptionsFieldNumber">
            <summary>Field number for the "options" field.</summary>
        </member>
        <member name="P:Google.Cloud.Iam.V1.GetIamPolicyRequest.Options">
            <summary>
            OPTIONAL: A `GetPolicyOptions` object for specifying options to
            `GetIamPolicy`.
            </summary>
        </member>
        <member name="P:Google.Cloud.Iam.V1.GetIamPolicyRequest.ResourceAsResourceName">
            <summary>
            <see cref="T:Google.Api.Gax.IResourceName"/>-typed view over the <see cref="P:Google.Cloud.Iam.V1.GetIamPolicyRequest.Resource"/> resource name property.
            </summary>
        </member>
        <member name="T:Google.Cloud.Iam.V1.TestIamPermissionsRequest">
            <summary>
            Request message for `TestIamPermissions` method.
            </summary>
        </member>
        <member name="F:Google.Cloud.Iam.V1.TestIamPermissionsRequest.ResourceFieldNumber">
            <summary>Field number for the "resource" field.</summary>
        </member>
        <member name="P:Google.Cloud.Iam.V1.TestIamPermissionsRequest.Resource">
            <summary>
            REQUIRED: The resource for which the policy detail is being requested.
            See the operation documentation for the appropriate value for this field.
            </summary>
        </member>
        <member name="F:Google.Cloud.Iam.V1.TestIamPermissionsRequest.PermissionsFieldNumber">
            <summary>Field number for the "permissions" field.</summary>
        </member>
        <member name="P:Google.Cloud.Iam.V1.TestIamPermissionsRequest.Permissions">
            <summary>
            The set of permissions to check for the `resource`. Permissions with
            wildcards (such as '*' or 'storage.*') are not allowed. For more
            information see
            [IAM Overview](https://cloud.google.com/iam/docs/overview#permissions).
            </summary>
        </member>
        <member name="P:Google.Cloud.Iam.V1.TestIamPermissionsRequest.ResourceAsResourceName">
            <summary>
            <see cref="T:Google.Api.Gax.IResourceName"/>-typed view over the <see cref="P:Google.Cloud.Iam.V1.TestIamPermissionsRequest.Resource"/> resource name property.
            </summary>
        </member>
        <member name="T:Google.Cloud.Iam.V1.TestIamPermissionsResponse">
            <summary>
            Response message for `TestIamPermissions` method.
            </summary>
        </member>
        <member name="F:Google.Cloud.Iam.V1.TestIamPermissionsResponse.PermissionsFieldNumber">
            <summary>Field number for the "permissions" field.</summary>
        </member>
        <member name="P:Google.Cloud.Iam.V1.TestIamPermissionsResponse.Permissions">
            <summary>
            A subset of `TestPermissionsRequest.permissions` that the caller is
            allowed.
            </summary>
        </member>
        <member name="T:Google.Cloud.Iam.V1.IAMPolicySettings">
            <summary>Settings for <see cref="T:Google.Cloud.Iam.V1.IAMPolicyClient"/> instances.</summary>
        </member>
        <member name="M:Google.Cloud.Iam.V1.IAMPolicySettings.GetDefault">
            <summary>Get a new instance of the default <see cref="T:Google.Cloud.Iam.V1.IAMPolicySettings"/>.</summary>
            <returns>A new instance of the default <see cref="T:Google.Cloud.Iam.V1.IAMPolicySettings"/>.</returns>
        </member>
        <member name="M:Google.Cloud.Iam.V1.IAMPolicySettings.#ctor">
            <summary>Constructs a new <see cref="T:Google.Cloud.Iam.V1.IAMPolicySettings"/> object with default settings.</summary>
        </member>
        <member name="P:Google.Cloud.Iam.V1.IAMPolicySettings.SetIamPolicySettings">
            <summary>
            <see cref="T:Google.Api.Gax.Grpc.CallSettings"/> for synchronous and asynchronous calls to
            <c>IAMPolicyClient.SetIamPolicy</c> and <c>IAMPolicyClient.SetIamPolicyAsync</c>.
            </summary>
            <remarks>
            <list type="bullet">
            <item><description>This call will not be retried.</description></item>
            <item><description>No timeout is applied.</description></item>
            </list>
            </remarks>
        </member>
        <member name="P:Google.Cloud.Iam.V1.IAMPolicySettings.GetIamPolicySettings">
            <summary>
            <see cref="T:Google.Api.Gax.Grpc.CallSettings"/> for synchronous and asynchronous calls to
            <c>IAMPolicyClient.GetIamPolicy</c> and <c>IAMPolicyClient.GetIamPolicyAsync</c>.
            </summary>
            <remarks>
            <list type="bullet">
            <item><description>This call will not be retried.</description></item>
            <item><description>No timeout is applied.</description></item>
            </list>
            </remarks>
        </member>
        <member name="P:Google.Cloud.Iam.V1.IAMPolicySettings.TestIamPermissionsSettings">
            <summary>
            <see cref="T:Google.Api.Gax.Grpc.CallSettings"/> for synchronous and asynchronous calls to
            <c>IAMPolicyClient.TestIamPermissions</c> and <c>IAMPolicyClient.TestIamPermissionsAsync</c>.
            </summary>
            <remarks>
            <list type="bullet">
            <item><description>This call will not be retried.</description></item>
            <item><description>No timeout is applied.</description></item>
            </list>
            </remarks>
        </member>
        <member name="M:Google.Cloud.Iam.V1.IAMPolicySettings.Clone">
            <summary>Creates a deep clone of this object, with all the same property values.</summary>
            <returns>A deep clone of this <see cref="T:Google.Cloud.Iam.V1.IAMPolicySettings"/> object.</returns>
        </member>
        <member name="T:Google.Cloud.Iam.V1.IAMPolicyClientBuilder">
            <summary>
            Builder class for <see cref="T:Google.Cloud.Iam.V1.IAMPolicyClient"/> to provide simple configuration of credentials, endpoint etc.
            </summary>
        </member>
        <member name="P:Google.Cloud.Iam.V1.IAMPolicyClientBuilder.Settings">
            <summary>The settings to use for RPCs, or <c>null</c> for the default settings.</summary>
        </member>
        <member name="M:Google.Cloud.Iam.V1.IAMPolicyClientBuilder.#ctor">
            <summary>Creates a new builder with default settings.</summary>
        </member>
        <member name="M:Google.Cloud.Iam.V1.IAMPolicyClientBuilder.Build">
            <summary>Builds the resulting client.</summary>
        </member>
        <member name="M:Google.Cloud.Iam.V1.IAMPolicyClientBuilder.BuildAsync(System.Threading.CancellationToken)">
            <summary>Builds the resulting client asynchronously.</summary>
        </member>
        <member name="M:Google.Cloud.Iam.V1.IAMPolicyClientBuilder.GetChannelPool">
            <summary>Returns the channel pool to use when no other options are specified.</summary>
        </member>
        <member name="T:Google.Cloud.Iam.V1.IAMPolicyClient">
            <summary>IAMPolicy client wrapper, for convenient use.</summary>
            <remarks>
            API Overview
            
            
            Manages Identity and Access Management (IAM) policies.
            
            Any implementation of an API that offers access control features
            implements the google.iam.v1.IAMPolicy interface.
            
            ## Data model
            
            Access control is applied when a principal (user or service account), takes
            some action on a resource exposed by a service. Resources, identified by
            URI-like names, are the unit of access control specification. Service
            implementations can choose the granularity of access control and the
            supported permissions for their resources.
            For example one database service may allow access control to be
            specified only at the Table level, whereas another might allow access control
            to also be specified at the Column level.
            
            ## Policy Structure
            
            See google.iam.v1.Policy
            
            This is intentionally not a CRUD style API because access control policies
            are created and deleted implicitly with the resources to which they are
            attached.
            </remarks>
        </member>
        <member name="P:Google.Cloud.Iam.V1.IAMPolicyClient.DefaultEndpoint">
            <summary>
            The default endpoint for the IAMPolicy service, which is a host of "iam-meta-api.googleapis.com" and a port
            of 443.
            </summary>
        </member>
        <member name="P:Google.Cloud.Iam.V1.IAMPolicyClient.DefaultScopes">
            <summary>The default IAMPolicy scopes.</summary>
            <remarks>The default IAMPolicy scopes are:<list type="bullet"></list></remarks>
        </member>
        <member name="P:Google.Cloud.Iam.V1.IAMPolicyClient.ServiceMetadata">
            <summary>The service metadata associated with this client type.</summary>
        </member>
        <member name="M:Google.Cloud.Iam.V1.IAMPolicyClient.CreateAsync(System.Threading.CancellationToken)">
            <summary>
            Asynchronously creates a <see cref="T:Google.Cloud.Iam.V1.IAMPolicyClient"/> using the default credentials, endpoint and settings.
            To specify custom credentials or other settings, use <see cref="T:Google.Cloud.Iam.V1.IAMPolicyClientBuilder"/>.
            </summary>
            <param name="cancellationToken">
            The <see cref="T:System.Threading.CancellationToken"/> to use while creating the client.
            </param>
            <returns>The task representing the created <see cref="T:Google.Cloud.Iam.V1.IAMPolicyClient"/>.</returns>
        </member>
        <member name="M:Google.Cloud.Iam.V1.IAMPolicyClient.Create">
            <summary>
            Synchronously creates a <see cref="T:Google.Cloud.Iam.V1.IAMPolicyClient"/> using the default credentials, endpoint and settings. 
            To specify custom credentials or other settings, use <see cref="T:Google.Cloud.Iam.V1.IAMPolicyClientBuilder"/>.
            </summary>
            <returns>The created <see cref="T:Google.Cloud.Iam.V1.IAMPolicyClient"/>.</returns>
        </member>
        <member name="M:Google.Cloud.Iam.V1.IAMPolicyClient.Create(Grpc.Core.CallInvoker,Google.Cloud.Iam.V1.IAMPolicySettings,Microsoft.Extensions.Logging.ILogger)">
            <summary>
            Creates a <see cref="T:Google.Cloud.Iam.V1.IAMPolicyClient"/> which uses the specified call invoker for remote operations.
            </summary>
            <param name="callInvoker">
            The <see cref="T:Grpc.Core.CallInvoker"/> for remote operations. Must not be null.
            </param>
            <param name="settings">Optional <see cref="T:Google.Cloud.Iam.V1.IAMPolicySettings"/>.</param>
            <param name="logger">Optional <see cref="T:Microsoft.Extensions.Logging.ILogger"/>.</param>
            <returns>The created <see cref="T:Google.Cloud.Iam.V1.IAMPolicyClient"/>.</returns>
        </member>
        <member name="M:Google.Cloud.Iam.V1.IAMPolicyClient.ShutdownDefaultChannelsAsync">
            <summary>
            Shuts down any channels automatically created by <see cref="M:Google.Cloud.Iam.V1.IAMPolicyClient.Create"/> and
            <see cref="M:Google.Cloud.Iam.V1.IAMPolicyClient.CreateAsync(System.Threading.CancellationToken)"/>. Channels which weren't automatically created are not
            affected.
            </summary>
            <remarks>
            After calling this method, further calls to <see cref="M:Google.Cloud.Iam.V1.IAMPolicyClient.Create"/> and
            <see cref="M:Google.Cloud.Iam.V1.IAMPolicyClient.CreateAsync(System.Threading.CancellationToken)"/> will create new channels, which could in turn be shut down
            by another call to this method.
            </remarks>
            <returns>A task representing the asynchronous shutdown operation.</returns>
        </member>
        <member name="P:Google.Cloud.Iam.V1.IAMPolicyClient.GrpcClient">
            <summary>The underlying gRPC IAMPolicy client</summary>
        </member>
        <member name="M:Google.Cloud.Iam.V1.IAMPolicyClient.SetIamPolicy(Google.Cloud.Iam.V1.SetIamPolicyRequest,Google.Api.Gax.Grpc.CallSettings)">
            <summary>
            Sets the access control policy on the specified resource. Replaces any
            existing policy.
            
            Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.
            </summary>
            <param name="request">The request object containing all of the parameters for the API call.</param>
            <param name="callSettings">If not null, applies overrides to this RPC call.</param>
            <returns>The RPC response.</returns>
        </member>
        <member name="M:Google.Cloud.Iam.V1.IAMPolicyClient.SetIamPolicyAsync(Google.Cloud.Iam.V1.SetIamPolicyRequest,Google.Api.Gax.Grpc.CallSettings)">
            <summary>
            Sets the access control policy on the specified resource. Replaces any
            existing policy.
            
            Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.
            </summary>
            <param name="request">The request object containing all of the parameters for the API call.</param>
            <param name="callSettings">If not null, applies overrides to this RPC call.</param>
            <returns>A Task containing the RPC response.</returns>
        </member>
        <member name="M:Google.Cloud.Iam.V1.IAMPolicyClient.SetIamPolicyAsync(Google.Cloud.Iam.V1.SetIamPolicyRequest,System.Threading.CancellationToken)">
            <summary>
            Sets the access control policy on the specified resource. Replaces any
            existing policy.
            
            Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.
            </summary>
            <param name="request">The request object containing all of the parameters for the API call.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken"/> to use for this RPC.</param>
            <returns>A Task containing the RPC response.</returns>
        </member>
        <member name="M:Google.Cloud.Iam.V1.IAMPolicyClient.GetIamPolicy(Google.Cloud.Iam.V1.GetIamPolicyRequest,Google.Api.Gax.Grpc.CallSettings)">
            <summary>
            Gets the access control policy for a resource.
            Returns an empty policy if the resource exists and does not have a policy
            set.
            </summary>
            <param name="request">The request object containing all of the parameters for the API call.</param>
            <param name="callSettings">If not null, applies overrides to this RPC call.</param>
            <returns>The RPC response.</returns>
        </member>
        <member name="M:Google.Cloud.Iam.V1.IAMPolicyClient.GetIamPolicyAsync(Google.Cloud.Iam.V1.GetIamPolicyRequest,Google.Api.Gax.Grpc.CallSettings)">
            <summary>
            Gets the access control policy for a resource.
            Returns an empty policy if the resource exists and does not have a policy
            set.
            </summary>
            <param name="request">The request object containing all of the parameters for the API call.</param>
            <param name="callSettings">If not null, applies overrides to this RPC call.</param>
            <returns>A Task containing the RPC response.</returns>
        </member>
        <member name="M:Google.Cloud.Iam.V1.IAMPolicyClient.GetIamPolicyAsync(Google.Cloud.Iam.V1.GetIamPolicyRequest,System.Threading.CancellationToken)">
            <summary>
            Gets the access control policy for a resource.
            Returns an empty policy if the resource exists and does not have a policy
            set.
            </summary>
            <param name="request">The request object containing all of the parameters for the API call.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken"/> to use for this RPC.</param>
            <returns>A Task containing the RPC response.</returns>
        </member>
        <member name="M:Google.Cloud.Iam.V1.IAMPolicyClient.TestIamPermissions(Google.Cloud.Iam.V1.TestIamPermissionsRequest,Google.Api.Gax.Grpc.CallSettings)">
            <summary>
            Returns permissions that a caller has on the specified resource.
            If the resource does not exist, this will return an empty set of
            permissions, not a `NOT_FOUND` error.
            
            Note: This operation is designed to be used for building permission-aware
            UIs and command-line tools, not for authorization checking. This operation
            may "fail open" without warning.
            </summary>
            <param name="request">The request object containing all of the parameters for the API call.</param>
            <param name="callSettings">If not null, applies overrides to this RPC call.</param>
            <returns>The RPC response.</returns>
        </member>
        <member name="M:Google.Cloud.Iam.V1.IAMPolicyClient.TestIamPermissionsAsync(Google.Cloud.Iam.V1.TestIamPermissionsRequest,Google.Api.Gax.Grpc.CallSettings)">
            <summary>
            Returns permissions that a caller has on the specified resource.
            If the resource does not exist, this will return an empty set of
            permissions, not a `NOT_FOUND` error.
            
            Note: This operation is designed to be used for building permission-aware
            UIs and command-line tools, not for authorization checking. This operation
            may "fail open" without warning.
            </summary>
            <param name="request">The request object containing all of the parameters for the API call.</param>
            <param name="callSettings">If not null, applies overrides to this RPC call.</param>
            <returns>A Task containing the RPC response.</returns>
        </member>
        <member name="M:Google.Cloud.Iam.V1.IAMPolicyClient.TestIamPermissionsAsync(Google.Cloud.Iam.V1.TestIamPermissionsRequest,System.Threading.CancellationToken)">
            <summary>
            Returns permissions that a caller has on the specified resource.
            If the resource does not exist, this will return an empty set of
            permissions, not a `NOT_FOUND` error.
            
            Note: This operation is designed to be used for building permission-aware
            UIs and command-line tools, not for authorization checking. This operation
            may "fail open" without warning.
            </summary>
            <param name="request">The request object containing all of the parameters for the API call.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken"/> to use for this RPC.</param>
            <returns>A Task containing the RPC response.</returns>
        </member>
        <member name="T:Google.Cloud.Iam.V1.IAMPolicyClientImpl">
            <summary>IAMPolicy client wrapper implementation, for convenient use.</summary>
            <remarks>
            API Overview
            
            
            Manages Identity and Access Management (IAM) policies.
            
            Any implementation of an API that offers access control features
            implements the google.iam.v1.IAMPolicy interface.
            
            ## Data model
            
            Access control is applied when a principal (user or service account), takes
            some action on a resource exposed by a service. Resources, identified by
            URI-like names, are the unit of access control specification. Service
            implementations can choose the granularity of access control and the
            supported permissions for their resources.
            For example one database service may allow access control to be
            specified only at the Table level, whereas another might allow access control
            to also be specified at the Column level.
            
            ## Policy Structure
            
            See google.iam.v1.Policy
            
            This is intentionally not a CRUD style API because access control policies
            are created and deleted implicitly with the resources to which they are
            attached.
            </remarks>
        </member>
        <member name="M:Google.Cloud.Iam.V1.IAMPolicyClientImpl.#ctor(Google.Cloud.Iam.V1.IAMPolicy.IAMPolicyClient,Google.Cloud.Iam.V1.IAMPolicySettings,Microsoft.Extensions.Logging.ILogger)">
            <summary>
            Constructs a client wrapper for the IAMPolicy service, with the specified gRPC client and settings.
            </summary>
            <param name="grpcClient">The underlying gRPC client.</param>
            <param name="settings">The base <see cref="T:Google.Cloud.Iam.V1.IAMPolicySettings"/> used within this client.</param>
            <param name="logger">Optional <see cref="T:Microsoft.Extensions.Logging.ILogger"/> to use within this client.</param>
        </member>
        <member name="P:Google.Cloud.Iam.V1.IAMPolicyClientImpl.GrpcClient">
            <summary>The underlying gRPC IAMPolicy client</summary>
        </member>
        <member name="M:Google.Cloud.Iam.V1.IAMPolicyClientImpl.SetIamPolicy(Google.Cloud.Iam.V1.SetIamPolicyRequest,Google.Api.Gax.Grpc.CallSettings)">
            <summary>
            Sets the access control policy on the specified resource. Replaces any
            existing policy.
            
            Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.
            </summary>
            <param name="request">The request object containing all of the parameters for the API call.</param>
            <param name="callSettings">If not null, applies overrides to this RPC call.</param>
            <returns>The RPC response.</returns>
        </member>
        <member name="M:Google.Cloud.Iam.V1.IAMPolicyClientImpl.SetIamPolicyAsync(Google.Cloud.Iam.V1.SetIamPolicyRequest,Google.Api.Gax.Grpc.CallSettings)">
            <summary>
            Sets the access control policy on the specified resource. Replaces any
            existing policy.
            
            Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.
            </summary>
            <param name="request">The request object containing all of the parameters for the API call.</param>
            <param name="callSettings">If not null, applies overrides to this RPC call.</param>
            <returns>A Task containing the RPC response.</returns>
        </member>
        <member name="M:Google.Cloud.Iam.V1.IAMPolicyClientImpl.GetIamPolicy(Google.Cloud.Iam.V1.GetIamPolicyRequest,Google.Api.Gax.Grpc.CallSettings)">
            <summary>
            Gets the access control policy for a resource.
            Returns an empty policy if the resource exists and does not have a policy
            set.
            </summary>
            <param name="request">The request object containing all of the parameters for the API call.</param>
            <param name="callSettings">If not null, applies overrides to this RPC call.</param>
            <returns>The RPC response.</returns>
        </member>
        <member name="M:Google.Cloud.Iam.V1.IAMPolicyClientImpl.GetIamPolicyAsync(Google.Cloud.Iam.V1.GetIamPolicyRequest,Google.Api.Gax.Grpc.CallSettings)">
            <summary>
            Gets the access control policy for a resource.
            Returns an empty policy if the resource exists and does not have a policy
            set.
            </summary>
            <param name="request">The request object containing all of the parameters for the API call.</param>
            <param name="callSettings">If not null, applies overrides to this RPC call.</param>
            <returns>A Task containing the RPC response.</returns>
        </member>
        <member name="M:Google.Cloud.Iam.V1.IAMPolicyClientImpl.TestIamPermissions(Google.Cloud.Iam.V1.TestIamPermissionsRequest,Google.Api.Gax.Grpc.CallSettings)">
            <summary>
            Returns permissions that a caller has on the specified resource.
            If the resource does not exist, this will return an empty set of
            permissions, not a `NOT_FOUND` error.
            
            Note: This operation is designed to be used for building permission-aware
            UIs and command-line tools, not for authorization checking. This operation
            may "fail open" without warning.
            </summary>
            <param name="request">The request object containing all of the parameters for the API call.</param>
            <param name="callSettings">If not null, applies overrides to this RPC call.</param>
            <returns>The RPC response.</returns>
        </member>
        <member name="M:Google.Cloud.Iam.V1.IAMPolicyClientImpl.TestIamPermissionsAsync(Google.Cloud.Iam.V1.TestIamPermissionsRequest,Google.Api.Gax.Grpc.CallSettings)">
            <summary>
            Returns permissions that a caller has on the specified resource.
            If the resource does not exist, this will return an empty set of
            permissions, not a `NOT_FOUND` error.
            
            Note: This operation is designed to be used for building permission-aware
            UIs and command-line tools, not for authorization checking. This operation
            may "fail open" without warning.
            </summary>
            <param name="request">The request object containing all of the parameters for the API call.</param>
            <param name="callSettings">If not null, applies overrides to this RPC call.</param>
            <returns>A Task containing the RPC response.</returns>
        </member>
        <member name="T:Google.Cloud.Iam.V1.IAMPolicy">
             <summary>
             API Overview
            
             Manages Identity and Access Management (IAM) policies.
            
             Any implementation of an API that offers access control features
             implements the google.iam.v1.IAMPolicy interface.
            
             ## Data model
            
             Access control is applied when a principal (user or service account), takes
             some action on a resource exposed by a service. Resources, identified by
             URI-like names, are the unit of access control specification. Service
             implementations can choose the granularity of access control and the
             supported permissions for their resources.
             For example one database service may allow access control to be
             specified only at the Table level, whereas another might allow access control
             to also be specified at the Column level.
            
             ## Policy Structure
            
             See google.iam.v1.Policy
            
             This is intentionally not a CRUD style API because access control policies
             are created and deleted implicitly with the resources to which they are
             attached.
             </summary>
        </member>
        <member name="P:Google.Cloud.Iam.V1.IAMPolicy.Descriptor">
            <summary>Service descriptor</summary>
        </member>
        <member name="T:Google.Cloud.Iam.V1.IAMPolicy.IAMPolicyBase">
            <summary>Base class for server-side implementations of IAMPolicy</summary>
        </member>
        <member name="M:Google.Cloud.Iam.V1.IAMPolicy.IAMPolicyBase.SetIamPolicy(Google.Cloud.Iam.V1.SetIamPolicyRequest,Grpc.Core.ServerCallContext)">
             <summary>
             Sets the access control policy on the specified resource. Replaces any
             existing policy.
            
             Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.
             </summary>
             <param name="request">The request received from the client.</param>
             <param name="context">The context of the server-side call handler being invoked.</param>
             <returns>The response to send back to the client (wrapped by a task).</returns>
        </member>
        <member name="M:Google.Cloud.Iam.V1.IAMPolicy.IAMPolicyBase.GetIamPolicy(Google.Cloud.Iam.V1.GetIamPolicyRequest,Grpc.Core.ServerCallContext)">
            <summary>
            Gets the access control policy for a resource.
            Returns an empty policy if the resource exists and does not have a policy
            set.
            </summary>
            <param name="request">The request received from the client.</param>
            <param name="context">The context of the server-side call handler being invoked.</param>
            <returns>The response to send back to the client (wrapped by a task).</returns>
        </member>
        <member name="M:Google.Cloud.Iam.V1.IAMPolicy.IAMPolicyBase.TestIamPermissions(Google.Cloud.Iam.V1.TestIamPermissionsRequest,Grpc.Core.ServerCallContext)">
             <summary>
             Returns permissions that a caller has on the specified resource.
             If the resource does not exist, this will return an empty set of
             permissions, not a `NOT_FOUND` error.
            
             Note: This operation is designed to be used for building permission-aware
             UIs and command-line tools, not for authorization checking. This operation
             may "fail open" without warning.
             </summary>
             <param name="request">The request received from the client.</param>
             <param name="context">The context of the server-side call handler being invoked.</param>
             <returns>The response to send back to the client (wrapped by a task).</returns>
        </member>
        <member name="T:Google.Cloud.Iam.V1.IAMPolicy.IAMPolicyClient">
            <summary>Client for IAMPolicy</summary>
        </member>
        <member name="M:Google.Cloud.Iam.V1.IAMPolicy.IAMPolicyClient.#ctor(Grpc.Core.ChannelBase)">
            <summary>Creates a new client for IAMPolicy</summary>
            <param name="channel">The channel to use to make remote calls.</param>
        </member>
        <member name="M:Google.Cloud.Iam.V1.IAMPolicy.IAMPolicyClient.#ctor(Grpc.Core.CallInvoker)">
            <summary>Creates a new client for IAMPolicy that uses a custom <c>CallInvoker</c>.</summary>
            <param name="callInvoker">The callInvoker to use to make remote calls.</param>
        </member>
        <member name="M:Google.Cloud.Iam.V1.IAMPolicy.IAMPolicyClient.#ctor">
            <summary>Protected parameterless constructor to allow creation of test doubles.</summary>
        </member>
        <member name="M:Google.Cloud.Iam.V1.IAMPolicy.IAMPolicyClient.#ctor(Grpc.Core.ClientBase.ClientBaseConfiguration)">
            <summary>Protected constructor to allow creation of configured clients.</summary>
            <param name="configuration">The client configuration.</param>
        </member>
        <member name="M:Google.Cloud.Iam.V1.IAMPolicy.IAMPolicyClient.SetIamPolicy(Google.Cloud.Iam.V1.SetIamPolicyRequest,Grpc.Core.Metadata,System.Nullable{System.DateTime},System.Threading.CancellationToken)">
             <summary>
             Sets the access control policy on the specified resource. Replaces any
             existing policy.
            
             Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.
             </summary>
             <param name="request">The request to send to the server.</param>
             <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
             <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
             <param name="cancellationToken">An optional token for canceling the call.</param>
             <returns>The response received from the server.</returns>
        </member>
        <member name="M:Google.Cloud.Iam.V1.IAMPolicy.IAMPolicyClient.SetIamPolicy(Google.Cloud.Iam.V1.SetIamPolicyRequest,Grpc.Core.CallOptions)">
             <summary>
             Sets the access control policy on the specified resource. Replaces any
             existing policy.
            
             Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.
             </summary>
             <param name="request">The request to send to the server.</param>
             <param name="options">The options for the call.</param>
             <returns>The response received from the server.</returns>
        </member>
        <member name="M:Google.Cloud.Iam.V1.IAMPolicy.IAMPolicyClient.SetIamPolicyAsync(Google.Cloud.Iam.V1.SetIamPolicyRequest,Grpc.Core.Metadata,System.Nullable{System.DateTime},System.Threading.CancellationToken)">
             <summary>
             Sets the access control policy on the specified resource. Replaces any
             existing policy.
            
             Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.
             </summary>
             <param name="request">The request to send to the server.</param>
             <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
             <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
             <param name="cancellationToken">An optional token for canceling the call.</param>
             <returns>The call object.</returns>
        </member>
        <member name="M:Google.Cloud.Iam.V1.IAMPolicy.IAMPolicyClient.SetIamPolicyAsync(Google.Cloud.Iam.V1.SetIamPolicyRequest,Grpc.Core.CallOptions)">
             <summary>
             Sets the access control policy on the specified resource. Replaces any
             existing policy.
            
             Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.
             </summary>
             <param name="request">The request to send to the server.</param>
             <param name="options">The options for the call.</param>
             <returns>The call object.</returns>
        </member>
        <member name="M:Google.Cloud.Iam.V1.IAMPolicy.IAMPolicyClient.GetIamPolicy(Google.Cloud.Iam.V1.GetIamPolicyRequest,Grpc.Core.Metadata,System.Nullable{System.DateTime},System.Threading.CancellationToken)">
            <summary>
            Gets the access control policy for a resource.
            Returns an empty policy if the resource exists and does not have a policy
            set.
            </summary>
            <param name="request">The request to send to the server.</param>
            <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
            <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
            <param name="cancellationToken">An optional token for canceling the call.</param>
            <returns>The response received from the server.</returns>
        </member>
        <member name="M:Google.Cloud.Iam.V1.IAMPolicy.IAMPolicyClient.GetIamPolicy(Google.Cloud.Iam.V1.GetIamPolicyRequest,Grpc.Core.CallOptions)">
            <summary>
            Gets the access control policy for a resource.
            Returns an empty policy if the resource exists and does not have a policy
            set.
            </summary>
            <param name="request">The request to send to the server.</param>
            <param name="options">The options for the call.</param>
            <returns>The response received from the server.</returns>
        </member>
        <member name="M:Google.Cloud.Iam.V1.IAMPolicy.IAMPolicyClient.GetIamPolicyAsync(Google.Cloud.Iam.V1.GetIamPolicyRequest,Grpc.Core.Metadata,System.Nullable{System.DateTime},System.Threading.CancellationToken)">
            <summary>
            Gets the access control policy for a resource.
            Returns an empty policy if the resource exists and does not have a policy
            set.
            </summary>
            <param name="request">The request to send to the server.</param>
            <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
            <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
            <param name="cancellationToken">An optional token for canceling the call.</param>
            <returns>The call object.</returns>
        </member>
        <member name="M:Google.Cloud.Iam.V1.IAMPolicy.IAMPolicyClient.GetIamPolicyAsync(Google.Cloud.Iam.V1.GetIamPolicyRequest,Grpc.Core.CallOptions)">
            <summary>
            Gets the access control policy for a resource.
            Returns an empty policy if the resource exists and does not have a policy
            set.
            </summary>
            <param name="request">The request to send to the server.</param>
            <param name="options">The options for the call.</param>
            <returns>The call object.</returns>
        </member>
        <member name="M:Google.Cloud.Iam.V1.IAMPolicy.IAMPolicyClient.TestIamPermissions(Google.Cloud.Iam.V1.TestIamPermissionsRequest,Grpc.Core.Metadata,System.Nullable{System.DateTime},System.Threading.CancellationToken)">
             <summary>
             Returns permissions that a caller has on the specified resource.
             If the resource does not exist, this will return an empty set of
             permissions, not a `NOT_FOUND` error.
            
             Note: This operation is designed to be used for building permission-aware
             UIs and command-line tools, not for authorization checking. This operation
             may "fail open" without warning.
             </summary>
             <param name="request">The request to send to the server.</param>
             <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
             <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
             <param name="cancellationToken">An optional token for canceling the call.</param>
             <returns>The response received from the server.</returns>
        </member>
        <member name="M:Google.Cloud.Iam.V1.IAMPolicy.IAMPolicyClient.TestIamPermissions(Google.Cloud.Iam.V1.TestIamPermissionsRequest,Grpc.Core.CallOptions)">
             <summary>
             Returns permissions that a caller has on the specified resource.
             If the resource does not exist, this will return an empty set of
             permissions, not a `NOT_FOUND` error.
            
             Note: This operation is designed to be used for building permission-aware
             UIs and command-line tools, not for authorization checking. This operation
             may "fail open" without warning.
             </summary>
             <param name="request">The request to send to the server.</param>
             <param name="options">The options for the call.</param>
             <returns>The response received from the server.</returns>
        </member>
        <member name="M:Google.Cloud.Iam.V1.IAMPolicy.IAMPolicyClient.TestIamPermissionsAsync(Google.Cloud.Iam.V1.TestIamPermissionsRequest,Grpc.Core.Metadata,System.Nullable{System.DateTime},System.Threading.CancellationToken)">
             <summary>
             Returns permissions that a caller has on the specified resource.
             If the resource does not exist, this will return an empty set of
             permissions, not a `NOT_FOUND` error.
            
             Note: This operation is designed to be used for building permission-aware
             UIs and command-line tools, not for authorization checking. This operation
             may "fail open" without warning.
             </summary>
             <param name="request">The request to send to the server.</param>
             <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
             <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
             <param name="cancellationToken">An optional token for canceling the call.</param>
             <returns>The call object.</returns>
        </member>
        <member name="M:Google.Cloud.Iam.V1.IAMPolicy.IAMPolicyClient.TestIamPermissionsAsync(Google.Cloud.Iam.V1.TestIamPermissionsRequest,Grpc.Core.CallOptions)">
             <summary>
             Returns permissions that a caller has on the specified resource.
             If the resource does not exist, this will return an empty set of
             permissions, not a `NOT_FOUND` error.
            
             Note: This operation is designed to be used for building permission-aware
             UIs and command-line tools, not for authorization checking. This operation
             may "fail open" without warning.
             </summary>
             <param name="request">The request to send to the server.</param>
             <param name="options">The options for the call.</param>
             <returns>The call object.</returns>
        </member>
        <member name="M:Google.Cloud.Iam.V1.IAMPolicy.IAMPolicyClient.NewInstance(Grpc.Core.ClientBase.ClientBaseConfiguration)">
            <summary>Creates a new instance of client from given <c>ClientBaseConfiguration</c>.</summary>
        </member>
        <member name="M:Google.Cloud.Iam.V1.IAMPolicy.BindService(Google.Cloud.Iam.V1.IAMPolicy.IAMPolicyBase)">
            <summary>Creates service definition that can be registered with a server</summary>
            <param name="serviceImpl">An object implementing the server-side handling logic.</param>
        </member>
        <member name="M:Google.Cloud.Iam.V1.IAMPolicy.BindService(Grpc.Core.ServiceBinderBase,Google.Cloud.Iam.V1.IAMPolicy.IAMPolicyBase)">
            <summary>Register service method with a service binder with or without implementation. Useful when customizing the  service binding logic.
            Note: this method is part of an experimental API that can change or be removed without any prior notice.</summary>
            <param name="serviceBinder">Service methods will be bound by calling <c>AddMethod</c> on this object.</param>
            <param name="serviceImpl">An object implementing the server-side handling logic.</param>
        </member>
        <member name="T:Google.Cloud.Iam.V1.Logging.AuditDataReflection">
            <summary>Holder for reflection information generated from google/iam/v1/logging/audit_data.proto</summary>
        </member>
        <member name="P:Google.Cloud.Iam.V1.Logging.AuditDataReflection.Descriptor">
            <summary>File descriptor for google/iam/v1/logging/audit_data.proto</summary>
        </member>
        <member name="T:Google.Cloud.Iam.V1.Logging.AuditData">
            <summary>
            Audit log information specific to Cloud IAM. This message is serialized
            as an `Any` type in the `ServiceData` message of an
            `AuditLog` message.
            </summary>
        </member>
        <member name="F:Google.Cloud.Iam.V1.Logging.AuditData.PolicyDeltaFieldNumber">
            <summary>Field number for the "policy_delta" field.</summary>
        </member>
        <member name="P:Google.Cloud.Iam.V1.Logging.AuditData.PolicyDelta">
            <summary>
            Policy delta between the original policy and the newly set policy.
            </summary>
        </member>
        <member name="T:Google.Cloud.Iam.V1.OptionsReflection">
            <summary>Holder for reflection information generated from google/iam/v1/options.proto</summary>
        </member>
        <member name="P:Google.Cloud.Iam.V1.OptionsReflection.Descriptor">
            <summary>File descriptor for google/iam/v1/options.proto</summary>
        </member>
        <member name="T:Google.Cloud.Iam.V1.GetPolicyOptions">
            <summary>
            Encapsulates settings provided to GetIamPolicy.
            </summary>
        </member>
        <member name="F:Google.Cloud.Iam.V1.GetPolicyOptions.RequestedPolicyVersionFieldNumber">
            <summary>Field number for the "requested_policy_version" field.</summary>
        </member>
        <member name="P:Google.Cloud.Iam.V1.GetPolicyOptions.RequestedPolicyVersion">
             <summary>
             Optional. The maximum policy version that will be used to format the
             policy.
            
             Valid values are 0, 1, and 3. Requests specifying an invalid value will be
             rejected.
            
             Requests for policies with any conditional role bindings must specify
             version 3. Policies with no conditional role bindings may specify any valid
             value or leave the field unset.
            
             The policy in the response might use the policy version that you specified,
             or it might use a lower policy version. For example, if you specify version
             3, but the policy has no conditional role bindings, the response uses
             version 1.
            
             To learn which resources support conditions in their IAM policies, see the
             [IAM
             documentation](https://cloud.google.com/iam/help/conditions/resource-policies).
             </summary>
        </member>
        <member name="T:Google.Cloud.Iam.V1.PackageApiMetadata">
            <summary>Static class to provide common access to package-wide API metadata.</summary>
        </member>
        <member name="P:Google.Cloud.Iam.V1.PackageApiMetadata.ApiMetadata">
            <summary>The <see cref="T:Google.Api.Gax.Grpc.ApiMetadata"/> for services in this package.</summary>
        </member>
        <member name="T:Google.Cloud.Iam.V1.PolicyReflection">
            <summary>Holder for reflection information generated from google/iam/v1/policy.proto</summary>
        </member>
        <member name="P:Google.Cloud.Iam.V1.PolicyReflection.Descriptor">
            <summary>File descriptor for google/iam/v1/policy.proto</summary>
        </member>
        <member name="T:Google.Cloud.Iam.V1.Policy">
             <summary>
             An Identity and Access Management (IAM) policy, which specifies access
             controls for Google Cloud resources.
            
             A `Policy` is a collection of `bindings`. A `binding` binds one or more
             `members`, or principals, to a single `role`. Principals can be user
             accounts, service accounts, Google groups, and domains (such as G Suite). A
             `role` is a named list of permissions; each `role` can be an IAM predefined
             role or a user-created custom role.
            
             For some types of Google Cloud resources, a `binding` can also specify a
             `condition`, which is a logical expression that allows access to a resource
             only if the expression evaluates to `true`. A condition can add constraints
             based on attributes of the request, the resource, or both. To learn which
             resources support conditions in their IAM policies, see the
             [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).
            
             **JSON example:**
            
                 {
                   "bindings": [
                     {
                       "role": "roles/resourcemanager.organizationAdmin",
                       "members": [
                         "user:<EMAIL>",
                         "group:<EMAIL>",
                         "domain:google.com",
                         "serviceAccount:<EMAIL>"
                       ]
                     },
                     {
                       "role": "roles/resourcemanager.organizationViewer",
                       "members": [
                         "user:<EMAIL>"
                       ],
                       "condition": {
                         "title": "expirable access",
                         "description": "Does not grant access after Sep 2020",
                         "expression": "request.time &lt; timestamp('2020-10-01T00:00:00.000Z')",
                       }
                     }
                   ],
                   "etag": "BwWWja0YfJA=",
                   "version": 3
                 }
            
             **YAML example:**
            
                 bindings:
                 - members:
                   - user:<EMAIL>
                   - group:<EMAIL>
                   - domain:google.com
                   - serviceAccount:<EMAIL>
                   role: roles/resourcemanager.organizationAdmin
                 - members:
                   - user:<EMAIL>
                   role: roles/resourcemanager.organizationViewer
                   condition:
                     title: expirable access
                     description: Does not grant access after Sep 2020
                     expression: request.time &lt; timestamp('2020-10-01T00:00:00.000Z')
                 etag: BwWWja0YfJA=
                 version: 3
            
             For a description of IAM and its features, see the
             [IAM documentation](https://cloud.google.com/iam/docs/).
             </summary>
        </member>
        <member name="F:Google.Cloud.Iam.V1.Policy.VersionFieldNumber">
            <summary>Field number for the "version" field.</summary>
        </member>
        <member name="P:Google.Cloud.Iam.V1.Policy.Version">
             <summary>
             Specifies the format of the policy.
            
             Valid values are `0`, `1`, and `3`. Requests that specify an invalid value
             are rejected.
            
             Any operation that affects conditional role bindings must specify version
             `3`. This requirement applies to the following operations:
            
             * Getting a policy that includes a conditional role binding
             * Adding a conditional role binding to a policy
             * Changing a conditional role binding in a policy
             * Removing any role binding, with or without a condition, from a policy
               that includes conditions
            
             **Important:** If you use IAM Conditions, you must include the `etag` field
             whenever you call `setIamPolicy`. If you omit this field, then IAM allows
             you to overwrite a version `3` policy with a version `1` policy, and all of
             the conditions in the version `3` policy are lost.
            
             If a policy does not include any conditions, operations on that policy may
             specify any valid version or leave the field unset.
            
             To learn which resources support conditions in their IAM policies, see the
             [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).
             </summary>
        </member>
        <member name="F:Google.Cloud.Iam.V1.Policy.BindingsFieldNumber">
            <summary>Field number for the "bindings" field.</summary>
        </member>
        <member name="P:Google.Cloud.Iam.V1.Policy.Bindings">
             <summary>
             Associates a list of `members`, or principals, with a `role`. Optionally,
             may specify a `condition` that determines how and when the `bindings` are
             applied. Each of the `bindings` must contain at least one principal.
            
             The `bindings` in a `Policy` can refer to up to 1,500 principals; up to 250
             of these principals can be Google groups. Each occurrence of a principal
             counts towards these limits. For example, if the `bindings` grant 50
             different roles to `user:<EMAIL>`, and not to any other
             principal, then you can add another 1,450 principals to the `bindings` in
             the `Policy`.
             </summary>
        </member>
        <member name="F:Google.Cloud.Iam.V1.Policy.AuditConfigsFieldNumber">
            <summary>Field number for the "audit_configs" field.</summary>
        </member>
        <member name="P:Google.Cloud.Iam.V1.Policy.AuditConfigs">
            <summary>
            Specifies cloud audit logging configuration for this policy.
            </summary>
        </member>
        <member name="F:Google.Cloud.Iam.V1.Policy.EtagFieldNumber">
            <summary>Field number for the "etag" field.</summary>
        </member>
        <member name="P:Google.Cloud.Iam.V1.Policy.Etag">
             <summary>
             `etag` is used for optimistic concurrency control as a way to help
             prevent simultaneous updates of a policy from overwriting each other.
             It is strongly suggested that systems make use of the `etag` in the
             read-modify-write cycle to perform policy updates in order to avoid race
             conditions: An `etag` is returned in the response to `getIamPolicy`, and
             systems are expected to put that etag in the request to `setIamPolicy` to
             ensure that their change will be applied to the same version of the policy.
            
             **Important:** If you use IAM Conditions, you must include the `etag` field
             whenever you call `setIamPolicy`. If you omit this field, then IAM allows
             you to overwrite a version `3` policy with a version `1` policy, and all of
             the conditions in the version `3` policy are lost.
             </summary>
        </member>
        <member name="M:Google.Cloud.Iam.V1.Policy.AddRoleMember(System.String,System.String)">
            <summary>
            Adds the specified member to the specified role. If the role does
            not already exist, it is created.
            This method will fail with an <see cref="T:System.InvalidOperationException"/>
            if it is called on a Policy with a <see cref="P:Google.Cloud.Iam.V1.Policy.Version"/> greater than 1,
            or if any of the bindings contain conditions,
            as that indicates a more complicated policy than this method is prepared
            to handle. Changes to such policies must be made manually.
            </summary>
            <param name="role">The role to add the member to. Must not be null or empty.</param>
            <param name="member">The member to add to the role. Must not be null or empty.</param>
            <returns><c>true</c> if the policy was changed; <c>false</c> if the member already existed in the role.</returns>
            <exception cref="T:System.InvalidOperationException">The <see cref="P:Google.Cloud.Iam.V1.Policy.Version"/> is greater than 1.</exception>
        </member>
        <member name="M:Google.Cloud.Iam.V1.Policy.RemoveRoleMember(System.String,System.String)">
            <summary>
            Removes the specified member to the specified role, if they belong to it. If the role becomes empty after
            removing the member, it is removed from the policy.
            This method will fail with an <see cref="T:System.InvalidOperationException"/>
            if it is called on a Policy with a <see cref="P:Google.Cloud.Iam.V1.Policy.Version"/> greater than 1,
            or if any of the bindings contain conditions,
            as that indicates a more complicated policy than this method is prepared
            to handle. Changes to such policies must be made manually.
            </summary>
            <param name="role">The role to remove the member from. Must not be null or empty.</param>
            <param name="member">The member to remove from the role. Must not be null or empty.</param>
            <returns><c>true</c> if the policy was changed; <c>false</c> if the member didn't exist in the role
            or the role didn't exist.</returns>
            <exception cref="T:System.InvalidOperationException">The <see cref="P:Google.Cloud.Iam.V1.Policy.Version"/> is greater than 1.</exception>
        </member>
        <member name="T:Google.Cloud.Iam.V1.Binding">
            <summary>
            Associates `members`, or principals, with a `role`.
            </summary>
        </member>
        <member name="F:Google.Cloud.Iam.V1.Binding.RoleFieldNumber">
            <summary>Field number for the "role" field.</summary>
        </member>
        <member name="P:Google.Cloud.Iam.V1.Binding.Role">
            <summary>
            Role that is assigned to the list of `members`, or principals.
            For example, `roles/viewer`, `roles/editor`, or `roles/owner`.
            </summary>
        </member>
        <member name="F:Google.Cloud.Iam.V1.Binding.MembersFieldNumber">
            <summary>Field number for the "members" field.</summary>
        </member>
        <member name="P:Google.Cloud.Iam.V1.Binding.Members">
             <summary>
             Specifies the principals requesting access for a Cloud Platform resource.
             `members` can have the following values:
            
             * `allUsers`: A special identifier that represents anyone who is
                on the internet; with or without a Google account.
            
             * `allAuthenticatedUsers`: A special identifier that represents anyone
                who is authenticated with a Google account or a service account.
            
             * `user:{emailid}`: An email address that represents a specific Google
                account. For example, `<EMAIL>` .
            
             * `serviceAccount:{emailid}`: An email address that represents a service
                account. For example, `<EMAIL>`.
            
             * `group:{emailid}`: An email address that represents a Google group.
                For example, `<EMAIL>`.
            
             * `deleted:user:{emailid}?uid={uniqueid}`: An email address (plus unique
                identifier) representing a user that has been recently deleted. For
                example, `<EMAIL>?uid=123456789012345678901`. If the user is
                recovered, this value reverts to `user:{emailid}` and the recovered user
                retains the role in the binding.
            
             * `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address (plus
                unique identifier) representing a service account that has been recently
                deleted. For example,
                `<EMAIL>?uid=123456789012345678901`.
                If the service account is undeleted, this value reverts to
                `serviceAccount:{emailid}` and the undeleted service account retains the
                role in the binding.
            
             * `deleted:group:{emailid}?uid={uniqueid}`: An email address (plus unique
                identifier) representing a Google group that has been recently
                deleted. For example, `<EMAIL>?uid=123456789012345678901`. If
                the group is recovered, this value reverts to `group:{emailid}` and the
                recovered group retains the role in the binding.
            
             * `domain:{domain}`: The G Suite domain (primary) that represents all the
                users of that domain. For example, `google.com` or `example.com`.
             </summary>
        </member>
        <member name="F:Google.Cloud.Iam.V1.Binding.ConditionFieldNumber">
            <summary>Field number for the "condition" field.</summary>
        </member>
        <member name="P:Google.Cloud.Iam.V1.Binding.Condition">
             <summary>
             The condition that is associated with this binding.
            
             If the condition evaluates to `true`, then this binding applies to the
             current request.
            
             If the condition evaluates to `false`, then this binding does not apply to
             the current request. However, a different role binding might grant the same
             role to one or more of the principals in this binding.
            
             To learn which resources support conditions in their IAM policies, see the
             [IAM
             documentation](https://cloud.google.com/iam/help/conditions/resource-policies).
             </summary>
        </member>
        <member name="T:Google.Cloud.Iam.V1.AuditConfig">
             <summary>
             Specifies the audit configuration for a service.
             The configuration determines which permission types are logged, and what
             identities, if any, are exempted from logging.
             An AuditConfig must have one or more AuditLogConfigs.
            
             If there are AuditConfigs for both `allServices` and a specific service,
             the union of the two AuditConfigs is used for that service: the log_types
             specified in each AuditConfig are enabled, and the exempted_members in each
             AuditLogConfig are exempted.
            
             Example Policy with multiple AuditConfigs:
            
                 {
                   "audit_configs": [
                     {
                       "service": "allServices",
                       "audit_log_configs": [
                         {
                           "log_type": "DATA_READ",
                           "exempted_members": [
                             "user:<EMAIL>"
                           ]
                         },
                         {
                           "log_type": "DATA_WRITE"
                         },
                         {
                           "log_type": "ADMIN_READ"
                         }
                       ]
                     },
                     {
                       "service": "sampleservice.googleapis.com",
                       "audit_log_configs": [
                         {
                           "log_type": "DATA_READ"
                         },
                         {
                           "log_type": "DATA_WRITE",
                           "exempted_members": [
                             "user:<EMAIL>"
                           ]
                         }
                       ]
                     }
                   ]
                 }
            
             For sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ
             logging. It <NAME_EMAIL> from DATA_READ logging, and
             <EMAIL> from DATA_WRITE logging.
             </summary>
        </member>
        <member name="F:Google.Cloud.Iam.V1.AuditConfig.ServiceFieldNumber">
            <summary>Field number for the "service" field.</summary>
        </member>
        <member name="P:Google.Cloud.Iam.V1.AuditConfig.Service">
            <summary>
            Specifies a service that will be enabled for audit logging.
            For example, `storage.googleapis.com`, `cloudsql.googleapis.com`.
            `allServices` is a special value that covers all services.
            </summary>
        </member>
        <member name="F:Google.Cloud.Iam.V1.AuditConfig.AuditLogConfigsFieldNumber">
            <summary>Field number for the "audit_log_configs" field.</summary>
        </member>
        <member name="P:Google.Cloud.Iam.V1.AuditConfig.AuditLogConfigs">
            <summary>
            The configuration for logging of each type of permission.
            </summary>
        </member>
        <member name="T:Google.Cloud.Iam.V1.AuditLogConfig">
             <summary>
             Provides the configuration for logging a type of permissions.
             Example:
            
                 {
                   "audit_log_configs": [
                     {
                       "log_type": "DATA_READ",
                       "exempted_members": [
                         "user:<EMAIL>"
                       ]
                     },
                     {
                       "log_type": "DATA_WRITE"
                     }
                   ]
                 }
            
             This enables 'DATA_READ' and 'DATA_WRITE' logging, while exempting
             <EMAIL> from DATA_READ logging.
             </summary>
        </member>
        <member name="F:Google.Cloud.Iam.V1.AuditLogConfig.LogTypeFieldNumber">
            <summary>Field number for the "log_type" field.</summary>
        </member>
        <member name="P:Google.Cloud.Iam.V1.AuditLogConfig.LogType">
            <summary>
            The log type that this config enables.
            </summary>
        </member>
        <member name="F:Google.Cloud.Iam.V1.AuditLogConfig.ExemptedMembersFieldNumber">
            <summary>Field number for the "exempted_members" field.</summary>
        </member>
        <member name="P:Google.Cloud.Iam.V1.AuditLogConfig.ExemptedMembers">
            <summary>
            Specifies the identities that do not cause logging for this type of
            permission.
            Follows the same format of [Binding.members][google.iam.v1.Binding.members].
            </summary>
        </member>
        <member name="T:Google.Cloud.Iam.V1.AuditLogConfig.Types">
            <summary>Container for nested types declared in the AuditLogConfig message type.</summary>
        </member>
        <member name="T:Google.Cloud.Iam.V1.AuditLogConfig.Types.LogType">
            <summary>
            The list of valid permission types for which logging can be configured.
            Admin writes are always logged, and are not configurable.
            </summary>
        </member>
        <member name="F:Google.Cloud.Iam.V1.AuditLogConfig.Types.LogType.Unspecified">
            <summary>
            Default case. Should never be this.
            </summary>
        </member>
        <member name="F:Google.Cloud.Iam.V1.AuditLogConfig.Types.LogType.AdminRead">
            <summary>
            Admin reads. Example: CloudIAM getIamPolicy
            </summary>
        </member>
        <member name="F:Google.Cloud.Iam.V1.AuditLogConfig.Types.LogType.DataWrite">
            <summary>
            Data writes. Example: CloudSQL Users create
            </summary>
        </member>
        <member name="F:Google.Cloud.Iam.V1.AuditLogConfig.Types.LogType.DataRead">
            <summary>
            Data reads. Example: CloudSQL Users list
            </summary>
        </member>
        <member name="T:Google.Cloud.Iam.V1.PolicyDelta">
            <summary>
            The difference delta between two policies.
            </summary>
        </member>
        <member name="F:Google.Cloud.Iam.V1.PolicyDelta.BindingDeltasFieldNumber">
            <summary>Field number for the "binding_deltas" field.</summary>
        </member>
        <member name="P:Google.Cloud.Iam.V1.PolicyDelta.BindingDeltas">
            <summary>
            The delta for Bindings between two policies.
            </summary>
        </member>
        <member name="F:Google.Cloud.Iam.V1.PolicyDelta.AuditConfigDeltasFieldNumber">
            <summary>Field number for the "audit_config_deltas" field.</summary>
        </member>
        <member name="P:Google.Cloud.Iam.V1.PolicyDelta.AuditConfigDeltas">
            <summary>
            The delta for AuditConfigs between two policies.
            </summary>
        </member>
        <member name="T:Google.Cloud.Iam.V1.BindingDelta">
            <summary>
            One delta entry for Binding. Each individual change (only one member in each
            entry) to a binding will be a separate entry.
            </summary>
        </member>
        <member name="F:Google.Cloud.Iam.V1.BindingDelta.ActionFieldNumber">
            <summary>Field number for the "action" field.</summary>
        </member>
        <member name="P:Google.Cloud.Iam.V1.BindingDelta.Action">
            <summary>
            The action that was performed on a Binding.
            Required
            </summary>
        </member>
        <member name="F:Google.Cloud.Iam.V1.BindingDelta.RoleFieldNumber">
            <summary>Field number for the "role" field.</summary>
        </member>
        <member name="P:Google.Cloud.Iam.V1.BindingDelta.Role">
            <summary>
            Role that is assigned to `members`.
            For example, `roles/viewer`, `roles/editor`, or `roles/owner`.
            Required
            </summary>
        </member>
        <member name="F:Google.Cloud.Iam.V1.BindingDelta.MemberFieldNumber">
            <summary>Field number for the "member" field.</summary>
        </member>
        <member name="P:Google.Cloud.Iam.V1.BindingDelta.Member">
            <summary>
            A single identity requesting access for a Cloud Platform resource.
            Follows the same format of Binding.members.
            Required
            </summary>
        </member>
        <member name="F:Google.Cloud.Iam.V1.BindingDelta.ConditionFieldNumber">
            <summary>Field number for the "condition" field.</summary>
        </member>
        <member name="P:Google.Cloud.Iam.V1.BindingDelta.Condition">
            <summary>
            The condition that is associated with this binding.
            </summary>
        </member>
        <member name="T:Google.Cloud.Iam.V1.BindingDelta.Types">
            <summary>Container for nested types declared in the BindingDelta message type.</summary>
        </member>
        <member name="T:Google.Cloud.Iam.V1.BindingDelta.Types.Action">
            <summary>
            The type of action performed on a Binding in a policy.
            </summary>
        </member>
        <member name="F:Google.Cloud.Iam.V1.BindingDelta.Types.Action.Unspecified">
            <summary>
            Unspecified.
            </summary>
        </member>
        <member name="F:Google.Cloud.Iam.V1.BindingDelta.Types.Action.Add">
            <summary>
            Addition of a Binding.
            </summary>
        </member>
        <member name="F:Google.Cloud.Iam.V1.BindingDelta.Types.Action.Remove">
            <summary>
            Removal of a Binding.
            </summary>
        </member>
        <member name="T:Google.Cloud.Iam.V1.AuditConfigDelta">
            <summary>
            One delta entry for AuditConfig. Each individual change (only one
            exempted_member in each entry) to a AuditConfig will be a separate entry.
            </summary>
        </member>
        <member name="F:Google.Cloud.Iam.V1.AuditConfigDelta.ActionFieldNumber">
            <summary>Field number for the "action" field.</summary>
        </member>
        <member name="P:Google.Cloud.Iam.V1.AuditConfigDelta.Action">
            <summary>
            The action that was performed on an audit configuration in a policy.
            Required
            </summary>
        </member>
        <member name="F:Google.Cloud.Iam.V1.AuditConfigDelta.ServiceFieldNumber">
            <summary>Field number for the "service" field.</summary>
        </member>
        <member name="P:Google.Cloud.Iam.V1.AuditConfigDelta.Service">
            <summary>
            Specifies a service that was configured for Cloud Audit Logging.
            For example, `storage.googleapis.com`, `cloudsql.googleapis.com`.
            `allServices` is a special value that covers all services.
            Required
            </summary>
        </member>
        <member name="F:Google.Cloud.Iam.V1.AuditConfigDelta.ExemptedMemberFieldNumber">
            <summary>Field number for the "exempted_member" field.</summary>
        </member>
        <member name="P:Google.Cloud.Iam.V1.AuditConfigDelta.ExemptedMember">
            <summary>
            A single identity that is exempted from "data access" audit
            logging for the `service` specified above.
            Follows the same format of Binding.members.
            </summary>
        </member>
        <member name="F:Google.Cloud.Iam.V1.AuditConfigDelta.LogTypeFieldNumber">
            <summary>Field number for the "log_type" field.</summary>
        </member>
        <member name="P:Google.Cloud.Iam.V1.AuditConfigDelta.LogType">
            <summary>
            Specifies the log_type that was be enabled. ADMIN_ACTIVITY is always
            enabled, and cannot be configured.
            Required
            </summary>
        </member>
        <member name="T:Google.Cloud.Iam.V1.AuditConfigDelta.Types">
            <summary>Container for nested types declared in the AuditConfigDelta message type.</summary>
        </member>
        <member name="T:Google.Cloud.Iam.V1.AuditConfigDelta.Types.Action">
            <summary>
            The type of action performed on an audit configuration in a policy.
            </summary>
        </member>
        <member name="F:Google.Cloud.Iam.V1.AuditConfigDelta.Types.Action.Unspecified">
            <summary>
            Unspecified.
            </summary>
        </member>
        <member name="F:Google.Cloud.Iam.V1.AuditConfigDelta.Types.Action.Add">
            <summary>
            Addition of an audit configuration.
            </summary>
        </member>
        <member name="F:Google.Cloud.Iam.V1.AuditConfigDelta.Types.Action.Remove">
            <summary>
            Removal of an audit configuration.
            </summary>
        </member>
        <member name="T:Microsoft.Extensions.DependencyInjection.ServiceCollectionExtensions">
            <summary>Static class to provide extension methods to configure API clients.</summary>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.ServiceCollectionExtensions.AddIAMPolicyClient(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.Action{Google.Cloud.Iam.V1.IAMPolicyClientBuilder})">
            <summary>Adds a singleton <see cref="T:Google.Cloud.Iam.V1.IAMPolicyClient"/> to <paramref name="services"/>.</summary>
            <param name="services">
            The service collection to add the client to. The services are used to configure the client when requested.
            </param>
            <param name="action">
            An optional action to invoke on the client builder. This is invoked before services from
            <paramref name="services"/> are used.
            </param>
        </member>
    </members>
</doc>
