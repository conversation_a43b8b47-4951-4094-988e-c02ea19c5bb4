﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2013/05/nuspec.xsd">
  <metadata>
    <id>Google.Api.CommonProtos</id>
    <version>2.5.0</version>
    <title>Google API Common Protos</title>
    <authors>Google LLC</authors>
    <requireLicenseAcceptance>false</requireLicenseAcceptance>
    <license type="file">LICENSE</license>
    <licenseUrl>https://aka.ms/deprecateLicenseUrl</licenseUrl>
    <icon>NuGetIcon.png</icon>
    <projectUrl>https://github.com/googleapis/gax-dotnet</projectUrl>
    <iconUrl>https://www.gstatic.com/images/branding/product/1x/google_developers_64dp.png</iconUrl>
    <description>Common Protocol Buffer messages for Google APIs</description>
    <copyright>Copyright 2020 Google LLC</copyright>
    <tags>Google</tags>
    <repository type="git" url="https://github.com/googleapis/gax-dotnet" commit="42fbad57912bb2a4aca03e07862822e173be045d" />
    <dependencies>
      <group targetFramework=".NETFramework4.6.1">
        <dependency id="Google.Protobuf" version="[3.18.0, 4.0.0)" exclude="Build,Analyzers" />
      </group>
      <group targetFramework=".NETStandard2.0">
        <dependency id="Google.Protobuf" version="[3.18.0, 4.0.0)" exclude="Build,Analyzers" />
      </group>
    </dependencies>
  </metadata>
</package>