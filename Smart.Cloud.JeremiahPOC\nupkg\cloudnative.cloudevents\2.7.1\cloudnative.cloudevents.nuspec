﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2013/05/nuspec.xsd">
  <metadata>
    <id>CloudNative.CloudEvents</id>
    <version>2.7.1</version>
    <authors>CloudNative.CloudEvents</authors>
    <license type="expression">Apache-2.0</license>
    <licenseUrl>https://licenses.nuget.org/Apache-2.0</licenseUrl>
    <icon>nuget-icon.png</icon>
    <projectUrl>https://cloudevents.io/</projectUrl>
    <description>CNCF CloudEvents SDK</description>
    <copyright>Copyright Cloud Native Foundation</copyright>
    <tags>cloudnative cloudevents events</tags>
    <repository type="git" url="https://github.com/cloudevents/sdk-csharp" commit="79999423345c75e4457abc10bd853e5899cfb1e7" />
    <dependencies>
      <group targetFramework=".NETStandard2.0">
        <dependency id="System.Memory" version="4.5.5" exclude="Build,Analyzers" />
      </group>
      <group targetFramework=".NETStandard2.1">
        <dependency id="System.Memory" version="4.5.5" exclude="Build,Analyzers" />
      </group>
    </dependencies>
  </metadata>
</package>