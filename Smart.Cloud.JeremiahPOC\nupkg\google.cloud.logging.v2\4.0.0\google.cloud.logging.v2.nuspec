﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2013/05/nuspec.xsd">
  <metadata>
    <id>Google.Cloud.Logging.V2</id>
    <version>4.0.0</version>
    <authors>Google LLC</authors>
    <license type="file">LICENSE</license>
    <licenseUrl>https://aka.ms/deprecateLicenseUrl</licenseUrl>
    <icon>NuGetIcon.png</icon>
    <projectUrl>https://github.com/googleapis/google-cloud-dotnet</projectUrl>
    <iconUrl>https://cloud.google.com/images/gcp-icon-64x64.png</iconUrl>
    <description>Recommended Google client library to access the Google Cloud Logging API, which writes log entries and manages your logs, log sinks, and logs-based metrics.</description>
    <copyright>Copyright 2022 Google LLC</copyright>
    <tags>Logging Stackdriver Google Cloud</tags>
    <repository type="git" url="https://github.com/googleapis/google-cloud-dotnet" commit="b1c12e7a07e37367fb70800f604eef5826e145b5" />
    <dependencies>
      <group targetFramework=".NETFramework4.6.2">
        <dependency id="Google.Cloud.Logging.Type" version="4.0.0" exclude="Build,Analyzers" />
        <dependency id="Google.LongRunning" version="3.0.0" exclude="Build,Analyzers" />
        <dependency id="Google.Api.Gax.Grpc" version="[4.0.0, 5.0.0)" exclude="Build,Analyzers" />
        <dependency id="Grpc.Core" version="[2.46.3, 3.0.0)" include="All" />
      </group>
      <group targetFramework=".NETStandard2.1">
        <dependency id="Google.Cloud.Logging.Type" version="4.0.0" exclude="Build,Analyzers" />
        <dependency id="Google.LongRunning" version="3.0.0" exclude="Build,Analyzers" />
        <dependency id="Google.Api.Gax.Grpc" version="[4.0.0, 5.0.0)" exclude="Build,Analyzers" />
      </group>
    </dependencies>
  </metadata>
</package>