﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2013/05/nuspec.xsd">
  <metadata>
    <id>Google.Cloud.Functions.Hosting</id>
    <version>2.2.1</version>
    <title>Google Cloud Functions Framework Hosting</title>
    <authors>Google LLC</authors>
    <license type="expression">Apache-2.0</license>
    <licenseUrl>https://licenses.nuget.org/Apache-2.0</licenseUrl>
    <icon>NuGetIcon.png</icon>
    <projectUrl>https://github.com/GoogleCloudPlatform/functions-framework-dotnet</projectUrl>
    <iconUrl>https://cloud.google.com/images/gcp-icon-64x64.png</iconUrl>
    <description>The Google Cloud Functions Framework Hosting package makes it easy to run a web server to execute functions.</description>
    <copyright>Copyright 2020 Google LLC</copyright>
    <tags>google cloud functions</tags>
    <repository type="git" url="https://github.com/GoogleCloudPlatform/functions-framework-dotnet" commit="27bbd9d536f099b8eeb3ab00a110263fbfc02643" />
    <dependencies>
      <group targetFramework="net6.0">
        <dependency id="Google.Cloud.Functions.Framework" version="2.2.1" exclude="Build,Analyzers" />
        <dependency id="CloudNative.CloudEvents.SystemTextJson" version="2.7.1" exclude="Build,Analyzers" />
      </group>
    </dependencies>
    <frameworkReferences>
      <group targetFramework="net6.0">
        <frameworkReference name="Microsoft.AspNetCore.App" />
      </group>
    </frameworkReferences>
  </metadata>
</package>