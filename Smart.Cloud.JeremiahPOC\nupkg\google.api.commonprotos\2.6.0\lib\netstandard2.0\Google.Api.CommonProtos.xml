<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Google.Api.CommonProtos</name>
    </assembly>
    <members>
        <member name="T:Google.Api.AnnotationsReflection">
            <summary>Holder for reflection information generated from google/api/annotations.proto</summary>
        </member>
        <member name="P:Google.Api.AnnotationsReflection.Descriptor">
            <summary>File descriptor for google/api/annotations.proto</summary>
        </member>
        <member name="T:Google.Api.AnnotationsExtensions">
            <summary>Holder for extension identifiers generated from the top level of google/api/annotations.proto</summary>
        </member>
        <member name="F:Google.Api.AnnotationsExtensions.Http">
            <summary>
            See `HttpRule`.
            </summary>
        </member>
        <member name="T:Google.Api.AuthReflection">
            <summary>Holder for reflection information generated from google/api/auth.proto</summary>
        </member>
        <member name="P:Google.Api.AuthReflection.Descriptor">
            <summary>File descriptor for google/api/auth.proto</summary>
        </member>
        <member name="T:Google.Api.Authentication">
             <summary>
             `Authentication` defines the authentication configuration for API methods
             provided by an API service.
            
             Example:
            
                 name: calendar.googleapis.com
                 authentication:
                   providers:
                   - id: google_calendar_auth
                     jwks_uri: https://www.googleapis.com/oauth2/v1/certs
                     issuer: https://securetoken.google.com
                   rules:
                   - selector: "*"
                     requirements:
                       provider_id: google_calendar_auth
                   - selector: google.calendar.Delegate
                     oauth:
                       canonical_scopes: https://www.googleapis.com/auth/calendar.read
             </summary>
        </member>
        <member name="F:Google.Api.Authentication.RulesFieldNumber">
            <summary>Field number for the "rules" field.</summary>
        </member>
        <member name="P:Google.Api.Authentication.Rules">
             <summary>
             A list of authentication rules that apply to individual API methods.
            
             **NOTE:** All service configuration rules follow "last one wins" order.
             </summary>
        </member>
        <member name="F:Google.Api.Authentication.ProvidersFieldNumber">
            <summary>Field number for the "providers" field.</summary>
        </member>
        <member name="P:Google.Api.Authentication.Providers">
            <summary>
            Defines a set of authentication providers that a service supports.
            </summary>
        </member>
        <member name="T:Google.Api.AuthenticationRule">
             <summary>
             Authentication rules for the service.
            
             By default, if a method has any authentication requirements, every request
             must include a valid credential matching one of the requirements.
             It's an error to include more than one kind of credential in a single
             request.
            
             If a method doesn't have any auth requirements, request credentials will be
             ignored.
             </summary>
        </member>
        <member name="F:Google.Api.AuthenticationRule.SelectorFieldNumber">
            <summary>Field number for the "selector" field.</summary>
        </member>
        <member name="P:Google.Api.AuthenticationRule.Selector">
             <summary>
             Selects the methods to which this rule applies.
            
             Refer to [selector][google.api.DocumentationRule.selector] for syntax details.
             </summary>
        </member>
        <member name="F:Google.Api.AuthenticationRule.OauthFieldNumber">
            <summary>Field number for the "oauth" field.</summary>
        </member>
        <member name="P:Google.Api.AuthenticationRule.Oauth">
            <summary>
            The requirements for OAuth credentials.
            </summary>
        </member>
        <member name="F:Google.Api.AuthenticationRule.AllowWithoutCredentialFieldNumber">
            <summary>Field number for the "allow_without_credential" field.</summary>
        </member>
        <member name="P:Google.Api.AuthenticationRule.AllowWithoutCredential">
            <summary>
            If true, the service accepts API keys without any other credential.
            This flag only applies to HTTP and gRPC requests.
            </summary>
        </member>
        <member name="F:Google.Api.AuthenticationRule.RequirementsFieldNumber">
            <summary>Field number for the "requirements" field.</summary>
        </member>
        <member name="P:Google.Api.AuthenticationRule.Requirements">
            <summary>
            Requirements for additional authentication providers.
            </summary>
        </member>
        <member name="T:Google.Api.JwtLocation">
            <summary>
            Specifies a location to extract JWT from an API request.
            </summary>
        </member>
        <member name="F:Google.Api.JwtLocation.HeaderFieldNumber">
            <summary>Field number for the "header" field.</summary>
        </member>
        <member name="P:Google.Api.JwtLocation.Header">
            <summary>
            Specifies HTTP header name to extract JWT token.
            </summary>
        </member>
        <member name="F:Google.Api.JwtLocation.QueryFieldNumber">
            <summary>Field number for the "query" field.</summary>
        </member>
        <member name="P:Google.Api.JwtLocation.Query">
            <summary>
            Specifies URL query parameter name to extract JWT token.
            </summary>
        </member>
        <member name="F:Google.Api.JwtLocation.ValuePrefixFieldNumber">
            <summary>Field number for the "value_prefix" field.</summary>
        </member>
        <member name="P:Google.Api.JwtLocation.ValuePrefix">
             <summary>
             The value prefix. The value format is "value_prefix{token}"
             Only applies to "in" header type. Must be empty for "in" query type.
             If not empty, the header value has to match (case sensitive) this prefix.
             If not matched, JWT will not be extracted. If matched, JWT will be
             extracted after the prefix is removed.
            
             For example, for "Authorization: Bearer {JWT}",
             value_prefix="Bearer " with a space at the end.
             </summary>
        </member>
        <member name="T:Google.Api.JwtLocation.InOneofCase">
            <summary>Enum of possible cases for the "in" oneof.</summary>
        </member>
        <member name="T:Google.Api.AuthProvider">
            <summary>
            Configuration for an authentication provider, including support for
            [JSON Web Token
            (JWT)](https://tools.ietf.org/html/draft-ietf-oauth-json-web-token-32).
            </summary>
        </member>
        <member name="F:Google.Api.AuthProvider.IdFieldNumber">
            <summary>Field number for the "id" field.</summary>
        </member>
        <member name="P:Google.Api.AuthProvider.Id">
             <summary>
             The unique identifier of the auth provider. It will be referred to by
             `AuthRequirement.provider_id`.
            
             Example: "bookstore_auth".
             </summary>
        </member>
        <member name="F:Google.Api.AuthProvider.IssuerFieldNumber">
            <summary>Field number for the "issuer" field.</summary>
        </member>
        <member name="P:Google.Api.AuthProvider.Issuer">
             <summary>
             Identifies the principal that issued the JWT. See
             https://tools.ietf.org/html/draft-ietf-oauth-json-web-token-32#section-4.1.1
             Usually a URL or an email address.
            
             Example: https://securetoken.google.com
             Example: <EMAIL>
             </summary>
        </member>
        <member name="F:Google.Api.AuthProvider.JwksUriFieldNumber">
            <summary>Field number for the "jwks_uri" field.</summary>
        </member>
        <member name="P:Google.Api.AuthProvider.JwksUri">
             <summary>
             URL of the provider's public key set to validate signature of the JWT. See
             [OpenID
             Discovery](https://openid.net/specs/openid-connect-discovery-1_0.html#ProviderMetadata).
             Optional if the key set document:
              - can be retrieved from
                [OpenID
                Discovery](https://openid.net/specs/openid-connect-discovery-1_0.html)
                of the issuer.
              - can be inferred from the email domain of the issuer (e.g. a Google
              service account).
            
             Example: https://www.googleapis.com/oauth2/v1/certs
             </summary>
        </member>
        <member name="F:Google.Api.AuthProvider.AudiencesFieldNumber">
            <summary>Field number for the "audiences" field.</summary>
        </member>
        <member name="P:Google.Api.AuthProvider.Audiences">
             <summary>
             The list of JWT
             [audiences](https://tools.ietf.org/html/draft-ietf-oauth-json-web-token-32#section-4.1.3).
             that are allowed to access. A JWT containing any of these audiences will
             be accepted. When this setting is absent, JWTs with audiences:
               - "https://[service.name]/[google.protobuf.Api.name]"
               - "https://[service.name]/"
             will be accepted.
             For example, if no audiences are in the setting, LibraryService API will
             accept JWTs with the following audiences:
               -
               https://library-example.googleapis.com/google.example.library.v1.LibraryService
               - https://library-example.googleapis.com/
            
             Example:
            
                 audiences: bookstore_android.apps.googleusercontent.com,
                            bookstore_web.apps.googleusercontent.com
             </summary>
        </member>
        <member name="F:Google.Api.AuthProvider.AuthorizationUrlFieldNumber">
            <summary>Field number for the "authorization_url" field.</summary>
        </member>
        <member name="P:Google.Api.AuthProvider.AuthorizationUrl">
            <summary>
            Redirect URL if JWT token is required but not present or is expired.
            Implement authorizationUrl of securityDefinitions in OpenAPI spec.
            </summary>
        </member>
        <member name="F:Google.Api.AuthProvider.JwtLocationsFieldNumber">
            <summary>Field number for the "jwt_locations" field.</summary>
        </member>
        <member name="P:Google.Api.AuthProvider.JwtLocations">
             <summary>
             Defines the locations to extract the JWT.
            
             JWT locations can be either from HTTP headers or URL query parameters.
             The rule is that the first match wins. The checking order is: checking
             all headers first, then URL query parameters.
            
             If not specified,  default to use following 3 locations:
                1) Authorization: Bearer
                2) x-goog-iap-jwt-assertion
                3) access_token query parameter
            
             Default locations can be specified as followings:
                jwt_locations:
                - header: Authorization
                  value_prefix: "Bearer "
                - header: x-goog-iap-jwt-assertion
                - query: access_token
             </summary>
        </member>
        <member name="T:Google.Api.OAuthRequirements">
             <summary>
             OAuth scopes are a way to define data and permissions on data. For example,
             there are scopes defined for "Read-only access to Google Calendar" and
             "Access to Cloud Platform". Users can consent to a scope for an application,
             giving it permission to access that data on their behalf.
            
             OAuth scope specifications should be fairly coarse grained; a user will need
             to see and understand the text description of what your scope means.
            
             In most cases: use one or at most two OAuth scopes for an entire family of
             products. If your product has multiple APIs, you should probably be sharing
             the OAuth scope across all of those APIs.
            
             When you need finer grained OAuth consent screens: talk with your product
             management about how developers will use them in practice.
            
             Please note that even though each of the canonical scopes is enough for a
             request to be accepted and passed to the backend, a request can still fail
             due to the backend requiring additional scopes or permissions.
             </summary>
        </member>
        <member name="F:Google.Api.OAuthRequirements.CanonicalScopesFieldNumber">
            <summary>Field number for the "canonical_scopes" field.</summary>
        </member>
        <member name="P:Google.Api.OAuthRequirements.CanonicalScopes">
             <summary>
             The list of publicly documented OAuth scopes that are allowed access. An
             OAuth token containing any of these scopes will be accepted.
            
             Example:
            
                  canonical_scopes: https://www.googleapis.com/auth/calendar,
                                    https://www.googleapis.com/auth/calendar.read
             </summary>
        </member>
        <member name="T:Google.Api.AuthRequirement">
            <summary>
            User-defined authentication requirements, including support for
            [JSON Web Token
            (JWT)](https://tools.ietf.org/html/draft-ietf-oauth-json-web-token-32).
            </summary>
        </member>
        <member name="F:Google.Api.AuthRequirement.ProviderIdFieldNumber">
            <summary>Field number for the "provider_id" field.</summary>
        </member>
        <member name="P:Google.Api.AuthRequirement.ProviderId">
             <summary>
             [id][google.api.AuthProvider.id] from authentication provider.
            
             Example:
            
                 provider_id: bookstore_auth
             </summary>
        </member>
        <member name="F:Google.Api.AuthRequirement.AudiencesFieldNumber">
            <summary>Field number for the "audiences" field.</summary>
        </member>
        <member name="P:Google.Api.AuthRequirement.Audiences">
             <summary>
             NOTE: This will be deprecated soon, once AuthProvider.audiences is
             implemented and accepted in all the runtime components.
            
             The list of JWT
             [audiences](https://tools.ietf.org/html/draft-ietf-oauth-json-web-token-32#section-4.1.3).
             that are allowed to access. A JWT containing any of these audiences will
             be accepted. When this setting is absent, only JWTs with audience
             "https://[Service_name][google.api.Service.name]/[API_name][google.protobuf.Api.name]"
             will be accepted. For example, if no audiences are in the setting,
             LibraryService API will only accept JWTs with the following audience
             "https://library-example.googleapis.com/google.example.library.v1.LibraryService".
            
             Example:
            
                 audiences: bookstore_android.apps.googleusercontent.com,
                            bookstore_web.apps.googleusercontent.com
             </summary>
        </member>
        <member name="T:Google.Api.BackendReflection">
            <summary>Holder for reflection information generated from google/api/backend.proto</summary>
        </member>
        <member name="P:Google.Api.BackendReflection.Descriptor">
            <summary>File descriptor for google/api/backend.proto</summary>
        </member>
        <member name="T:Google.Api.Backend">
            <summary>
            `Backend` defines the backend configuration for a service.
            </summary>
        </member>
        <member name="F:Google.Api.Backend.RulesFieldNumber">
            <summary>Field number for the "rules" field.</summary>
        </member>
        <member name="P:Google.Api.Backend.Rules">
             <summary>
             A list of API backend rules that apply to individual API methods.
            
             **NOTE:** All service configuration rules follow "last one wins" order.
             </summary>
        </member>
        <member name="T:Google.Api.BackendRule">
            <summary>
            A backend rule provides configuration for an individual API element.
            </summary>
        </member>
        <member name="F:Google.Api.BackendRule.SelectorFieldNumber">
            <summary>Field number for the "selector" field.</summary>
        </member>
        <member name="P:Google.Api.BackendRule.Selector">
             <summary>
             Selects the methods to which this rule applies.
            
             Refer to [selector][google.api.DocumentationRule.selector] for syntax details.
             </summary>
        </member>
        <member name="F:Google.Api.BackendRule.AddressFieldNumber">
            <summary>Field number for the "address" field.</summary>
        </member>
        <member name="P:Google.Api.BackendRule.Address">
             <summary>
             The address of the API backend.
            
             The scheme is used to determine the backend protocol and security.
             The following schemes are accepted:
            
                SCHEME        PROTOCOL    SECURITY
                http://       HTTP        None
                https://      HTTP        TLS
                grpc://       gRPC        None
                grpcs://      gRPC        TLS
            
             It is recommended to explicitly include a scheme. Leaving out the scheme
             may cause constrasting behaviors across platforms.
            
             If the port is unspecified, the default is:
             - 80 for schemes without TLS
             - 443 for schemes with TLS
            
             For HTTP backends, use [protocol][google.api.BackendRule.protocol]
             to specify the protocol version.
             </summary>
        </member>
        <member name="F:Google.Api.BackendRule.DeadlineFieldNumber">
            <summary>Field number for the "deadline" field.</summary>
        </member>
        <member name="P:Google.Api.BackendRule.Deadline">
            <summary>
            The number of seconds to wait for a response from a request. The default
            varies based on the request protocol and deployment environment.
            </summary>
        </member>
        <member name="F:Google.Api.BackendRule.MinDeadlineFieldNumber">
            <summary>Field number for the "min_deadline" field.</summary>
        </member>
        <member name="P:Google.Api.BackendRule.MinDeadline">
            <summary>
            Minimum deadline in seconds needed for this method. Calls having deadline
            value lower than this will be rejected.
            </summary>
        </member>
        <member name="F:Google.Api.BackendRule.OperationDeadlineFieldNumber">
            <summary>Field number for the "operation_deadline" field.</summary>
        </member>
        <member name="P:Google.Api.BackendRule.OperationDeadline">
            <summary>
            The number of seconds to wait for the completion of a long running
            operation. The default is no deadline.
            </summary>
        </member>
        <member name="F:Google.Api.BackendRule.PathTranslationFieldNumber">
            <summary>Field number for the "path_translation" field.</summary>
        </member>
        <member name="F:Google.Api.BackendRule.JwtAudienceFieldNumber">
            <summary>Field number for the "jwt_audience" field.</summary>
        </member>
        <member name="P:Google.Api.BackendRule.JwtAudience">
            <summary>
            The JWT audience is used when generating a JWT ID token for the backend.
            This ID token will be added in the HTTP "authorization" header, and sent
            to the backend.
            </summary>
        </member>
        <member name="F:Google.Api.BackendRule.DisableAuthFieldNumber">
            <summary>Field number for the "disable_auth" field.</summary>
        </member>
        <member name="P:Google.Api.BackendRule.DisableAuth">
            <summary>
            When disable_auth is true, a JWT ID token won't be generated and the
            original "Authorization" HTTP header will be preserved. If the header is
            used to carry the original token and is expected by the backend, this
            field must be set to true to preserve the header.
            </summary>
        </member>
        <member name="F:Google.Api.BackendRule.ProtocolFieldNumber">
            <summary>Field number for the "protocol" field.</summary>
        </member>
        <member name="P:Google.Api.BackendRule.Protocol">
             <summary>
             The protocol used for sending a request to the backend.
             The supported values are "http/1.1" and "h2".
            
             The default value is inferred from the scheme in the
             [address][google.api.BackendRule.address] field:
            
                SCHEME        PROTOCOL
                http://       http/1.1
                https://      http/1.1
                grpc://       h2
                grpcs://      h2
            
             For secure HTTP backends (https://) that support HTTP/2, set this field
             to "h2" for improved performance.
            
             Configuring this field to non-default values is only supported for secure
             HTTP backends. This field will be ignored for all other backends.
            
             See
             https://www.iana.org/assignments/tls-extensiontype-values/tls-extensiontype-values.xhtml#alpn-protocol-ids
             for more details on the supported values.
             </summary>
        </member>
        <member name="T:Google.Api.BackendRule.AuthenticationOneofCase">
            <summary>Enum of possible cases for the "authentication" oneof.</summary>
        </member>
        <member name="T:Google.Api.BackendRule.Types">
            <summary>Container for nested types declared in the BackendRule message type.</summary>
        </member>
        <member name="T:Google.Api.BackendRule.Types.PathTranslation">
             <summary>
             Path Translation specifies how to combine the backend address with the
             request path in order to produce the appropriate forwarding URL for the
             request.
            
             Path Translation is applicable only to HTTP-based backends. Backends which
             do not accept requests over HTTP/HTTPS should leave `path_translation`
             unspecified.
             </summary>
        </member>
        <member name="F:Google.Api.BackendRule.Types.PathTranslation.ConstantAddress">
             <summary>
             Use the backend address as-is, with no modification to the path. If the
             URL pattern contains variables, the variable names and values will be
             appended to the query string. If a query string parameter and a URL
             pattern variable have the same name, this may result in duplicate keys in
             the query string.
            
             # Examples
            
             Given the following operation config:
            
                 Method path:        /api/company/{cid}/user/{uid}
                 Backend address:    https://example.cloudfunctions.net/getUser
            
             Requests to the following request paths will call the backend at the
             translated path:
            
                 Request path: /api/company/widgetworks/user/johndoe
                 Translated:
                 https://example.cloudfunctions.net/getUser?cid=widgetworks&amp;uid=johndoe
            
                 Request path: /api/company/widgetworks/user/johndoe?timezone=EST
                 Translated:
                 https://example.cloudfunctions.net/getUser?timezone=EST&amp;cid=widgetworks&amp;uid=johndoe
             </summary>
        </member>
        <member name="F:Google.Api.BackendRule.Types.PathTranslation.AppendPathToAddress">
             <summary>
             The request path will be appended to the backend address.
            
             # Examples
            
             Given the following operation config:
            
                 Method path:        /api/company/{cid}/user/{uid}
                 Backend address:    https://example.appspot.com
            
             Requests to the following request paths will call the backend at the
             translated path:
            
                 Request path: /api/company/widgetworks/user/johndoe
                 Translated:
                 https://example.appspot.com/api/company/widgetworks/user/johndoe
            
                 Request path: /api/company/widgetworks/user/johndoe?timezone=EST
                 Translated:
                 https://example.appspot.com/api/company/widgetworks/user/johndoe?timezone=EST
             </summary>
        </member>
        <member name="T:Google.Api.BillingReflection">
            <summary>Holder for reflection information generated from google/api/billing.proto</summary>
        </member>
        <member name="P:Google.Api.BillingReflection.Descriptor">
            <summary>File descriptor for google/api/billing.proto</summary>
        </member>
        <member name="T:Google.Api.Billing">
             <summary>
             Billing related configuration of the service.
            
             The following example shows how to configure monitored resources and metrics
             for billing, `consumer_destinations` is the only supported destination and
             the monitored resources need at least one label key
             `cloud.googleapis.com/location` to indicate the location of the billing
             usage, using different monitored resources between monitoring and billing is
             recommended so they can be evolved independently:
            
                 monitored_resources:
                 - type: library.googleapis.com/billing_branch
                   labels:
                   - key: cloud.googleapis.com/location
                     description: |
                       Predefined label to support billing location restriction.
                   - key: city
                     description: |
                       Custom label to define the city where the library branch is located
                       in.
                   - key: name
                     description: Custom label to define the name of the library branch.
                 metrics:
                 - name: library.googleapis.com/book/borrowed_count
                   metric_kind: DELTA
                   value_type: INT64
                   unit: "1"
                 billing:
                   consumer_destinations:
                   - monitored_resource: library.googleapis.com/billing_branch
                     metrics:
                     - library.googleapis.com/book/borrowed_count
             </summary>
        </member>
        <member name="F:Google.Api.Billing.ConsumerDestinationsFieldNumber">
            <summary>Field number for the "consumer_destinations" field.</summary>
        </member>
        <member name="P:Google.Api.Billing.ConsumerDestinations">
            <summary>
            Billing configurations for sending metrics to the consumer project.
            There can be multiple consumer destinations per service, each one must have
            a different monitored resource type. A metric can be used in at most
            one consumer destination.
            </summary>
        </member>
        <member name="T:Google.Api.Billing.Types">
            <summary>Container for nested types declared in the Billing message type.</summary>
        </member>
        <member name="T:Google.Api.Billing.Types.BillingDestination">
            <summary>
            Configuration of a specific billing destination (Currently only support
            bill against consumer project).
            </summary>
        </member>
        <member name="F:Google.Api.Billing.Types.BillingDestination.MonitoredResourceFieldNumber">
            <summary>Field number for the "monitored_resource" field.</summary>
        </member>
        <member name="P:Google.Api.Billing.Types.BillingDestination.MonitoredResource">
            <summary>
            The monitored resource type. The type must be defined in
            [Service.monitored_resources][google.api.Service.monitored_resources] section.
            </summary>
        </member>
        <member name="F:Google.Api.Billing.Types.BillingDestination.MetricsFieldNumber">
            <summary>Field number for the "metrics" field.</summary>
        </member>
        <member name="P:Google.Api.Billing.Types.BillingDestination.Metrics">
            <summary>
            Names of the metrics to report to this billing destination.
            Each name must be defined in [Service.metrics][google.api.Service.metrics] section.
            </summary>
        </member>
        <member name="T:Google.Api.ClientReflection">
            <summary>Holder for reflection information generated from google/api/client.proto</summary>
        </member>
        <member name="P:Google.Api.ClientReflection.Descriptor">
            <summary>File descriptor for google/api/client.proto</summary>
        </member>
        <member name="T:Google.Api.ClientExtensions">
            <summary>Holder for extension identifiers generated from the top level of google/api/client.proto</summary>
        </member>
        <member name="F:Google.Api.ClientExtensions.MethodSignature">
             <summary>
             A definition of a client library method signature.
            
             In client libraries, each proto RPC corresponds to one or more methods
             which the end user is able to call, and calls the underlying RPC.
             Normally, this method receives a single argument (a struct or instance
             corresponding to the RPC request object). Defining this field will
             add one or more overloads providing flattened or simpler method signatures
             in some languages.
            
             The fields on the method signature are provided as a comma-separated
             string.
            
             For example, the proto RPC and annotation:
            
               rpc CreateSubscription(CreateSubscriptionRequest)
                   returns (Subscription) {
                 option (google.api.method_signature) = "name,topic";
               }
            
             Would add the following Java overload (in addition to the method accepting
             the request object):
            
               public final Subscription createSubscription(String name, String topic)
            
             The following backwards-compatibility guidelines apply:
            
               * Adding this annotation to an unannotated method is backwards
                 compatible.
               * Adding this annotation to a method which already has existing
                 method signature annotations is backwards compatible if and only if
                 the new method signature annotation is last in the sequence.
               * Modifying or removing an existing method signature annotation is
                 a breaking change.
               * Re-ordering existing method signature annotations is a breaking
                 change.
             </summary>
        </member>
        <member name="F:Google.Api.ClientExtensions.DefaultHost">
             <summary>
             The hostname for this service.
             This should be specified with no prefix or protocol.
            
             Example:
            
               service Foo {
                 option (google.api.default_host) = "foo.googleapi.com";
                 ...
               }
             </summary>
        </member>
        <member name="F:Google.Api.ClientExtensions.OauthScopes">
             <summary>
             OAuth scopes needed for the client.
            
             Example:
            
               service Foo {
                 option (google.api.oauth_scopes) = \
                   "https://www.googleapis.com/auth/cloud-platform";
                 ...
               }
            
             If there is more than one scope, use a comma-separated string:
            
             Example:
            
               service Foo {
                 option (google.api.oauth_scopes) = \
                   "https://www.googleapis.com/auth/cloud-platform,"
                   "https://www.googleapis.com/auth/monitoring";
                 ...
               }
             </summary>
        </member>
        <member name="T:Google.Api.ConfigChangeReflection">
            <summary>Holder for reflection information generated from google/api/config_change.proto</summary>
        </member>
        <member name="P:Google.Api.ConfigChangeReflection.Descriptor">
            <summary>File descriptor for google/api/config_change.proto</summary>
        </member>
        <member name="T:Google.Api.ChangeType">
            <summary>
            Classifies set of possible modifications to an object in the service
            configuration.
            </summary>
        </member>
        <member name="F:Google.Api.ChangeType.Unspecified">
            <summary>
            No value was provided.
            </summary>
        </member>
        <member name="F:Google.Api.ChangeType.Added">
            <summary>
            The changed object exists in the 'new' service configuration, but not
            in the 'old' service configuration.
            </summary>
        </member>
        <member name="F:Google.Api.ChangeType.Removed">
            <summary>
            The changed object exists in the 'old' service configuration, but not
            in the 'new' service configuration.
            </summary>
        </member>
        <member name="F:Google.Api.ChangeType.Modified">
            <summary>
            The changed object exists in both service configurations, but its value
            is different.
            </summary>
        </member>
        <member name="T:Google.Api.ConfigChange">
             <summary>
             Output generated from semantically comparing two versions of a service
             configuration.
            
             Includes detailed information about a field that have changed with
             applicable advice about potential consequences for the change, such as
             backwards-incompatibility.
             </summary>
        </member>
        <member name="F:Google.Api.ConfigChange.ElementFieldNumber">
            <summary>Field number for the "element" field.</summary>
        </member>
        <member name="P:Google.Api.ConfigChange.Element">
            <summary>
            Object hierarchy path to the change, with levels separated by a '.'
            character. For repeated fields, an applicable unique identifier field is
            used for the index (usually selector, name, or id). For maps, the term
            'key' is used. If the field has no unique identifier, the numeric index
            is used.
            Examples:
            - visibility.rules[selector=="google.LibraryService.ListBooks"].restriction
            - quota.metric_rules[selector=="google"].metric_costs[key=="reads"].value
            - logging.producer_destinations[0]
            </summary>
        </member>
        <member name="F:Google.Api.ConfigChange.OldValueFieldNumber">
            <summary>Field number for the "old_value" field.</summary>
        </member>
        <member name="P:Google.Api.ConfigChange.OldValue">
            <summary>
            Value of the changed object in the old Service configuration,
            in JSON format. This field will not be populated if ChangeType == ADDED.
            </summary>
        </member>
        <member name="F:Google.Api.ConfigChange.NewValueFieldNumber">
            <summary>Field number for the "new_value" field.</summary>
        </member>
        <member name="P:Google.Api.ConfigChange.NewValue">
            <summary>
            Value of the changed object in the new Service configuration,
            in JSON format. This field will not be populated if ChangeType == REMOVED.
            </summary>
        </member>
        <member name="F:Google.Api.ConfigChange.ChangeTypeFieldNumber">
            <summary>Field number for the "change_type" field.</summary>
        </member>
        <member name="P:Google.Api.ConfigChange.ChangeType">
            <summary>
            The type for this change, either ADDED, REMOVED, or MODIFIED.
            </summary>
        </member>
        <member name="F:Google.Api.ConfigChange.AdvicesFieldNumber">
            <summary>Field number for the "advices" field.</summary>
        </member>
        <member name="P:Google.Api.ConfigChange.Advices">
            <summary>
            Collection of advice provided for this change, useful for determining the
            possible impact of this change.
            </summary>
        </member>
        <member name="T:Google.Api.Advice">
            <summary>
            Generated advice about this change, used for providing more
            information about how a change will affect the existing service.
            </summary>
        </member>
        <member name="F:Google.Api.Advice.DescriptionFieldNumber">
            <summary>Field number for the "description" field.</summary>
        </member>
        <member name="P:Google.Api.Advice.Description">
            <summary>
            Useful description for why this advice was applied and what actions should
            be taken to mitigate any implied risks.
            </summary>
        </member>
        <member name="T:Google.Api.ConsumerReflection">
            <summary>Holder for reflection information generated from google/api/consumer.proto</summary>
        </member>
        <member name="P:Google.Api.ConsumerReflection.Descriptor">
            <summary>File descriptor for google/api/consumer.proto</summary>
        </member>
        <member name="T:Google.Api.ProjectProperties">
             <summary>
             A descriptor for defining project properties for a service. One service may
             have many consumer projects, and the service may want to behave differently
             depending on some properties on the project. For example, a project may be
             associated with a school, or a business, or a government agency, a business
             type property on the project may affect how a service responds to the client.
             This descriptor defines which properties are allowed to be set on a project.
            
             Example:
            
                project_properties:
                  properties:
                  - name: NO_WATERMARK
                    type: BOOL
                    description: Allows usage of the API without watermarks.
                  - name: EXTENDED_TILE_CACHE_PERIOD
                    type: INT64
             </summary>
        </member>
        <member name="F:Google.Api.ProjectProperties.PropertiesFieldNumber">
            <summary>Field number for the "properties" field.</summary>
        </member>
        <member name="P:Google.Api.ProjectProperties.Properties">
            <summary>
            List of per consumer project-specific properties.
            </summary>
        </member>
        <member name="T:Google.Api.Property">
             <summary>
             Defines project properties.
            
             API services can define properties that can be assigned to consumer projects
             so that backends can perform response customization without having to make
             additional calls or maintain additional storage. For example, Maps API
             defines properties that controls map tile cache period, or whether to embed a
             watermark in a result.
            
             These values can be set via API producer console. Only API providers can
             define and set these properties.
             </summary>
        </member>
        <member name="F:Google.Api.Property.NameFieldNumber">
            <summary>Field number for the "name" field.</summary>
        </member>
        <member name="P:Google.Api.Property.Name">
            <summary>
            The name of the property (a.k.a key).
            </summary>
        </member>
        <member name="F:Google.Api.Property.TypeFieldNumber">
            <summary>Field number for the "type" field.</summary>
        </member>
        <member name="P:Google.Api.Property.Type">
            <summary>
            The type of this property.
            </summary>
        </member>
        <member name="F:Google.Api.Property.DescriptionFieldNumber">
            <summary>Field number for the "description" field.</summary>
        </member>
        <member name="P:Google.Api.Property.Description">
            <summary>
            The description of the property
            </summary>
        </member>
        <member name="T:Google.Api.Property.Types">
            <summary>Container for nested types declared in the Property message type.</summary>
        </member>
        <member name="T:Google.Api.Property.Types.PropertyType">
            <summary>
            Supported data type of the property values
            </summary>
        </member>
        <member name="F:Google.Api.Property.Types.PropertyType.Unspecified">
            <summary>
            The type is unspecified, and will result in an error.
            </summary>
        </member>
        <member name="F:Google.Api.Property.Types.PropertyType.Int64">
            <summary>
            The type is `int64`.
            </summary>
        </member>
        <member name="F:Google.Api.Property.Types.PropertyType.Bool">
            <summary>
            The type is `bool`.
            </summary>
        </member>
        <member name="F:Google.Api.Property.Types.PropertyType.String">
            <summary>
            The type is `string`.
            </summary>
        </member>
        <member name="F:Google.Api.Property.Types.PropertyType.Double">
            <summary>
            The type is 'double'.
            </summary>
        </member>
        <member name="T:Google.Api.ContextReflection">
            <summary>Holder for reflection information generated from google/api/context.proto</summary>
        </member>
        <member name="P:Google.Api.ContextReflection.Descriptor">
            <summary>File descriptor for google/api/context.proto</summary>
        </member>
        <member name="T:Google.Api.Context">
             <summary>
             `Context` defines which contexts an API requests.
            
             Example:
            
                 context:
                   rules:
                   - selector: "*"
                     requested:
                     - google.rpc.context.ProjectContext
                     - google.rpc.context.OriginContext
            
             The above specifies that all methods in the API request
             `google.rpc.context.ProjectContext` and
             `google.rpc.context.OriginContext`.
            
             Available context types are defined in package
             `google.rpc.context`.
            
             This also provides mechanism to allowlist any protobuf message extension that
             can be sent in grpc metadata using “x-goog-ext-&lt;extension_id>-bin” and
             “x-goog-ext-&lt;extension_id>-jspb” format. For example, list any service
             specific protobuf types that can appear in grpc metadata as follows in your
             yaml file:
            
             Example:
            
                 context:
                   rules:
                    - selector: "google.example.library.v1.LibraryService.CreateBook"
                      allowed_request_extensions:
                      - google.foo.v1.NewExtension
                      allowed_response_extensions:
                      - google.foo.v1.NewExtension
            
             You can also specify extension ID instead of fully qualified extension name
             here.
             </summary>
        </member>
        <member name="F:Google.Api.Context.RulesFieldNumber">
            <summary>Field number for the "rules" field.</summary>
        </member>
        <member name="P:Google.Api.Context.Rules">
             <summary>
             A list of RPC context rules that apply to individual API methods.
            
             **NOTE:** All service configuration rules follow "last one wins" order.
             </summary>
        </member>
        <member name="T:Google.Api.ContextRule">
            <summary>
            A context rule provides information about the context for an individual API
            element.
            </summary>
        </member>
        <member name="F:Google.Api.ContextRule.SelectorFieldNumber">
            <summary>Field number for the "selector" field.</summary>
        </member>
        <member name="P:Google.Api.ContextRule.Selector">
             <summary>
             Selects the methods to which this rule applies.
            
             Refer to [selector][google.api.DocumentationRule.selector] for syntax details.
             </summary>
        </member>
        <member name="F:Google.Api.ContextRule.RequestedFieldNumber">
            <summary>Field number for the "requested" field.</summary>
        </member>
        <member name="P:Google.Api.ContextRule.Requested">
            <summary>
            A list of full type names of requested contexts.
            </summary>
        </member>
        <member name="F:Google.Api.ContextRule.ProvidedFieldNumber">
            <summary>Field number for the "provided" field.</summary>
        </member>
        <member name="P:Google.Api.ContextRule.Provided">
            <summary>
            A list of full type names of provided contexts.
            </summary>
        </member>
        <member name="F:Google.Api.ContextRule.AllowedRequestExtensionsFieldNumber">
            <summary>Field number for the "allowed_request_extensions" field.</summary>
        </member>
        <member name="P:Google.Api.ContextRule.AllowedRequestExtensions">
            <summary>
            A list of full type names or extension IDs of extensions allowed in grpc
            side channel from client to backend.
            </summary>
        </member>
        <member name="F:Google.Api.ContextRule.AllowedResponseExtensionsFieldNumber">
            <summary>Field number for the "allowed_response_extensions" field.</summary>
        </member>
        <member name="P:Google.Api.ContextRule.AllowedResponseExtensions">
            <summary>
            A list of full type names or extension IDs of extensions allowed in grpc
            side channel from backend to client.
            </summary>
        </member>
        <member name="T:Google.Api.ControlReflection">
            <summary>Holder for reflection information generated from google/api/control.proto</summary>
        </member>
        <member name="P:Google.Api.ControlReflection.Descriptor">
            <summary>File descriptor for google/api/control.proto</summary>
        </member>
        <member name="T:Google.Api.Control">
            <summary>
            Selects and configures the service controller used by the service.  The
            service controller handles features like abuse, quota, billing, logging,
            monitoring, etc.
            </summary>
        </member>
        <member name="F:Google.Api.Control.EnvironmentFieldNumber">
            <summary>Field number for the "environment" field.</summary>
        </member>
        <member name="P:Google.Api.Control.Environment">
            <summary>
            The service control environment to use. If empty, no control plane
            feature (like quota and billing) will be enabled.
            </summary>
        </member>
        <member name="T:Google.Api.DistributionReflection">
            <summary>Holder for reflection information generated from google/api/distribution.proto</summary>
        </member>
        <member name="P:Google.Api.DistributionReflection.Descriptor">
            <summary>File descriptor for google/api/distribution.proto</summary>
        </member>
        <member name="T:Google.Api.Distribution">
             <summary>
             `Distribution` contains summary statistics for a population of values. It
             optionally contains a histogram representing the distribution of those values
             across a set of buckets.
            
             The summary statistics are the count, mean, sum of the squared deviation from
             the mean, the minimum, and the maximum of the set of population of values.
             The histogram is based on a sequence of buckets and gives a count of values
             that fall into each bucket. The boundaries of the buckets are given either
             explicitly or by formulas for buckets of fixed or exponentially increasing
             widths.
            
             Although it is not forbidden, it is generally a bad idea to include
             non-finite values (infinities or NaNs) in the population of values, as this
             will render the `mean` and `sum_of_squared_deviation` fields meaningless.
             </summary>
        </member>
        <member name="F:Google.Api.Distribution.CountFieldNumber">
            <summary>Field number for the "count" field.</summary>
        </member>
        <member name="P:Google.Api.Distribution.Count">
            <summary>
            The number of values in the population. Must be non-negative. This value
            must equal the sum of the values in `bucket_counts` if a histogram is
            provided.
            </summary>
        </member>
        <member name="F:Google.Api.Distribution.MeanFieldNumber">
            <summary>Field number for the "mean" field.</summary>
        </member>
        <member name="P:Google.Api.Distribution.Mean">
            <summary>
            The arithmetic mean of the values in the population. If `count` is zero
            then this field must be zero.
            </summary>
        </member>
        <member name="F:Google.Api.Distribution.SumOfSquaredDeviationFieldNumber">
            <summary>Field number for the "sum_of_squared_deviation" field.</summary>
        </member>
        <member name="P:Google.Api.Distribution.SumOfSquaredDeviation">
             <summary>
             The sum of squared deviations from the mean of the values in the
             population. For values x_i this is:
            
                 Sum[i=1..n]((x_i - mean)^2)
            
             Knuth, "The Art of Computer Programming", Vol. 2, page 232, 3rd edition
             describes Welford's method for accumulating this sum in one pass.
            
             If `count` is zero then this field must be zero.
             </summary>
        </member>
        <member name="F:Google.Api.Distribution.RangeFieldNumber">
            <summary>Field number for the "range" field.</summary>
        </member>
        <member name="P:Google.Api.Distribution.Range">
            <summary>
            If specified, contains the range of the population values. The field
            must not be present if the `count` is zero.
            </summary>
        </member>
        <member name="F:Google.Api.Distribution.BucketOptionsFieldNumber">
            <summary>Field number for the "bucket_options" field.</summary>
        </member>
        <member name="P:Google.Api.Distribution.BucketOptions">
            <summary>
            Defines the histogram bucket boundaries. If the distribution does not
            contain a histogram, then omit this field.
            </summary>
        </member>
        <member name="F:Google.Api.Distribution.BucketCountsFieldNumber">
            <summary>Field number for the "bucket_counts" field.</summary>
        </member>
        <member name="P:Google.Api.Distribution.BucketCounts">
             <summary>
             The number of values in each bucket of the histogram, as described in
             `bucket_options`. If the distribution does not have a histogram, then omit
             this field. If there is a histogram, then the sum of the values in
             `bucket_counts` must equal the value in the `count` field of the
             distribution.
            
             If present, `bucket_counts` should contain N values, where N is the number
             of buckets specified in `bucket_options`. If you supply fewer than N
             values, the remaining values are assumed to be 0.
            
             The order of the values in `bucket_counts` follows the bucket numbering
             schemes described for the three bucket types. The first value must be the
             count for the underflow bucket (number 0). The next N-2 values are the
             counts for the finite buckets (number 1 through N-2). The N'th value in
             `bucket_counts` is the count for the overflow bucket (number N-1).
             </summary>
        </member>
        <member name="F:Google.Api.Distribution.ExemplarsFieldNumber">
            <summary>Field number for the "exemplars" field.</summary>
        </member>
        <member name="P:Google.Api.Distribution.Exemplars">
            <summary>
            Must be in increasing order of `value` field.
            </summary>
        </member>
        <member name="T:Google.Api.Distribution.Types">
            <summary>Container for nested types declared in the Distribution message type.</summary>
        </member>
        <member name="T:Google.Api.Distribution.Types.Range">
            <summary>
            The range of the population values.
            </summary>
        </member>
        <member name="F:Google.Api.Distribution.Types.Range.MinFieldNumber">
            <summary>Field number for the "min" field.</summary>
        </member>
        <member name="P:Google.Api.Distribution.Types.Range.Min">
            <summary>
            The minimum of the population values.
            </summary>
        </member>
        <member name="F:Google.Api.Distribution.Types.Range.MaxFieldNumber">
            <summary>Field number for the "max" field.</summary>
        </member>
        <member name="P:Google.Api.Distribution.Types.Range.Max">
            <summary>
            The maximum of the population values.
            </summary>
        </member>
        <member name="T:Google.Api.Distribution.Types.BucketOptions">
             <summary>
             `BucketOptions` describes the bucket boundaries used to create a histogram
             for the distribution. The buckets can be in a linear sequence, an
             exponential sequence, or each bucket can be specified explicitly.
             `BucketOptions` does not include the number of values in each bucket.
            
             A bucket has an inclusive lower bound and exclusive upper bound for the
             values that are counted for that bucket. The upper bound of a bucket must
             be strictly greater than the lower bound. The sequence of N buckets for a
             distribution consists of an underflow bucket (number 0), zero or more
             finite buckets (number 1 through N - 2) and an overflow bucket (number N -
             1). The buckets are contiguous: the lower bound of bucket i (i > 0) is the
             same as the upper bound of bucket i - 1. The buckets span the whole range
             of finite values: lower bound of the underflow bucket is -infinity and the
             upper bound of the overflow bucket is +infinity. The finite buckets are
             so-called because both bounds are finite.
             </summary>
        </member>
        <member name="F:Google.Api.Distribution.Types.BucketOptions.LinearBucketsFieldNumber">
            <summary>Field number for the "linear_buckets" field.</summary>
        </member>
        <member name="P:Google.Api.Distribution.Types.BucketOptions.LinearBuckets">
            <summary>
            The linear bucket.
            </summary>
        </member>
        <member name="F:Google.Api.Distribution.Types.BucketOptions.ExponentialBucketsFieldNumber">
            <summary>Field number for the "exponential_buckets" field.</summary>
        </member>
        <member name="P:Google.Api.Distribution.Types.BucketOptions.ExponentialBuckets">
            <summary>
            The exponential buckets.
            </summary>
        </member>
        <member name="F:Google.Api.Distribution.Types.BucketOptions.ExplicitBucketsFieldNumber">
            <summary>Field number for the "explicit_buckets" field.</summary>
        </member>
        <member name="P:Google.Api.Distribution.Types.BucketOptions.ExplicitBuckets">
            <summary>
            The explicit buckets.
            </summary>
        </member>
        <member name="T:Google.Api.Distribution.Types.BucketOptions.OptionsOneofCase">
            <summary>Enum of possible cases for the "options" oneof.</summary>
        </member>
        <member name="T:Google.Api.Distribution.Types.BucketOptions.Types">
            <summary>Container for nested types declared in the BucketOptions message type.</summary>
        </member>
        <member name="T:Google.Api.Distribution.Types.BucketOptions.Types.Linear">
             <summary>
             Specifies a linear sequence of buckets that all have the same width
             (except overflow and underflow). Each bucket represents a constant
             absolute uncertainty on the specific value in the bucket.
            
             There are `num_finite_buckets + 2` (= N) buckets. Bucket `i` has the
             following boundaries:
            
                Upper bound (0 &lt;= i &lt; N-1):     offset + (width * i).
                Lower bound (1 &lt;= i &lt; N):       offset + (width * (i - 1)).
             </summary>
        </member>
        <member name="F:Google.Api.Distribution.Types.BucketOptions.Types.Linear.NumFiniteBucketsFieldNumber">
            <summary>Field number for the "num_finite_buckets" field.</summary>
        </member>
        <member name="P:Google.Api.Distribution.Types.BucketOptions.Types.Linear.NumFiniteBuckets">
            <summary>
            Must be greater than 0.
            </summary>
        </member>
        <member name="F:Google.Api.Distribution.Types.BucketOptions.Types.Linear.WidthFieldNumber">
            <summary>Field number for the "width" field.</summary>
        </member>
        <member name="P:Google.Api.Distribution.Types.BucketOptions.Types.Linear.Width">
            <summary>
            Must be greater than 0.
            </summary>
        </member>
        <member name="F:Google.Api.Distribution.Types.BucketOptions.Types.Linear.OffsetFieldNumber">
            <summary>Field number for the "offset" field.</summary>
        </member>
        <member name="P:Google.Api.Distribution.Types.BucketOptions.Types.Linear.Offset">
            <summary>
            Lower bound of the first bucket.
            </summary>
        </member>
        <member name="T:Google.Api.Distribution.Types.BucketOptions.Types.Exponential">
             <summary>
             Specifies an exponential sequence of buckets that have a width that is
             proportional to the value of the lower bound. Each bucket represents a
             constant relative uncertainty on a specific value in the bucket.
            
             There are `num_finite_buckets + 2` (= N) buckets. Bucket `i` has the
             following boundaries:
            
                Upper bound (0 &lt;= i &lt; N-1):     scale * (growth_factor ^ i).
                Lower bound (1 &lt;= i &lt; N):       scale * (growth_factor ^ (i - 1)).
             </summary>
        </member>
        <member name="F:Google.Api.Distribution.Types.BucketOptions.Types.Exponential.NumFiniteBucketsFieldNumber">
            <summary>Field number for the "num_finite_buckets" field.</summary>
        </member>
        <member name="P:Google.Api.Distribution.Types.BucketOptions.Types.Exponential.NumFiniteBuckets">
            <summary>
            Must be greater than 0.
            </summary>
        </member>
        <member name="F:Google.Api.Distribution.Types.BucketOptions.Types.Exponential.GrowthFactorFieldNumber">
            <summary>Field number for the "growth_factor" field.</summary>
        </member>
        <member name="P:Google.Api.Distribution.Types.BucketOptions.Types.Exponential.GrowthFactor">
            <summary>
            Must be greater than 1.
            </summary>
        </member>
        <member name="F:Google.Api.Distribution.Types.BucketOptions.Types.Exponential.ScaleFieldNumber">
            <summary>Field number for the "scale" field.</summary>
        </member>
        <member name="P:Google.Api.Distribution.Types.BucketOptions.Types.Exponential.Scale">
            <summary>
            Must be greater than 0.
            </summary>
        </member>
        <member name="T:Google.Api.Distribution.Types.BucketOptions.Types.Explicit">
             <summary>
             Specifies a set of buckets with arbitrary widths.
            
             There are `size(bounds) + 1` (= N) buckets. Bucket `i` has the following
             boundaries:
            
                Upper bound (0 &lt;= i &lt; N-1):     bounds[i]
                Lower bound (1 &lt;= i &lt; N);       bounds[i - 1]
            
             The `bounds` field must contain at least one element. If `bounds` has
             only one element, then there are no finite buckets, and that single
             element is the common boundary of the overflow and underflow buckets.
             </summary>
        </member>
        <member name="F:Google.Api.Distribution.Types.BucketOptions.Types.Explicit.BoundsFieldNumber">
            <summary>Field number for the "bounds" field.</summary>
        </member>
        <member name="P:Google.Api.Distribution.Types.BucketOptions.Types.Explicit.Bounds">
            <summary>
            The values must be monotonically increasing.
            </summary>
        </member>
        <member name="T:Google.Api.Distribution.Types.Exemplar">
            <summary>
            Exemplars are example points that may be used to annotate aggregated
            distribution values. They are metadata that gives information about a
            particular value added to a Distribution bucket, such as a trace ID that
            was active when a value was added. They may contain further information,
            such as a example values and timestamps, origin, etc.
            </summary>
        </member>
        <member name="F:Google.Api.Distribution.Types.Exemplar.ValueFieldNumber">
            <summary>Field number for the "value" field.</summary>
        </member>
        <member name="P:Google.Api.Distribution.Types.Exemplar.Value">
            <summary>
            Value of the exemplar point. This value determines to which bucket the
            exemplar belongs.
            </summary>
        </member>
        <member name="F:Google.Api.Distribution.Types.Exemplar.TimestampFieldNumber">
            <summary>Field number for the "timestamp" field.</summary>
        </member>
        <member name="P:Google.Api.Distribution.Types.Exemplar.Timestamp">
            <summary>
            The observation (sampling) time of the above value.
            </summary>
        </member>
        <member name="F:Google.Api.Distribution.Types.Exemplar.AttachmentsFieldNumber">
            <summary>Field number for the "attachments" field.</summary>
        </member>
        <member name="P:Google.Api.Distribution.Types.Exemplar.Attachments">
             <summary>
             Contextual information about the example value. Examples are:
            
               Trace: type.googleapis.com/google.monitoring.v3.SpanContext
            
               Literal string: type.googleapis.com/google.protobuf.StringValue
            
               Labels dropped during aggregation:
                 type.googleapis.com/google.monitoring.v3.DroppedLabels
            
             There may be only a single attachment of any given message type in a
             single exemplar, and this is enforced by the system.
             </summary>
        </member>
        <member name="T:Google.Api.DocumentationReflection">
            <summary>Holder for reflection information generated from google/api/documentation.proto</summary>
        </member>
        <member name="P:Google.Api.DocumentationReflection.Descriptor">
            <summary>File descriptor for google/api/documentation.proto</summary>
        </member>
        <member name="T:Google.Api.Documentation">
             <summary>
             `Documentation` provides the information for describing a service.
            
             Example:
             &lt;pre>&lt;code>documentation:
               summary: >
                 The Google Calendar API gives access
                 to most calendar features.
               pages:
               - name: Overview
                 content: &amp;#40;== include google/foo/overview.md ==&amp;#41;
               - name: Tutorial
                 content: &amp;#40;== include google/foo/tutorial.md ==&amp;#41;
                 subpages;
                 - name: Java
                   content: &amp;#40;== include google/foo/tutorial_java.md ==&amp;#41;
               rules:
               - selector: google.calendar.Calendar.Get
                 description: >
                   ...
               - selector: google.calendar.Calendar.Put
                 description: >
                   ...
             &lt;/code>&lt;/pre>
             Documentation is provided in markdown syntax. In addition to
             standard markdown features, definition lists, tables and fenced
             code blocks are supported. Section headers can be provided and are
             interpreted relative to the section nesting of the context where
             a documentation fragment is embedded.
            
             Documentation from the IDL is merged with documentation defined
             via the config at normalization time, where documentation provided
             by config rules overrides IDL provided.
            
             A number of constructs specific to the API platform are supported
             in documentation text.
            
             In order to reference a proto element, the following
             notation can be used:
             &lt;pre>&lt;code>&amp;#91;fully.qualified.proto.name]&amp;#91;]&lt;/code>&lt;/pre>
             To override the display text used for the link, this can be used:
             &lt;pre>&lt;code>&amp;#91;display text]&amp;#91;fully.qualified.proto.name]&lt;/code>&lt;/pre>
             Text can be excluded from doc using the following notation:
             &lt;pre>&lt;code>&amp;#40;-- internal comment --&amp;#41;&lt;/code>&lt;/pre>
            
             A few directives are available in documentation. Note that
             directives must appear on a single line to be properly
             identified. The `include` directive includes a markdown file from
             an external source:
             &lt;pre>&lt;code>&amp;#40;== include path/to/file ==&amp;#41;&lt;/code>&lt;/pre>
             The `resource_for` directive marks a message to be the resource of
             a collection in REST view. If it is not specified, tools attempt
             to infer the resource from the operations in a collection:
             &lt;pre>&lt;code>&amp;#40;== resource_for v1.shelves.books ==&amp;#41;&lt;/code>&lt;/pre>
             The directive `suppress_warning` does not directly affect documentation
             and is documented together with service config validation.
             </summary>
        </member>
        <member name="F:Google.Api.Documentation.SummaryFieldNumber">
            <summary>Field number for the "summary" field.</summary>
        </member>
        <member name="P:Google.Api.Documentation.Summary">
            <summary>
            A short summary of what the service does. Can only be provided by
            plain text.
            </summary>
        </member>
        <member name="F:Google.Api.Documentation.PagesFieldNumber">
            <summary>Field number for the "pages" field.</summary>
        </member>
        <member name="P:Google.Api.Documentation.Pages">
            <summary>
            The top level pages for the documentation set.
            </summary>
        </member>
        <member name="F:Google.Api.Documentation.RulesFieldNumber">
            <summary>Field number for the "rules" field.</summary>
        </member>
        <member name="P:Google.Api.Documentation.Rules">
             <summary>
             A list of documentation rules that apply to individual API elements.
            
             **NOTE:** All service configuration rules follow "last one wins" order.
             </summary>
        </member>
        <member name="F:Google.Api.Documentation.DocumentationRootUrlFieldNumber">
            <summary>Field number for the "documentation_root_url" field.</summary>
        </member>
        <member name="P:Google.Api.Documentation.DocumentationRootUrl">
            <summary>
            The URL to the root of documentation.
            </summary>
        </member>
        <member name="F:Google.Api.Documentation.ServiceRootUrlFieldNumber">
            <summary>Field number for the "service_root_url" field.</summary>
        </member>
        <member name="P:Google.Api.Documentation.ServiceRootUrl">
            <summary>
            Specifies the service root url if the default one (the service name
            from the yaml file) is not suitable. This can be seen in any fully
            specified service urls as well as sections that show a base that other
            urls are relative to.
            </summary>
        </member>
        <member name="F:Google.Api.Documentation.OverviewFieldNumber">
            <summary>Field number for the "overview" field.</summary>
        </member>
        <member name="P:Google.Api.Documentation.Overview">
            <summary>
            Declares a single overview page. For example:
            &lt;pre>&lt;code>documentation:
              summary: ...
              overview: &amp;#40;== include overview.md ==&amp;#41;
            &lt;/code>&lt;/pre>
            This is a shortcut for the following declaration (using pages style):
            &lt;pre>&lt;code>documentation:
              summary: ...
              pages:
              - name: Overview
                content: &amp;#40;== include overview.md ==&amp;#41;
            &lt;/code>&lt;/pre>
            Note: you cannot specify both `overview` field and `pages` field.
            </summary>
        </member>
        <member name="T:Google.Api.DocumentationRule">
            <summary>
            A documentation rule provides information about individual API elements.
            </summary>
        </member>
        <member name="F:Google.Api.DocumentationRule.SelectorFieldNumber">
            <summary>Field number for the "selector" field.</summary>
        </member>
        <member name="P:Google.Api.DocumentationRule.Selector">
            <summary>
            The selector is a comma-separated list of patterns. Each pattern is a
            qualified name of the element which may end in "*", indicating a wildcard.
            Wildcards are only allowed at the end and for a whole component of the
            qualified name, i.e. "foo.*" is ok, but not "foo.b*" or "foo.*.bar". A
            wildcard will match one or more components. To specify a default for all
            applicable elements, the whole pattern "*" is used.
            </summary>
        </member>
        <member name="F:Google.Api.DocumentationRule.DescriptionFieldNumber">
            <summary>Field number for the "description" field.</summary>
        </member>
        <member name="P:Google.Api.DocumentationRule.Description">
            <summary>
            Description of the selected API(s).
            </summary>
        </member>
        <member name="F:Google.Api.DocumentationRule.DeprecationDescriptionFieldNumber">
            <summary>Field number for the "deprecation_description" field.</summary>
        </member>
        <member name="P:Google.Api.DocumentationRule.DeprecationDescription">
            <summary>
            Deprecation description of the selected element(s). It can be provided if
            an element is marked as `deprecated`.
            </summary>
        </member>
        <member name="T:Google.Api.Page">
            <summary>
            Represents a documentation page. A page can contain subpages to represent
            nested documentation set structure.
            </summary>
        </member>
        <member name="F:Google.Api.Page.NameFieldNumber">
            <summary>Field number for the "name" field.</summary>
        </member>
        <member name="P:Google.Api.Page.Name">
            <summary>
            The name of the page. It will be used as an identity of the page to
            generate URI of the page, text of the link to this page in navigation,
            etc. The full page name (start from the root page name to this page
            concatenated with `.`) can be used as reference to the page in your
            documentation. For example:
            &lt;pre>&lt;code>pages:
            - name: Tutorial
              content: &amp;#40;== include tutorial.md ==&amp;#41;
              subpages:
              - name: Java
                content: &amp;#40;== include tutorial_java.md ==&amp;#41;
            &lt;/code>&lt;/pre>
            You can reference `Java` page using Markdown reference link syntax:
            `[Java][Tutorial.Java]`.
            </summary>
        </member>
        <member name="F:Google.Api.Page.ContentFieldNumber">
            <summary>Field number for the "content" field.</summary>
        </member>
        <member name="P:Google.Api.Page.Content">
            <summary>
            The Markdown content of the page. You can use &lt;code>&amp;#40;== include {path}
            ==&amp;#41;&lt;/code> to include content from a Markdown file.
            </summary>
        </member>
        <member name="F:Google.Api.Page.SubpagesFieldNumber">
            <summary>Field number for the "subpages" field.</summary>
        </member>
        <member name="P:Google.Api.Page.Subpages">
            <summary>
            Subpages of this page. The order of subpages specified here will be
            honored in the generated docset.
            </summary>
        </member>
        <member name="T:Google.Api.EndpointReflection">
            <summary>Holder for reflection information generated from google/api/endpoint.proto</summary>
        </member>
        <member name="P:Google.Api.EndpointReflection.Descriptor">
            <summary>File descriptor for google/api/endpoint.proto</summary>
        </member>
        <member name="T:Google.Api.Endpoint">
             <summary>
             `Endpoint` describes a network endpoint of a service that serves a set of
             APIs. It is commonly known as a service endpoint. A service may expose
             any number of service endpoints, and all service endpoints share the same
             service definition, such as quota limits and monitoring metrics.
            
             Example service configuration:
            
                 name: library-example.googleapis.com
                 endpoints:
                   # Below entry makes 'google.example.library.v1.Library'
                   # API be served from endpoint address library-example.googleapis.com.
                   # It also allows HTTP OPTIONS calls to be passed to the backend, for
                   # it to decide whether the subsequent cross-origin request is
                   # allowed to proceed.
                 - name: library-example.googleapis.com
                   allow_cors: true
             </summary>
        </member>
        <member name="F:Google.Api.Endpoint.NameFieldNumber">
            <summary>Field number for the "name" field.</summary>
        </member>
        <member name="P:Google.Api.Endpoint.Name">
            <summary>
            The canonical name of this endpoint.
            </summary>
        </member>
        <member name="F:Google.Api.Endpoint.AliasesFieldNumber">
            <summary>Field number for the "aliases" field.</summary>
        </member>
        <member name="P:Google.Api.Endpoint.Aliases">
             <summary>
             Unimplemented. Dot not use.
            
             DEPRECATED: This field is no longer supported. Instead of using aliases,
             please specify multiple [google.api.Endpoint][google.api.Endpoint] for each of the intended
             aliases.
            
             Additional names that this endpoint will be hosted on.
             </summary>
        </member>
        <member name="F:Google.Api.Endpoint.TargetFieldNumber">
            <summary>Field number for the "target" field.</summary>
        </member>
        <member name="P:Google.Api.Endpoint.Target">
            <summary>
            The specification of an Internet routable address of API frontend that will
            handle requests to this [API
            Endpoint](https://cloud.google.com/apis/design/glossary). It should be
            either a valid IPv4 address or a fully-qualified domain name. For example,
            "*******" or "myservice.appspot.com".
            </summary>
        </member>
        <member name="F:Google.Api.Endpoint.AllowCorsFieldNumber">
            <summary>Field number for the "allow_cors" field.</summary>
        </member>
        <member name="P:Google.Api.Endpoint.AllowCors">
            <summary>
            Allowing
            [CORS](https://en.wikipedia.org/wiki/Cross-origin_resource_sharing), aka
            cross-domain traffic, would allow the backends served from this endpoint to
            receive and respond to HTTP OPTIONS requests. The response will be used by
            the browser to determine whether the subsequent cross-origin request is
            allowed to proceed.
            </summary>
        </member>
        <member name="T:Google.Api.ErrorReasonReflection">
            <summary>Holder for reflection information generated from google/api/error_reason.proto</summary>
        </member>
        <member name="P:Google.Api.ErrorReasonReflection.Descriptor">
            <summary>File descriptor for google/api/error_reason.proto</summary>
        </member>
        <member name="T:Google.Api.ErrorReason">
            <summary>
            Defines the supported values for `google.rpc.ErrorInfo.reason` for the
            `googleapis.com` error domain. This error domain is reserved for [Service
            Infrastructure](https://cloud.google.com/service-infrastructure/docs/overview).
            For each error info of this domain, the metadata key "service" refers to the
            logical identifier of an API service, such as "pubsub.googleapis.com". The
            "consumer" refers to the entity that consumes an API Service. It typically is
            a Google project that owns the client application or the server resource,
            such as "projects/123". Other metadata keys are specific to each error
            reason. For more information, see the definition of the specific error
            reason.
            </summary>
        </member>
        <member name="F:Google.Api.ErrorReason.Unspecified">
            <summary>
            Do not use this default value.
            </summary>
        </member>
        <member name="F:Google.Api.ErrorReason.ServiceDisabled">
             <summary>
             The request is calling a disabled service for a consumer.
            
             Example of an ErrorInfo when the consumer "projects/123" contacting
             "pubsub.googleapis.com" service which is disabled:
            
                 { "reason": "SERVICE_DISABLED",
                   "domain": "googleapis.com",
                   "metadata": {
                     "consumer": "projects/123",
                     "service": "pubsub.googleapis.com"
                   }
                 }
            
             This response indicates the "pubsub.googleapis.com" has been disabled in
             "projects/123".
             </summary>
        </member>
        <member name="F:Google.Api.ErrorReason.BillingDisabled">
             <summary>
             The request whose associated billing account is disabled.
            
             Example of an ErrorInfo when the consumer "projects/123" fails to contact
             "pubsub.googleapis.com" service because the associated billing account is
             disabled:
            
                 { "reason": "BILLING_DISABLED",
                   "domain": "googleapis.com",
                   "metadata": {
                     "consumer": "projects/123",
                     "service": "pubsub.googleapis.com"
                   }
                 }
            
             This response indicates the billing account associated has been disabled.
             </summary>
        </member>
        <member name="F:Google.Api.ErrorReason.ApiKeyInvalid">
             <summary>
             The request is denied because the provided [API
             key](https://cloud.google.com/docs/authentication/api-keys) is invalid. It
             may be in a bad format, cannot be found, or has been expired).
            
             Example of an ErrorInfo when the request is contacting
             "storage.googleapis.com" service with an invalid API key:
            
                 { "reason": "API_KEY_INVALID",
                   "domain": "googleapis.com",
                   "metadata": {
                     "service": "storage.googleapis.com",
                   }
                 }
             </summary>
        </member>
        <member name="F:Google.Api.ErrorReason.ApiKeyServiceBlocked">
             <summary>
             The request is denied because it violates [API key API
             restrictions](https://cloud.google.com/docs/authentication/api-keys#adding_api_restrictions).
            
             Example of an ErrorInfo when the consumer "projects/123" fails to call the
             "storage.googleapis.com" service because this service is restricted in the
             API key:
            
                 { "reason": "API_KEY_SERVICE_BLOCKED",
                   "domain": "googleapis.com",
                   "metadata": {
                     "consumer": "projects/123",
                     "service": "storage.googleapis.com"
                   }
                 }
             </summary>
        </member>
        <member name="F:Google.Api.ErrorReason.ApiKeyHttpReferrerBlocked">
             <summary>
             The request is denied because it violates [API key HTTP
             restrictions](https://cloud.google.com/docs/authentication/api-keys#adding_http_restrictions).
            
             Example of an ErrorInfo when the consumer "projects/123" fails to call
             "storage.googleapis.com" service because the http referrer of the request
             violates API key HTTP restrictions:
            
                 { "reason": "API_KEY_HTTP_REFERRER_BLOCKED",
                   "domain": "googleapis.com",
                   "metadata": {
                     "consumer": "projects/123",
                     "service": "storage.googleapis.com",
                   }
                 }
             </summary>
        </member>
        <member name="F:Google.Api.ErrorReason.ApiKeyIpAddressBlocked">
             <summary>
             The request is denied because it violates [API key IP address
             restrictions](https://cloud.google.com/docs/authentication/api-keys#adding_application_restrictions).
            
             Example of an ErrorInfo when the consumer "projects/123" fails to call
             "storage.googleapis.com" service because the caller IP of the request
             violates API key IP address restrictions:
            
                 { "reason": "API_KEY_IP_ADDRESS_BLOCKED",
                   "domain": "googleapis.com",
                   "metadata": {
                     "consumer": "projects/123",
                     "service": "storage.googleapis.com",
                   }
                 }
             </summary>
        </member>
        <member name="F:Google.Api.ErrorReason.ApiKeyAndroidAppBlocked">
             <summary>
             The request is denied because it violates [API key Android application
             restrictions](https://cloud.google.com/docs/authentication/api-keys#adding_application_restrictions).
            
             Example of an ErrorInfo when the consumer "projects/123" fails to call
             "storage.googleapis.com" service because the request from the Android apps
             violates the API key Android application restrictions:
            
                 { "reason": "API_KEY_ANDROID_APP_BLOCKED",
                   "domain": "googleapis.com",
                   "metadata": {
                     "consumer": "projects/123",
                     "service": "storage.googleapis.com"
                   }
                 }
             </summary>
        </member>
        <member name="F:Google.Api.ErrorReason.ApiKeyIosAppBlocked">
             <summary>
             The request is denied because it violates [API key iOS application
             restrictions](https://cloud.google.com/docs/authentication/api-keys#adding_application_restrictions).
            
             Example of an ErrorInfo when the consumer "projects/123" fails to call
             "storage.googleapis.com" service because the request from the iOS apps
             violates the API key iOS application restrictions:
            
                 { "reason": "API_KEY_IOS_APP_BLOCKED",
                   "domain": "googleapis.com",
                   "metadata": {
                     "consumer": "projects/123",
                     "service": "storage.googleapis.com"
                   }
                 }
             </summary>
        </member>
        <member name="F:Google.Api.ErrorReason.RateLimitExceeded">
             <summary>
             The request is denied because there is not enough rate quota for the
             consumer.
            
             Example of an ErrorInfo when the consumer "projects/123" fails to contact
             "pubsub.googleapis.com" service because consumer's rate quota usage has
             reached the maximum value set for the quota limit
             "ReadsPerMinutePerProject" on the quota metric
             "pubsub.googleapis.com/read_requests":
            
                 { "reason": "RATE_LIMIT_EXCEEDED",
                   "domain": "googleapis.com",
                   "metadata": {
                     "consumer": "projects/123",
                     "service": "pubsub.googleapis.com",
                     "quota_metric": "pubsub.googleapis.com/read_requests",
                     "quota_limit": "ReadsPerMinutePerProject"
                   }
                 }
            
             Example of an ErrorInfo when the consumer "projects/123" checks quota on
             the service "dataflow.googleapis.com" and hits the organization quota
             limit "DefaultRequestsPerMinutePerOrganization" on the metric
             "dataflow.googleapis.com/default_requests".
            
                 { "reason": "RATE_LIMIT_EXCEEDED",
                   "domain": "googleapis.com",
                   "metadata": {
                     "consumer": "projects/123",
                     "service": "dataflow.googleapis.com",
                     "quota_metric": "dataflow.googleapis.com/default_requests",
                     "quota_limit": "DefaultRequestsPerMinutePerOrganization"
                   }
                 }
             </summary>
        </member>
        <member name="F:Google.Api.ErrorReason.ResourceQuotaExceeded">
             <summary>
             The request is denied because there is not enough resource quota for the
             consumer.
            
             Example of an ErrorInfo when the consumer "projects/123" fails to contact
             "compute.googleapis.com" service because consumer's resource quota usage
             has reached the maximum value set for the quota limit "VMsPerProject"
             on the quota metric "compute.googleapis.com/vms":
            
                 { "reason": "RESOURCE_QUOTA_EXCEEDED",
                   "domain": "googleapis.com",
                   "metadata": {
                     "consumer": "projects/123",
                     "service": "compute.googleapis.com",
                     "quota_metric": "compute.googleapis.com/vms",
                     "quota_limit": "VMsPerProject"
                   }
                 }
            
             Example of an ErrorInfo when the consumer "projects/123" checks resource
             quota on the service "dataflow.googleapis.com" and hits the organization
             quota limit "jobs-per-organization" on the metric
             "dataflow.googleapis.com/job_count".
            
                 { "reason": "RESOURCE_QUOTA_EXCEEDED",
                   "domain": "googleapis.com",
                   "metadata": {
                     "consumer": "projects/123",
                     "service": "dataflow.googleapis.com",
                     "quota_metric": "dataflow.googleapis.com/job_count",
                     "quota_limit": "jobs-per-organization"
                   }
                 }
             </summary>
        </member>
        <member name="F:Google.Api.ErrorReason.LocationTaxPolicyViolated">
             <summary>
             The request whose associated billing account address is in a tax restricted
             location, violates the local tax restrictions when creating resources in
             the restricted region.
            
             Example of an ErrorInfo when creating the Cloud Storage Bucket in the
             container "projects/123" under a tax restricted region
             "locations/asia-northeast3":
            
                 { "reason": "LOCATION_TAX_POLICY_VIOLATED",
                   "domain": "googleapis.com",
                   "metadata": {
                     "consumer": "projects/123",
                     "service": "storage.googleapis.com",
                     "location": "locations/asia-northeast3"
                   }
                 }
            
             This response indicates creating the Cloud Storage Bucket in
             "locations/asia-northeast3" violates the location tax restriction.
             </summary>
        </member>
        <member name="F:Google.Api.ErrorReason.UserProjectDenied">
             <summary>
             The request is denied because the caller does not have required permission
             on the user project "projects/123" or the user project is invalid. For more
             information, check the [userProject System
             Parameters](https://cloud.google.com/apis/docs/system-parameters).
            
             Example of an ErrorInfo when the caller is calling Cloud Storage service
             with insufficient permissions on the user project:
            
                 { "reason": "USER_PROJECT_DENIED",
                   "domain": "googleapis.com",
                   "metadata": {
                     "consumer": "projects/123",
                     "service": "storage.googleapis.com"
                   }
                 }
             </summary>
        </member>
        <member name="F:Google.Api.ErrorReason.ConsumerSuspended">
             <summary>
             The request is denied because the consumer "projects/123" is suspended due
             to Terms of Service(Tos) violations. Check [Project suspension
             guidelines](https://cloud.google.com/resource-manager/docs/project-suspension-guidelines)
             for more information.
            
             Example of an ErrorInfo when calling Cloud Storage service with the
             suspended consumer "projects/123":
            
                 { "reason": "CONSUMER_SUSPENDED",
                   "domain": "googleapis.com",
                   "metadata": {
                     "consumer": "projects/123",
                     "service": "storage.googleapis.com"
                   }
                 }
             </summary>
        </member>
        <member name="F:Google.Api.ErrorReason.ConsumerInvalid">
             <summary>
             The request is denied because the associated consumer is invalid. It may be
             in a bad format, cannot be found, or have been deleted.
            
             Example of an ErrorInfo when calling Cloud Storage service with the
             invalid consumer "projects/123":
            
                 { "reason": "CONSUMER_INVALID",
                   "domain": "googleapis.com",
                   "metadata": {
                     "consumer": "projects/123",
                     "service": "storage.googleapis.com"
                   }
                 }
             </summary>
        </member>
        <member name="F:Google.Api.ErrorReason.SecurityPolicyViolated">
             <summary>
             The request is denied because it violates [VPC Service
             Controls](https://cloud.google.com/vpc-service-controls/docs/overview).
             The 'uid' field is a random generated identifier that customer can use it
             to search the audit log for a request rejected by VPC Service Controls. For
             more information, please refer [VPC Service Controls
             Troubleshooting](https://cloud.google.com/vpc-service-controls/docs/troubleshooting#unique-id)
            
             Example of an ErrorInfo when the consumer "projects/123" fails to call
             Cloud Storage service because the request is prohibited by the VPC Service
             Controls.
            
                 { "reason": "SECURITY_POLICY_VIOLATED",
                   "domain": "googleapis.com",
                   "metadata": {
                     "uid": "123456789abcde",
                     "consumer": "projects/123",
                     "service": "storage.googleapis.com"
                   }
                 }
             </summary>
        </member>
        <member name="F:Google.Api.ErrorReason.AccessTokenExpired">
             <summary>
             The request is denied because the provided access token has expired.
            
             Example of an ErrorInfo when the request is calling Cloud Storage service
             with an expired access token:
            
                 { "reason": "ACCESS_TOKEN_EXPIRED",
                   "domain": "googleapis.com",
                   "metadata": {
                     "service": "storage.googleapis.com",
                     "method": "google.storage.v1.Storage.GetObject"
                   }
                 }
             </summary>
        </member>
        <member name="F:Google.Api.ErrorReason.AccessTokenScopeInsufficient">
             <summary>
             The request is denied because the provided access token doesn't have at
             least one of the acceptable scopes required for the API. Please check
             [OAuth 2.0 Scopes for Google
             APIs](https://developers.google.com/identity/protocols/oauth2/scopes) for
             the list of the OAuth 2.0 scopes that you might need to request to access
             the API.
            
             Example of an ErrorInfo when the request is calling Cloud Storage service
             with an access token that is missing required scopes:
            
                 { "reason": "ACCESS_TOKEN_SCOPE_INSUFFICIENT",
                   "domain": "googleapis.com",
                   "metadata": {
                     "service": "storage.googleapis.com",
                     "method": "google.storage.v1.Storage.GetObject"
                   }
                 }
             </summary>
        </member>
        <member name="F:Google.Api.ErrorReason.AccountStateInvalid">
             <summary>
             The request is denied because the account associated with the provided
             access token is in an invalid state, such as disabled or deleted.
             For more information, see https://cloud.google.com/docs/authentication.
            
             Warning: For privacy reasons, the server may not be able to disclose the
             email address for some accounts. The client MUST NOT depend on the
             availability of the `email` attribute.
            
             Example of an ErrorInfo when the request is to the Cloud Storage API with
             an access token that is associated with a disabled or deleted [service
             account](http://cloud/iam/docs/service-accounts):
            
                 { "reason": "ACCOUNT_STATE_INVALID",
                   "domain": "googleapis.com",
                   "metadata": {
                     "service": "storage.googleapis.com",
                     "method": "google.storage.v1.Storage.GetObject",
                     "email": "<EMAIL>"
                   }
                 }
             </summary>
        </member>
        <member name="F:Google.Api.ErrorReason.AccessTokenTypeUnsupported">
             <summary>
             The request is denied because the type of the provided access token is not
             supported by the API being called.
            
             Example of an ErrorInfo when the request is to the Cloud Storage API with
             an unsupported token type.
            
                 { "reason": "ACCESS_TOKEN_TYPE_UNSUPPORTED",
                   "domain": "googleapis.com",
                   "metadata": {
                     "service": "storage.googleapis.com",
                     "method": "google.storage.v1.Storage.GetObject"
                   }
                 }
             </summary>
        </member>
        <member name="T:Google.Api.FieldBehaviorReflection">
            <summary>Holder for reflection information generated from google/api/field_behavior.proto</summary>
        </member>
        <member name="P:Google.Api.FieldBehaviorReflection.Descriptor">
            <summary>File descriptor for google/api/field_behavior.proto</summary>
        </member>
        <member name="T:Google.Api.FieldBehaviorExtensions">
            <summary>Holder for extension identifiers generated from the top level of google/api/field_behavior.proto</summary>
        </member>
        <member name="F:Google.Api.FieldBehaviorExtensions.FieldBehavior">
             <summary>
             A designation of a specific field behavior (required, output only, etc.)
             in protobuf messages.
            
             Examples:
            
               string name = 1 [(google.api.field_behavior) = REQUIRED];
               State state = 1 [(google.api.field_behavior) = OUTPUT_ONLY];
               google.protobuf.Duration ttl = 1
                 [(google.api.field_behavior) = INPUT_ONLY];
               google.protobuf.Timestamp expire_time = 1
                 [(google.api.field_behavior) = OUTPUT_ONLY,
                  (google.api.field_behavior) = IMMUTABLE];
             </summary>
        </member>
        <member name="T:Google.Api.FieldBehavior">
             <summary>
             An indicator of the behavior of a given field (for example, that a field
             is required in requests, or given as output but ignored as input).
             This **does not** change the behavior in protocol buffers itself; it only
             denotes the behavior and may affect how API tooling handles the field.
            
             Note: This enum **may** receive new values in the future.
             </summary>
        </member>
        <member name="F:Google.Api.FieldBehavior.Unspecified">
            <summary>
            Conventional default for enums. Do not use this.
            </summary>
        </member>
        <member name="F:Google.Api.FieldBehavior.Optional">
            <summary>
            Specifically denotes a field as optional.
            While all fields in protocol buffers are optional, this may be specified
            for emphasis if appropriate.
            </summary>
        </member>
        <member name="F:Google.Api.FieldBehavior.Required">
            <summary>
            Denotes a field as required.
            This indicates that the field **must** be provided as part of the request,
            and failure to do so will cause an error (usually `INVALID_ARGUMENT`).
            </summary>
        </member>
        <member name="F:Google.Api.FieldBehavior.OutputOnly">
            <summary>
            Denotes a field as output only.
            This indicates that the field is provided in responses, but including the
            field in a request does nothing (the server *must* ignore it and
            *must not* throw an error as a result of the field's presence).
            </summary>
        </member>
        <member name="F:Google.Api.FieldBehavior.InputOnly">
            <summary>
            Denotes a field as input only.
            This indicates that the field is provided in requests, and the
            corresponding field is not included in output.
            </summary>
        </member>
        <member name="F:Google.Api.FieldBehavior.Immutable">
            <summary>
            Denotes a field as immutable.
            This indicates that the field may be set once in a request to create a
            resource, but may not be changed thereafter.
            </summary>
        </member>
        <member name="F:Google.Api.FieldBehavior.UnorderedList">
            <summary>
            Denotes that a (repeated) field is an unordered list.
            This indicates that the service may provide the elements of the list
            in any arbitrary  order, rather than the order the user originally
            provided. Additionally, the list's order may or may not be stable.
            </summary>
        </member>
        <member name="F:Google.Api.FieldBehavior.NonEmptyDefault">
            <summary>
            Denotes that this field returns a non-empty default value if not set.
            This indicates that if the user provides the empty value in a request,
            a non-empty value will be returned. The user will not be aware of what
            non-empty value to expect.
            </summary>
        </member>
        <member name="T:Google.Api.HttpReflection">
            <summary>Holder for reflection information generated from google/api/http.proto</summary>
        </member>
        <member name="P:Google.Api.HttpReflection.Descriptor">
            <summary>File descriptor for google/api/http.proto</summary>
        </member>
        <member name="T:Google.Api.Http">
            <summary>
            Defines the HTTP configuration for an API service. It contains a list of
            [HttpRule][google.api.HttpRule], each specifying the mapping of an RPC method
            to one or more HTTP REST API methods.
            </summary>
        </member>
        <member name="F:Google.Api.Http.RulesFieldNumber">
            <summary>Field number for the "rules" field.</summary>
        </member>
        <member name="P:Google.Api.Http.Rules">
             <summary>
             A list of HTTP configuration rules that apply to individual API methods.
            
             **NOTE:** All service configuration rules follow "last one wins" order.
             </summary>
        </member>
        <member name="F:Google.Api.Http.FullyDecodeReservedExpansionFieldNumber">
            <summary>Field number for the "fully_decode_reserved_expansion" field.</summary>
        </member>
        <member name="P:Google.Api.Http.FullyDecodeReservedExpansion">
             <summary>
             When set to true, URL path parameters will be fully URI-decoded except in
             cases of single segment matches in reserved expansion, where "%2F" will be
             left encoded.
            
             The default behavior is to not decode RFC 6570 reserved characters in multi
             segment matches.
             </summary>
        </member>
        <member name="T:Google.Api.HttpRule">
             <summary>
             # gRPC Transcoding
            
             gRPC Transcoding is a feature for mapping between a gRPC method and one or
             more HTTP REST endpoints. It allows developers to build a single API service
             that supports both gRPC APIs and REST APIs. Many systems, including [Google
             APIs](https://github.com/googleapis/googleapis),
             [Cloud Endpoints](https://cloud.google.com/endpoints), [gRPC
             Gateway](https://github.com/grpc-ecosystem/grpc-gateway),
             and [Envoy](https://github.com/envoyproxy/envoy) proxy support this feature
             and use it for large scale production services.
            
             `HttpRule` defines the schema of the gRPC/REST mapping. The mapping specifies
             how different portions of the gRPC request message are mapped to the URL
             path, URL query parameters, and HTTP request body. It also controls how the
             gRPC response message is mapped to the HTTP response body. `HttpRule` is
             typically specified as an `google.api.http` annotation on the gRPC method.
            
             Each mapping specifies a URL path template and an HTTP method. The path
             template may refer to one or more fields in the gRPC request message, as long
             as each field is a non-repeated field with a primitive (non-message) type.
             The path template controls how fields of the request message are mapped to
             the URL path.
            
             Example:
            
                 service Messaging {
                   rpc GetMessage(GetMessageRequest) returns (Message) {
                     option (google.api.http) = {
                         get: "/v1/{name=messages/*}"
                     };
                   }
                 }
                 message GetMessageRequest {
                   string name = 1; // Mapped to URL path.
                 }
                 message Message {
                   string text = 1; // The resource content.
                 }
            
             This enables an HTTP REST to gRPC mapping as below:
            
             HTTP | gRPC
             -----|-----
             `GET /v1/messages/123456`  | `GetMessage(name: "messages/123456")`
            
             Any fields in the request message which are not bound by the path template
             automatically become HTTP query parameters if there is no HTTP request body.
             For example:
            
                 service Messaging {
                   rpc GetMessage(GetMessageRequest) returns (Message) {
                     option (google.api.http) = {
                         get:"/v1/messages/{message_id}"
                     };
                   }
                 }
                 message GetMessageRequest {
                   message SubMessage {
                     string subfield = 1;
                   }
                   string message_id = 1; // Mapped to URL path.
                   int64 revision = 2;    // Mapped to URL query parameter `revision`.
                   SubMessage sub = 3;    // Mapped to URL query parameter `sub.subfield`.
                 }
            
             This enables a HTTP JSON to RPC mapping as below:
            
             HTTP | gRPC
             -----|-----
             `GET /v1/messages/123456?revision=2&amp;sub.subfield=foo` |
             `GetMessage(message_id: "123456" revision: 2 sub: SubMessage(subfield:
             "foo"))`
            
             Note that fields which are mapped to URL query parameters must have a
             primitive type or a repeated primitive type or a non-repeated message type.
             In the case of a repeated type, the parameter can be repeated in the URL
             as `...?param=A&amp;param=B`. In the case of a message type, each field of the
             message is mapped to a separate parameter, such as
             `...?foo.a=A&amp;foo.b=B&amp;foo.c=C`.
            
             For HTTP methods that allow a request body, the `body` field
             specifies the mapping. Consider a REST update method on the
             message resource collection:
            
                 service Messaging {
                   rpc UpdateMessage(UpdateMessageRequest) returns (Message) {
                     option (google.api.http) = {
                       patch: "/v1/messages/{message_id}"
                       body: "message"
                     };
                   }
                 }
                 message UpdateMessageRequest {
                   string message_id = 1; // mapped to the URL
                   Message message = 2;   // mapped to the body
                 }
            
             The following HTTP JSON to RPC mapping is enabled, where the
             representation of the JSON in the request body is determined by
             protos JSON encoding:
            
             HTTP | gRPC
             -----|-----
             `PATCH /v1/messages/123456 { "text": "Hi!" }` | `UpdateMessage(message_id:
             "123456" message { text: "Hi!" })`
            
             The special name `*` can be used in the body mapping to define that
             every field not bound by the path template should be mapped to the
             request body.  This enables the following alternative definition of
             the update method:
            
                 service Messaging {
                   rpc UpdateMessage(Message) returns (Message) {
                     option (google.api.http) = {
                       patch: "/v1/messages/{message_id}"
                       body: "*"
                     };
                   }
                 }
                 message Message {
                   string message_id = 1;
                   string text = 2;
                 }
            
             The following HTTP JSON to RPC mapping is enabled:
            
             HTTP | gRPC
             -----|-----
             `PATCH /v1/messages/123456 { "text": "Hi!" }` | `UpdateMessage(message_id:
             "123456" text: "Hi!")`
            
             Note that when using `*` in the body mapping, it is not possible to
             have HTTP parameters, as all fields not bound by the path end in
             the body. This makes this option more rarely used in practice when
             defining REST APIs. The common usage of `*` is in custom methods
             which don't use the URL at all for transferring data.
            
             It is possible to define multiple HTTP methods for one RPC by using
             the `additional_bindings` option. Example:
            
                 service Messaging {
                   rpc GetMessage(GetMessageRequest) returns (Message) {
                     option (google.api.http) = {
                       get: "/v1/messages/{message_id}"
                       additional_bindings {
                         get: "/v1/users/{user_id}/messages/{message_id}"
                       }
                     };
                   }
                 }
                 message GetMessageRequest {
                   string message_id = 1;
                   string user_id = 2;
                 }
            
             This enables the following two alternative HTTP JSON to RPC mappings:
            
             HTTP | gRPC
             -----|-----
             `GET /v1/messages/123456` | `GetMessage(message_id: "123456")`
             `GET /v1/users/me/messages/123456` | `GetMessage(user_id: "me" message_id:
             "123456")`
            
             ## Rules for HTTP mapping
            
             1. Leaf request fields (recursive expansion nested messages in the request
                message) are classified into three categories:
                - Fields referred by the path template. They are passed via the URL path.
                - Fields referred by the [HttpRule.body][google.api.HttpRule.body]. They are passed via the HTTP
                  request body.
                - All other fields are passed via the URL query parameters, and the
                  parameter name is the field path in the request message. A repeated
                  field can be represented as multiple query parameters under the same
                  name.
              2. If [HttpRule.body][google.api.HttpRule.body] is "*", there is no URL query parameter, all fields
                 are passed via URL path and HTTP request body.
              3. If [HttpRule.body][google.api.HttpRule.body] is omitted, there is no HTTP request body, all
                 fields are passed via URL path and URL query parameters.
            
             ### Path template syntax
            
                 Template = "/" Segments [ Verb ] ;
                 Segments = Segment { "/" Segment } ;
                 Segment  = "*" | "**" | LITERAL | Variable ;
                 Variable = "{" FieldPath [ "=" Segments ] "}" ;
                 FieldPath = IDENT { "." IDENT } ;
                 Verb     = ":" LITERAL ;
            
             The syntax `*` matches a single URL path segment. The syntax `**` matches
             zero or more URL path segments, which must be the last part of the URL path
             except the `Verb`.
            
             The syntax `Variable` matches part of the URL path as specified by its
             template. A variable template must not contain other variables. If a variable
             matches a single path segment, its template may be omitted, e.g. `{var}`
             is equivalent to `{var=*}`.
            
             The syntax `LITERAL` matches literal text in the URL path. If the `LITERAL`
             contains any reserved character, such characters should be percent-encoded
             before the matching.
            
             If a variable contains exactly one path segment, such as `"{var}"` or
             `"{var=*}"`, when such a variable is expanded into a URL path on the client
             side, all characters except `[-_.~0-9a-zA-Z]` are percent-encoded. The
             server side does the reverse decoding. Such variables show up in the
             [Discovery
             Document](https://developers.google.com/discovery/v1/reference/apis) as
             `{var}`.
            
             If a variable contains multiple path segments, such as `"{var=foo/*}"`
             or `"{var=**}"`, when such a variable is expanded into a URL path on the
             client side, all characters except `[-_.~/0-9a-zA-Z]` are percent-encoded.
             The server side does the reverse decoding, except "%2F" and "%2f" are left
             unchanged. Such variables show up in the
             [Discovery
             Document](https://developers.google.com/discovery/v1/reference/apis) as
             `{+var}`.
            
             ## Using gRPC API Service Configuration
            
             gRPC API Service Configuration (service config) is a configuration language
             for configuring a gRPC service to become a user-facing product. The
             service config is simply the YAML representation of the `google.api.Service`
             proto message.
            
             As an alternative to annotating your proto file, you can configure gRPC
             transcoding in your service config YAML files. You do this by specifying a
             `HttpRule` that maps the gRPC method to a REST endpoint, achieving the same
             effect as the proto annotation. This can be particularly useful if you
             have a proto that is reused in multiple services. Note that any transcoding
             specified in the service config will override any matching transcoding
             configuration in the proto.
            
             Example:
            
                 http:
                   rules:
                     # Selects a gRPC method and applies HttpRule to it.
                     - selector: example.v1.Messaging.GetMessage
                       get: /v1/messages/{message_id}/{sub.subfield}
            
             ## Special notes
            
             When gRPC Transcoding is used to map a gRPC to JSON REST endpoints, the
             proto to JSON conversion must follow the [proto3
             specification](https://developers.google.com/protocol-buffers/docs/proto3#json).
            
             While the single segment variable follows the semantics of
             [RFC 6570](https://tools.ietf.org/html/rfc6570) Section 3.2.2 Simple String
             Expansion, the multi segment variable **does not** follow RFC 6570 Section
             3.2.3 Reserved Expansion. The reason is that the Reserved Expansion
             does not expand special characters like `?` and `#`, which would lead
             to invalid URLs. As the result, gRPC Transcoding uses a custom encoding
             for multi segment variables.
            
             The path variables **must not** refer to any repeated or mapped field,
             because client libraries are not capable of handling such variable expansion.
            
             The path variables **must not** capture the leading "/" character. The reason
             is that the most common use case "{var}" does not capture the leading "/"
             character. For consistency, all path variables must share the same behavior.
            
             Repeated message fields must not be mapped to URL query parameters, because
             no client library can support such complicated mapping.
            
             If an API needs to use a JSON array for request or response body, it can map
             the request or response body to a repeated field. However, some gRPC
             Transcoding implementations may not support this feature.
             </summary>
        </member>
        <member name="F:Google.Api.HttpRule.SelectorFieldNumber">
            <summary>Field number for the "selector" field.</summary>
        </member>
        <member name="P:Google.Api.HttpRule.Selector">
             <summary>
             Selects a method to which this rule applies.
            
             Refer to [selector][google.api.DocumentationRule.selector] for syntax details.
             </summary>
        </member>
        <member name="F:Google.Api.HttpRule.GetFieldNumber">
            <summary>Field number for the "get" field.</summary>
        </member>
        <member name="P:Google.Api.HttpRule.Get">
            <summary>
            Maps to HTTP GET. Used for listing and getting information about
            resources.
            </summary>
        </member>
        <member name="F:Google.Api.HttpRule.PutFieldNumber">
            <summary>Field number for the "put" field.</summary>
        </member>
        <member name="P:Google.Api.HttpRule.Put">
            <summary>
            Maps to HTTP PUT. Used for replacing a resource.
            </summary>
        </member>
        <member name="F:Google.Api.HttpRule.PostFieldNumber">
            <summary>Field number for the "post" field.</summary>
        </member>
        <member name="P:Google.Api.HttpRule.Post">
            <summary>
            Maps to HTTP POST. Used for creating a resource or performing an action.
            </summary>
        </member>
        <member name="F:Google.Api.HttpRule.DeleteFieldNumber">
            <summary>Field number for the "delete" field.</summary>
        </member>
        <member name="P:Google.Api.HttpRule.Delete">
            <summary>
            Maps to HTTP DELETE. Used for deleting a resource.
            </summary>
        </member>
        <member name="F:Google.Api.HttpRule.PatchFieldNumber">
            <summary>Field number for the "patch" field.</summary>
        </member>
        <member name="P:Google.Api.HttpRule.Patch">
            <summary>
            Maps to HTTP PATCH. Used for updating a resource.
            </summary>
        </member>
        <member name="F:Google.Api.HttpRule.CustomFieldNumber">
            <summary>Field number for the "custom" field.</summary>
        </member>
        <member name="P:Google.Api.HttpRule.Custom">
            <summary>
            The custom pattern is used for specifying an HTTP method that is not
            included in the `pattern` field, such as HEAD, or "*" to leave the
            HTTP method unspecified for this rule. The wild-card rule is useful
            for services that provide content to Web (HTML) clients.
            </summary>
        </member>
        <member name="F:Google.Api.HttpRule.BodyFieldNumber">
            <summary>Field number for the "body" field.</summary>
        </member>
        <member name="P:Google.Api.HttpRule.Body">
             <summary>
             The name of the request field whose value is mapped to the HTTP request
             body, or `*` for mapping all request fields not captured by the path
             pattern to the HTTP body, or omitted for not having any HTTP request body.
            
             NOTE: the referred field must be present at the top-level of the request
             message type.
             </summary>
        </member>
        <member name="F:Google.Api.HttpRule.ResponseBodyFieldNumber">
            <summary>Field number for the "response_body" field.</summary>
        </member>
        <member name="P:Google.Api.HttpRule.ResponseBody">
             <summary>
             Optional. The name of the response field whose value is mapped to the HTTP
             response body. When omitted, the entire response message will be used
             as the HTTP response body.
            
             NOTE: The referred field must be present at the top-level of the response
             message type.
             </summary>
        </member>
        <member name="F:Google.Api.HttpRule.AdditionalBindingsFieldNumber">
            <summary>Field number for the "additional_bindings" field.</summary>
        </member>
        <member name="P:Google.Api.HttpRule.AdditionalBindings">
            <summary>
            Additional HTTP bindings for the selector. Nested bindings must
            not contain an `additional_bindings` field themselves (that is,
            the nesting may only be one level deep).
            </summary>
        </member>
        <member name="T:Google.Api.HttpRule.PatternOneofCase">
            <summary>Enum of possible cases for the "pattern" oneof.</summary>
        </member>
        <member name="T:Google.Api.CustomHttpPattern">
            <summary>
            A custom pattern is used for defining custom HTTP verb.
            </summary>
        </member>
        <member name="F:Google.Api.CustomHttpPattern.KindFieldNumber">
            <summary>Field number for the "kind" field.</summary>
        </member>
        <member name="P:Google.Api.CustomHttpPattern.Kind">
            <summary>
            The name of this custom HTTP verb.
            </summary>
        </member>
        <member name="F:Google.Api.CustomHttpPattern.PathFieldNumber">
            <summary>Field number for the "path" field.</summary>
        </member>
        <member name="P:Google.Api.CustomHttpPattern.Path">
            <summary>
            The path matched by this custom verb.
            </summary>
        </member>
        <member name="T:Google.Api.HttpbodyReflection">
            <summary>Holder for reflection information generated from google/api/httpbody.proto</summary>
        </member>
        <member name="P:Google.Api.HttpbodyReflection.Descriptor">
            <summary>File descriptor for google/api/httpbody.proto</summary>
        </member>
        <member name="T:Google.Api.HttpBody">
             <summary>
             Message that represents an arbitrary HTTP body. It should only be used for
             payload formats that can't be represented as JSON, such as raw binary or
             an HTML page.
            
             This message can be used both in streaming and non-streaming API methods in
             the request as well as the response.
            
             It can be used as a top-level request field, which is convenient if one
             wants to extract parameters from either the URL or HTTP template into the
             request fields and also want access to the raw HTTP body.
            
             Example:
            
                 message GetResourceRequest {
                   // A unique request id.
                   string request_id = 1;
            
                   // The raw HTTP body is bound to this field.
                   google.api.HttpBody http_body = 2;
            
                 }
            
                 service ResourceService {
                   rpc GetResource(GetResourceRequest)
                     returns (google.api.HttpBody);
                   rpc UpdateResource(google.api.HttpBody)
                     returns (google.protobuf.Empty);
            
                 }
            
             Example with streaming methods:
            
                 service CaldavService {
                   rpc GetCalendar(stream google.api.HttpBody)
                     returns (stream google.api.HttpBody);
                   rpc UpdateCalendar(stream google.api.HttpBody)
                     returns (stream google.api.HttpBody);
            
                 }
            
             Use of this type only changes how the request and response bodies are
             handled, all other features will continue to work unchanged.
             </summary>
        </member>
        <member name="F:Google.Api.HttpBody.ContentTypeFieldNumber">
            <summary>Field number for the "content_type" field.</summary>
        </member>
        <member name="P:Google.Api.HttpBody.ContentType">
            <summary>
            The HTTP Content-Type header value specifying the content type of the body.
            </summary>
        </member>
        <member name="F:Google.Api.HttpBody.DataFieldNumber">
            <summary>Field number for the "data" field.</summary>
        </member>
        <member name="P:Google.Api.HttpBody.Data">
            <summary>
            The HTTP request/response body as raw binary.
            </summary>
        </member>
        <member name="F:Google.Api.HttpBody.ExtensionsFieldNumber">
            <summary>Field number for the "extensions" field.</summary>
        </member>
        <member name="P:Google.Api.HttpBody.Extensions">
            <summary>
            Application specific response metadata. Must be set in the first response
            for streaming APIs.
            </summary>
        </member>
        <member name="T:Google.Api.LabelReflection">
            <summary>Holder for reflection information generated from google/api/label.proto</summary>
        </member>
        <member name="P:Google.Api.LabelReflection.Descriptor">
            <summary>File descriptor for google/api/label.proto</summary>
        </member>
        <member name="T:Google.Api.LabelDescriptor">
            <summary>
            A description of a label.
            </summary>
        </member>
        <member name="F:Google.Api.LabelDescriptor.KeyFieldNumber">
            <summary>Field number for the "key" field.</summary>
        </member>
        <member name="P:Google.Api.LabelDescriptor.Key">
            <summary>
            The label key.
            </summary>
        </member>
        <member name="F:Google.Api.LabelDescriptor.ValueTypeFieldNumber">
            <summary>Field number for the "value_type" field.</summary>
        </member>
        <member name="P:Google.Api.LabelDescriptor.ValueType">
            <summary>
            The type of data that can be assigned to the label.
            </summary>
        </member>
        <member name="F:Google.Api.LabelDescriptor.DescriptionFieldNumber">
            <summary>Field number for the "description" field.</summary>
        </member>
        <member name="P:Google.Api.LabelDescriptor.Description">
            <summary>
            A human-readable description for the label.
            </summary>
        </member>
        <member name="T:Google.Api.LabelDescriptor.Types">
            <summary>Container for nested types declared in the LabelDescriptor message type.</summary>
        </member>
        <member name="T:Google.Api.LabelDescriptor.Types.ValueType">
            <summary>
            Value types that can be used as label values.
            </summary>
        </member>
        <member name="F:Google.Api.LabelDescriptor.Types.ValueType.String">
            <summary>
            A variable-length string. This is the default.
            </summary>
        </member>
        <member name="F:Google.Api.LabelDescriptor.Types.ValueType.Bool">
            <summary>
            Boolean; true or false.
            </summary>
        </member>
        <member name="F:Google.Api.LabelDescriptor.Types.ValueType.Int64">
            <summary>
            A 64-bit signed integer.
            </summary>
        </member>
        <member name="T:Google.Api.LaunchStageReflection">
            <summary>Holder for reflection information generated from google/api/launch_stage.proto</summary>
        </member>
        <member name="P:Google.Api.LaunchStageReflection.Descriptor">
            <summary>File descriptor for google/api/launch_stage.proto</summary>
        </member>
        <member name="T:Google.Api.LaunchStage">
            <summary>
            The launch stage as defined by [Google Cloud Platform
            Launch Stages](http://cloud.google.com/terms/launch-stages).
            </summary>
        </member>
        <member name="F:Google.Api.LaunchStage.Unspecified">
            <summary>
            Do not use this default value.
            </summary>
        </member>
        <member name="F:Google.Api.LaunchStage.Unimplemented">
            <summary>
            The feature is not yet implemented. Users can not use it.
            </summary>
        </member>
        <member name="F:Google.Api.LaunchStage.Prelaunch">
            <summary>
            Prelaunch features are hidden from users and are only visible internally.
            </summary>
        </member>
        <member name="F:Google.Api.LaunchStage.EarlyAccess">
            <summary>
            Early Access features are limited to a closed group of testers. To use
            these features, you must sign up in advance and sign a Trusted Tester
            agreement (which includes confidentiality provisions). These features may
            be unstable, changed in backward-incompatible ways, and are not
            guaranteed to be released.
            </summary>
        </member>
        <member name="F:Google.Api.LaunchStage.Alpha">
            <summary>
            Alpha is a limited availability test for releases before they are cleared
            for widespread use. By Alpha, all significant design issues are resolved
            and we are in the process of verifying functionality. Alpha customers
            need to apply for access, agree to applicable terms, and have their
            projects allowlisted. Alpha releases don’t have to be feature complete,
            no SLAs are provided, and there are no technical support obligations, but
            they will be far enough along that customers can actually use them in
            test environments or for limited-use tests -- just like they would in
            normal production cases.
            </summary>
        </member>
        <member name="F:Google.Api.LaunchStage.Beta">
            <summary>
            Beta is the point at which we are ready to open a release for any
            customer to use. There are no SLA or technical support obligations in a
            Beta release. Products will be complete from a feature perspective, but
            may have some open outstanding issues. Beta releases are suitable for
            limited production use cases.
            </summary>
        </member>
        <member name="F:Google.Api.LaunchStage.Ga">
            <summary>
            GA features are open to all developers and are considered stable and
            fully qualified for production use.
            </summary>
        </member>
        <member name="F:Google.Api.LaunchStage.Deprecated">
            <summary>
            Deprecated features are scheduled to be shut down and removed. For more
            information, see the “Deprecation Policy” section of our [Terms of
            Service](https://cloud.google.com/terms/)
            and the [Google Cloud Platform Subject to the Deprecation
            Policy](https://cloud.google.com/terms/deprecation) documentation.
            </summary>
        </member>
        <member name="T:Google.Api.LogReflection">
            <summary>Holder for reflection information generated from google/api/log.proto</summary>
        </member>
        <member name="P:Google.Api.LogReflection.Descriptor">
            <summary>File descriptor for google/api/log.proto</summary>
        </member>
        <member name="T:Google.Api.LogDescriptor">
             <summary>
             A description of a log type. Example in YAML format:
            
                 - name: library.googleapis.com/activity_history
                   description: The history of borrowing and returning library items.
                   display_name: Activity
                   labels:
                   - key: /customer_id
                     description: Identifier of a library customer
             </summary>
        </member>
        <member name="F:Google.Api.LogDescriptor.NameFieldNumber">
            <summary>Field number for the "name" field.</summary>
        </member>
        <member name="P:Google.Api.LogDescriptor.Name">
            <summary>
            The name of the log. It must be less than 512 characters long and can
            include the following characters: upper- and lower-case alphanumeric
            characters [A-Za-z0-9], and punctuation characters including
            slash, underscore, hyphen, period [/_-.].
            </summary>
        </member>
        <member name="F:Google.Api.LogDescriptor.LabelsFieldNumber">
            <summary>Field number for the "labels" field.</summary>
        </member>
        <member name="P:Google.Api.LogDescriptor.Labels">
            <summary>
            The set of labels that are available to describe a specific log entry.
            Runtime requests that contain labels not specified here are
            considered invalid.
            </summary>
        </member>
        <member name="F:Google.Api.LogDescriptor.DescriptionFieldNumber">
            <summary>Field number for the "description" field.</summary>
        </member>
        <member name="P:Google.Api.LogDescriptor.Description">
            <summary>
            A human-readable description of this log. This information appears in
            the documentation and can contain details.
            </summary>
        </member>
        <member name="F:Google.Api.LogDescriptor.DisplayNameFieldNumber">
            <summary>Field number for the "display_name" field.</summary>
        </member>
        <member name="P:Google.Api.LogDescriptor.DisplayName">
            <summary>
            The human-readable name for this log. This information appears on
            the user interface and should be concise.
            </summary>
        </member>
        <member name="T:Google.Api.LoggingReflection">
            <summary>Holder for reflection information generated from google/api/logging.proto</summary>
        </member>
        <member name="P:Google.Api.LoggingReflection.Descriptor">
            <summary>File descriptor for google/api/logging.proto</summary>
        </member>
        <member name="T:Google.Api.Logging">
             <summary>
             Logging configuration of the service.
            
             The following example shows how to configure logs to be sent to the
             producer and consumer projects. In the example, the `activity_history`
             log is sent to both the producer and consumer projects, whereas the
             `purchase_history` log is only sent to the producer project.
            
                 monitored_resources:
                 - type: library.googleapis.com/branch
                   labels:
                   - key: /city
                     description: The city where the library branch is located in.
                   - key: /name
                     description: The name of the branch.
                 logs:
                 - name: activity_history
                   labels:
                   - key: /customer_id
                 - name: purchase_history
                 logging:
                   producer_destinations:
                   - monitored_resource: library.googleapis.com/branch
                     logs:
                     - activity_history
                     - purchase_history
                   consumer_destinations:
                   - monitored_resource: library.googleapis.com/branch
                     logs:
                     - activity_history
             </summary>
        </member>
        <member name="F:Google.Api.Logging.ProducerDestinationsFieldNumber">
            <summary>Field number for the "producer_destinations" field.</summary>
        </member>
        <member name="P:Google.Api.Logging.ProducerDestinations">
            <summary>
            Logging configurations for sending logs to the producer project.
            There can be multiple producer destinations, each one must have a
            different monitored resource type. A log can be used in at most
            one producer destination.
            </summary>
        </member>
        <member name="F:Google.Api.Logging.ConsumerDestinationsFieldNumber">
            <summary>Field number for the "consumer_destinations" field.</summary>
        </member>
        <member name="P:Google.Api.Logging.ConsumerDestinations">
            <summary>
            Logging configurations for sending logs to the consumer project.
            There can be multiple consumer destinations, each one must have a
            different monitored resource type. A log can be used in at most
            one consumer destination.
            </summary>
        </member>
        <member name="T:Google.Api.Logging.Types">
            <summary>Container for nested types declared in the Logging message type.</summary>
        </member>
        <member name="T:Google.Api.Logging.Types.LoggingDestination">
            <summary>
            Configuration of a specific logging destination (the producer project
            or the consumer project).
            </summary>
        </member>
        <member name="F:Google.Api.Logging.Types.LoggingDestination.MonitoredResourceFieldNumber">
            <summary>Field number for the "monitored_resource" field.</summary>
        </member>
        <member name="P:Google.Api.Logging.Types.LoggingDestination.MonitoredResource">
            <summary>
            The monitored resource type. The type must be defined in the
            [Service.monitored_resources][google.api.Service.monitored_resources] section.
            </summary>
        </member>
        <member name="F:Google.Api.Logging.Types.LoggingDestination.LogsFieldNumber">
            <summary>Field number for the "logs" field.</summary>
        </member>
        <member name="P:Google.Api.Logging.Types.LoggingDestination.Logs">
            <summary>
            Names of the logs to be sent to this destination. Each name must
            be defined in the [Service.logs][google.api.Service.logs] section. If the log name is
            not a domain scoped name, it will be automatically prefixed with
            the service name followed by "/".
            </summary>
        </member>
        <member name="T:Google.Api.MetricReflection">
            <summary>Holder for reflection information generated from google/api/metric.proto</summary>
        </member>
        <member name="P:Google.Api.MetricReflection.Descriptor">
            <summary>File descriptor for google/api/metric.proto</summary>
        </member>
        <member name="T:Google.Api.MetricDescriptor">
            <summary>
            Defines a metric type and its schema. Once a metric descriptor is created,
            deleting or altering it stops data collection and makes the metric type's
            existing data unusable.
            </summary>
        </member>
        <member name="F:Google.Api.MetricDescriptor.NameFieldNumber">
            <summary>Field number for the "name" field.</summary>
        </member>
        <member name="P:Google.Api.MetricDescriptor.Name">
            <summary>
            The resource name of the metric descriptor.
            </summary>
        </member>
        <member name="F:Google.Api.MetricDescriptor.TypeFieldNumber">
            <summary>Field number for the "type" field.</summary>
        </member>
        <member name="P:Google.Api.MetricDescriptor.Type">
             <summary>
             The metric type, including its DNS name prefix. The type is not
             URL-encoded. All user-defined metric types have the DNS name
             `custom.googleapis.com` or `external.googleapis.com`. Metric types should
             use a natural hierarchical grouping. For example:
            
                 "custom.googleapis.com/invoice/paid/amount"
                 "external.googleapis.com/prometheus/up"
                 "appengine.googleapis.com/http/server/response_latencies"
             </summary>
        </member>
        <member name="F:Google.Api.MetricDescriptor.LabelsFieldNumber">
            <summary>Field number for the "labels" field.</summary>
        </member>
        <member name="P:Google.Api.MetricDescriptor.Labels">
            <summary>
            The set of labels that can be used to describe a specific
            instance of this metric type. For example, the
            `appengine.googleapis.com/http/server/response_latencies` metric
            type has a label for the HTTP response code, `response_code`, so
            you can look at latencies for successful responses or just
            for responses that failed.
            </summary>
        </member>
        <member name="F:Google.Api.MetricDescriptor.MetricKindFieldNumber">
            <summary>Field number for the "metric_kind" field.</summary>
        </member>
        <member name="P:Google.Api.MetricDescriptor.MetricKind">
            <summary>
            Whether the metric records instantaneous values, changes to a value, etc.
            Some combinations of `metric_kind` and `value_type` might not be supported.
            </summary>
        </member>
        <member name="F:Google.Api.MetricDescriptor.ValueTypeFieldNumber">
            <summary>Field number for the "value_type" field.</summary>
        </member>
        <member name="P:Google.Api.MetricDescriptor.ValueType">
            <summary>
            Whether the measurement is an integer, a floating-point number, etc.
            Some combinations of `metric_kind` and `value_type` might not be supported.
            </summary>
        </member>
        <member name="F:Google.Api.MetricDescriptor.UnitFieldNumber">
            <summary>Field number for the "unit" field.</summary>
        </member>
        <member name="P:Google.Api.MetricDescriptor.Unit">
             <summary>
             The units in which the metric value is reported. It is only applicable
             if the `value_type` is `INT64`, `DOUBLE`, or `DISTRIBUTION`. The `unit`
             defines the representation of the stored metric values.
            
             Different systems might scale the values to be more easily displayed (so a
             value of `0.02kBy` _might_ be displayed as `20By`, and a value of
             `3523kBy` _might_ be displayed as `3.5MBy`). However, if the `unit` is
             `kBy`, then the value of the metric is always in thousands of bytes, no
             matter how it might be displayed.
            
             If you want a custom metric to record the exact number of CPU-seconds used
             by a job, you can create an `INT64 CUMULATIVE` metric whose `unit` is
             `s{CPU}` (or equivalently `1s{CPU}` or just `s`). If the job uses 12,005
             CPU-seconds, then the value is written as `12005`.
            
             Alternatively, if you want a custom metric to record data in a more
             granular way, you can create a `DOUBLE CUMULATIVE` metric whose `unit` is
             `ks{CPU}`, and then write the value `12.005` (which is `12005/1000`),
             or use `Kis{CPU}` and write `11.723` (which is `12005/1024`).
            
             The supported units are a subset of [The Unified Code for Units of
             Measure](https://unitsofmeasure.org/ucum.html) standard:
            
             **Basic units (UNIT)**
            
             * `bit`   bit
             * `By`    byte
             * `s`     second
             * `min`   minute
             * `h`     hour
             * `d`     day
             * `1`     dimensionless
            
             **Prefixes (PREFIX)**
            
             * `k`     kilo    (10^3)
             * `M`     mega    (10^6)
             * `G`     giga    (10^9)
             * `T`     tera    (10^12)
             * `P`     peta    (10^15)
             * `E`     exa     (10^18)
             * `Z`     zetta   (10^21)
             * `Y`     yotta   (10^24)
            
             * `m`     milli   (10^-3)
             * `u`     micro   (10^-6)
             * `n`     nano    (10^-9)
             * `p`     pico    (10^-12)
             * `f`     femto   (10^-15)
             * `a`     atto    (10^-18)
             * `z`     zepto   (10^-21)
             * `y`     yocto   (10^-24)
            
             * `Ki`    kibi    (2^10)
             * `Mi`    mebi    (2^20)
             * `Gi`    gibi    (2^30)
             * `Ti`    tebi    (2^40)
             * `Pi`    pebi    (2^50)
            
             **Grammar**
            
             The grammar also includes these connectors:
            
             * `/`    division or ratio (as an infix operator). For examples,
                      `kBy/{email}` or `MiBy/10ms` (although you should almost never
                      have `/s` in a metric `unit`; rates should always be computed at
                      query time from the underlying cumulative or delta value).
             * `.`    multiplication or composition (as an infix operator). For
                      examples, `GBy.d` or `k{watt}.h`.
            
             The grammar for a unit is as follows:
            
                 Expression = Component { "." Component } { "/" Component } ;
            
                 Component = ( [ PREFIX ] UNIT | "%" ) [ Annotation ]
                           | Annotation
                           | "1"
                           ;
            
                 Annotation = "{" NAME "}" ;
            
             Notes:
            
             * `Annotation` is just a comment if it follows a `UNIT`. If the annotation
                is used alone, then the unit is equivalent to `1`. For examples,
                `{request}/s == 1/s`, `By{transmitted}/s == By/s`.
             * `NAME` is a sequence of non-blank printable ASCII characters not
                containing `{` or `}`.
             * `1` represents a unitary [dimensionless
                unit](https://en.wikipedia.org/wiki/Dimensionless_quantity) of 1, such
                as in `1/s`. It is typically used when none of the basic units are
                appropriate. For example, "new users per day" can be represented as
                `1/d` or `{new-users}/d` (and a metric value `5` would mean "5 new
                users). Alternatively, "thousands of page views per day" would be
                represented as `1000/d` or `k1/d` or `k{page_views}/d` (and a metric
                value of `5.3` would mean "5300 page views per day").
             * `%` represents dimensionless value of 1/100, and annotates values giving
                a percentage (so the metric values are typically in the range of 0..100,
                and a metric value `3` means "3 percent").
             * `10^2.%` indicates a metric contains a ratio, typically in the range
                0..1, that will be multiplied by 100 and displayed as a percentage
                (so a metric value `0.03` means "3 percent").
             </summary>
        </member>
        <member name="F:Google.Api.MetricDescriptor.DescriptionFieldNumber">
            <summary>Field number for the "description" field.</summary>
        </member>
        <member name="P:Google.Api.MetricDescriptor.Description">
            <summary>
            A detailed description of the metric, which can be used in documentation.
            </summary>
        </member>
        <member name="F:Google.Api.MetricDescriptor.DisplayNameFieldNumber">
            <summary>Field number for the "display_name" field.</summary>
        </member>
        <member name="P:Google.Api.MetricDescriptor.DisplayName">
            <summary>
            A concise name for the metric, which can be displayed in user interfaces.
            Use sentence case without an ending period, for example "Request count".
            This field is optional but it is recommended to be set for any metrics
            associated with user-visible concepts, such as Quota.
            </summary>
        </member>
        <member name="F:Google.Api.MetricDescriptor.MetadataFieldNumber">
            <summary>Field number for the "metadata" field.</summary>
        </member>
        <member name="P:Google.Api.MetricDescriptor.Metadata">
            <summary>
            Optional. Metadata which can be used to guide usage of the metric.
            </summary>
        </member>
        <member name="F:Google.Api.MetricDescriptor.LaunchStageFieldNumber">
            <summary>Field number for the "launch_stage" field.</summary>
        </member>
        <member name="P:Google.Api.MetricDescriptor.LaunchStage">
            <summary>
            Optional. The launch stage of the metric definition.
            </summary>
        </member>
        <member name="F:Google.Api.MetricDescriptor.MonitoredResourceTypesFieldNumber">
            <summary>Field number for the "monitored_resource_types" field.</summary>
        </member>
        <member name="P:Google.Api.MetricDescriptor.MonitoredResourceTypes">
            <summary>
            Read-only. If present, then a [time
            series][google.monitoring.v3.TimeSeries], which is identified partially by
            a metric type and a [MonitoredResourceDescriptor][google.api.MonitoredResourceDescriptor], that is associated
            with this metric type can only be associated with one of the monitored
            resource types listed here.
            </summary>
        </member>
        <member name="T:Google.Api.MetricDescriptor.Types">
            <summary>Container for nested types declared in the MetricDescriptor message type.</summary>
        </member>
        <member name="T:Google.Api.MetricDescriptor.Types.MetricKind">
            <summary>
            The kind of measurement. It describes how the data is reported.
            For information on setting the start time and end time based on
            the MetricKind, see [TimeInterval][google.monitoring.v3.TimeInterval].
            </summary>
        </member>
        <member name="F:Google.Api.MetricDescriptor.Types.MetricKind.Unspecified">
            <summary>
            Do not use this default value.
            </summary>
        </member>
        <member name="F:Google.Api.MetricDescriptor.Types.MetricKind.Gauge">
            <summary>
            An instantaneous measurement of a value.
            </summary>
        </member>
        <member name="F:Google.Api.MetricDescriptor.Types.MetricKind.Delta">
            <summary>
            The change in a value during a time interval.
            </summary>
        </member>
        <member name="F:Google.Api.MetricDescriptor.Types.MetricKind.Cumulative">
            <summary>
            A value accumulated over a time interval.  Cumulative
            measurements in a time series should have the same start time
            and increasing end times, until an event resets the cumulative
            value to zero and sets a new start time for the following
            points.
            </summary>
        </member>
        <member name="T:Google.Api.MetricDescriptor.Types.ValueType">
            <summary>
            The value type of a metric.
            </summary>
        </member>
        <member name="F:Google.Api.MetricDescriptor.Types.ValueType.Unspecified">
            <summary>
            Do not use this default value.
            </summary>
        </member>
        <member name="F:Google.Api.MetricDescriptor.Types.ValueType.Bool">
            <summary>
            The value is a boolean.
            This value type can be used only if the metric kind is `GAUGE`.
            </summary>
        </member>
        <member name="F:Google.Api.MetricDescriptor.Types.ValueType.Int64">
            <summary>
            The value is a signed 64-bit integer.
            </summary>
        </member>
        <member name="F:Google.Api.MetricDescriptor.Types.ValueType.Double">
            <summary>
            The value is a double precision floating point number.
            </summary>
        </member>
        <member name="F:Google.Api.MetricDescriptor.Types.ValueType.String">
            <summary>
            The value is a text string.
            This value type can be used only if the metric kind is `GAUGE`.
            </summary>
        </member>
        <member name="F:Google.Api.MetricDescriptor.Types.ValueType.Distribution">
            <summary>
            The value is a [`Distribution`][google.api.Distribution].
            </summary>
        </member>
        <member name="F:Google.Api.MetricDescriptor.Types.ValueType.Money">
            <summary>
            The value is money.
            </summary>
        </member>
        <member name="T:Google.Api.MetricDescriptor.Types.MetricDescriptorMetadata">
            <summary>
            Additional annotations that can be used to guide the usage of a metric.
            </summary>
        </member>
        <member name="F:Google.Api.MetricDescriptor.Types.MetricDescriptorMetadata.LaunchStageFieldNumber">
            <summary>Field number for the "launch_stage" field.</summary>
        </member>
        <member name="P:Google.Api.MetricDescriptor.Types.MetricDescriptorMetadata.LaunchStage">
            <summary>
            Deprecated. Must use the [MetricDescriptor.launch_stage][google.api.MetricDescriptor.launch_stage] instead.
            </summary>
        </member>
        <member name="F:Google.Api.MetricDescriptor.Types.MetricDescriptorMetadata.SamplePeriodFieldNumber">
            <summary>Field number for the "sample_period" field.</summary>
        </member>
        <member name="P:Google.Api.MetricDescriptor.Types.MetricDescriptorMetadata.SamplePeriod">
            <summary>
            The sampling period of metric data points. For metrics which are written
            periodically, consecutive data points are stored at this time interval,
            excluding data loss due to errors. Metrics with a higher granularity have
            a smaller sampling period.
            </summary>
        </member>
        <member name="F:Google.Api.MetricDescriptor.Types.MetricDescriptorMetadata.IngestDelayFieldNumber">
            <summary>Field number for the "ingest_delay" field.</summary>
        </member>
        <member name="P:Google.Api.MetricDescriptor.Types.MetricDescriptorMetadata.IngestDelay">
            <summary>
            The delay of data points caused by ingestion. Data points older than this
            age are guaranteed to be ingested and available to be read, excluding
            data loss due to errors.
            </summary>
        </member>
        <member name="T:Google.Api.Metric">
            <summary>
            A specific metric, identified by specifying values for all of the
            labels of a [`MetricDescriptor`][google.api.MetricDescriptor].
            </summary>
        </member>
        <member name="F:Google.Api.Metric.TypeFieldNumber">
            <summary>Field number for the "type" field.</summary>
        </member>
        <member name="P:Google.Api.Metric.Type">
            <summary>
            An existing metric type, see [google.api.MetricDescriptor][google.api.MetricDescriptor].
            For example, `custom.googleapis.com/invoice/paid/amount`.
            </summary>
        </member>
        <member name="F:Google.Api.Metric.LabelsFieldNumber">
            <summary>Field number for the "labels" field.</summary>
        </member>
        <member name="P:Google.Api.Metric.Labels">
            <summary>
            The set of label values that uniquely identify this metric. All
            labels listed in the `MetricDescriptor` must be assigned values.
            </summary>
        </member>
        <member name="T:Google.Api.MonitoredResourceReflection">
            <summary>Holder for reflection information generated from google/api/monitored_resource.proto</summary>
        </member>
        <member name="P:Google.Api.MonitoredResourceReflection.Descriptor">
            <summary>File descriptor for google/api/monitored_resource.proto</summary>
        </member>
        <member name="T:Google.Api.MonitoredResourceDescriptor">
             <summary>
             An object that describes the schema of a [MonitoredResource][google.api.MonitoredResource] object using a
             type name and a set of labels.  For example, the monitored resource
             descriptor for Google Compute Engine VM instances has a type of
             `"gce_instance"` and specifies the use of the labels `"instance_id"` and
             `"zone"` to identify particular VM instances.
            
             Different APIs can support different monitored resource types. APIs generally
             provide a `list` method that returns the monitored resource descriptors used
             by the API.
             </summary>
        </member>
        <member name="F:Google.Api.MonitoredResourceDescriptor.NameFieldNumber">
            <summary>Field number for the "name" field.</summary>
        </member>
        <member name="P:Google.Api.MonitoredResourceDescriptor.Name">
            <summary>
            Optional. The resource name of the monitored resource descriptor:
            `"projects/{project_id}/monitoredResourceDescriptors/{type}"` where
            {type} is the value of the `type` field in this object and
            {project_id} is a project ID that provides API-specific context for
            accessing the type.  APIs that do not use project information can use the
            resource name format `"monitoredResourceDescriptors/{type}"`.
            </summary>
        </member>
        <member name="F:Google.Api.MonitoredResourceDescriptor.TypeFieldNumber">
            <summary>Field number for the "type" field.</summary>
        </member>
        <member name="P:Google.Api.MonitoredResourceDescriptor.Type">
            <summary>
            Required. The monitored resource type. For example, the type
            `"cloudsql_database"` represents databases in Google Cloud SQL.
            </summary>
        </member>
        <member name="F:Google.Api.MonitoredResourceDescriptor.DisplayNameFieldNumber">
            <summary>Field number for the "display_name" field.</summary>
        </member>
        <member name="P:Google.Api.MonitoredResourceDescriptor.DisplayName">
            <summary>
            Optional. A concise name for the monitored resource type that might be
            displayed in user interfaces. It should be a Title Cased Noun Phrase,
            without any article or other determiners. For example,
            `"Google Cloud SQL Database"`.
            </summary>
        </member>
        <member name="F:Google.Api.MonitoredResourceDescriptor.DescriptionFieldNumber">
            <summary>Field number for the "description" field.</summary>
        </member>
        <member name="P:Google.Api.MonitoredResourceDescriptor.Description">
            <summary>
            Optional. A detailed description of the monitored resource type that might
            be used in documentation.
            </summary>
        </member>
        <member name="F:Google.Api.MonitoredResourceDescriptor.LabelsFieldNumber">
            <summary>Field number for the "labels" field.</summary>
        </member>
        <member name="P:Google.Api.MonitoredResourceDescriptor.Labels">
            <summary>
            Required. A set of labels used to describe instances of this monitored
            resource type. For example, an individual Google Cloud SQL database is
            identified by values for the labels `"database_id"` and `"zone"`.
            </summary>
        </member>
        <member name="F:Google.Api.MonitoredResourceDescriptor.LaunchStageFieldNumber">
            <summary>Field number for the "launch_stage" field.</summary>
        </member>
        <member name="P:Google.Api.MonitoredResourceDescriptor.LaunchStage">
            <summary>
            Optional. The launch stage of the monitored resource definition.
            </summary>
        </member>
        <member name="T:Google.Api.MonitoredResource">
             <summary>
             An object representing a resource that can be used for monitoring, logging,
             billing, or other purposes. Examples include virtual machine instances,
             databases, and storage devices such as disks. The `type` field identifies a
             [MonitoredResourceDescriptor][google.api.MonitoredResourceDescriptor] object that describes the resource's
             schema. Information in the `labels` field identifies the actual resource and
             its attributes according to the schema. For example, a particular Compute
             Engine VM instance could be represented by the following object, because the
             [MonitoredResourceDescriptor][google.api.MonitoredResourceDescriptor] for `"gce_instance"` has labels
             `"instance_id"` and `"zone"`:
            
                 { "type": "gce_instance",
                   "labels": { "instance_id": "************34",
                               "zone": "us-central1-a" }}
             </summary>
        </member>
        <member name="F:Google.Api.MonitoredResource.TypeFieldNumber">
            <summary>Field number for the "type" field.</summary>
        </member>
        <member name="P:Google.Api.MonitoredResource.Type">
            <summary>
            Required. The monitored resource type. This field must match
            the `type` field of a [MonitoredResourceDescriptor][google.api.MonitoredResourceDescriptor] object. For
            example, the type of a Compute Engine VM instance is `gce_instance`.
            </summary>
        </member>
        <member name="F:Google.Api.MonitoredResource.LabelsFieldNumber">
            <summary>Field number for the "labels" field.</summary>
        </member>
        <member name="P:Google.Api.MonitoredResource.Labels">
            <summary>
            Required. Values for all of the labels listed in the associated monitored
            resource descriptor. For example, Compute Engine VM instances use the
            labels `"project_id"`, `"instance_id"`, and `"zone"`.
            </summary>
        </member>
        <member name="T:Google.Api.MonitoredResourceMetadata">
            <summary>
            Auxiliary metadata for a [MonitoredResource][google.api.MonitoredResource] object.
            [MonitoredResource][google.api.MonitoredResource] objects contain the minimum set of information to
            uniquely identify a monitored resource instance. There is some other useful
            auxiliary metadata. Monitoring and Logging use an ingestion
            pipeline to extract metadata for cloud resources of all types, and store
            the metadata in this message.
            </summary>
        </member>
        <member name="F:Google.Api.MonitoredResourceMetadata.SystemLabelsFieldNumber">
            <summary>Field number for the "system_labels" field.</summary>
        </member>
        <member name="P:Google.Api.MonitoredResourceMetadata.SystemLabels">
             <summary>
             Output only. Values for predefined system metadata labels.
             System labels are a kind of metadata extracted by Google, including
             "machine_image", "vpc", "subnet_id",
             "security_group", "name", etc.
             System label values can be only strings, Boolean values, or a list of
             strings. For example:
            
                 { "name": "my-test-instance",
                   "security_group": ["a", "b", "c"],
                   "spot_instance": false }
             </summary>
        </member>
        <member name="F:Google.Api.MonitoredResourceMetadata.UserLabelsFieldNumber">
            <summary>Field number for the "user_labels" field.</summary>
        </member>
        <member name="P:Google.Api.MonitoredResourceMetadata.UserLabels">
            <summary>
            Output only. A map of user-defined metadata labels.
            </summary>
        </member>
        <member name="T:Google.Api.MonitoringReflection">
            <summary>Holder for reflection information generated from google/api/monitoring.proto</summary>
        </member>
        <member name="P:Google.Api.MonitoringReflection.Descriptor">
            <summary>File descriptor for google/api/monitoring.proto</summary>
        </member>
        <member name="T:Google.Api.Monitoring">
             <summary>
             Monitoring configuration of the service.
            
             The example below shows how to configure monitored resources and metrics
             for monitoring. In the example, a monitored resource and two metrics are
             defined. The `library.googleapis.com/book/returned_count` metric is sent
             to both producer and consumer projects, whereas the
             `library.googleapis.com/book/num_overdue` metric is only sent to the
             consumer project.
            
                 monitored_resources:
                 - type: library.googleapis.com/Branch
                   display_name: "Library Branch"
                   description: "A branch of a library."
                   launch_stage: GA
                   labels:
                   - key: resource_container
                     description: "The Cloud container (ie. project id) for the Branch."
                   - key: location
                     description: "The location of the library branch."
                   - key: branch_id
                     description: "The id of the branch."
                 metrics:
                 - name: library.googleapis.com/book/returned_count
                   display_name: "Books Returned"
                   description: "The count of books that have been returned."
                   launch_stage: GA
                   metric_kind: DELTA
                   value_type: INT64
                   unit: "1"
                   labels:
                   - key: customer_id
                     description: "The id of the customer."
                 - name: library.googleapis.com/book/num_overdue
                   display_name: "Books Overdue"
                   description: "The current number of overdue books."
                   launch_stage: GA
                   metric_kind: GAUGE
                   value_type: INT64
                   unit: "1"
                   labels:
                   - key: customer_id
                     description: "The id of the customer."
                 monitoring:
                   producer_destinations:
                   - monitored_resource: library.googleapis.com/Branch
                     metrics:
                     - library.googleapis.com/book/returned_count
                   consumer_destinations:
                   - monitored_resource: library.googleapis.com/Branch
                     metrics:
                     - library.googleapis.com/book/returned_count
                     - library.googleapis.com/book/num_overdue
             </summary>
        </member>
        <member name="F:Google.Api.Monitoring.ProducerDestinationsFieldNumber">
            <summary>Field number for the "producer_destinations" field.</summary>
        </member>
        <member name="P:Google.Api.Monitoring.ProducerDestinations">
            <summary>
            Monitoring configurations for sending metrics to the producer project.
            There can be multiple producer destinations. A monitored resource type may
            appear in multiple monitoring destinations if different aggregations are
            needed for different sets of metrics associated with that monitored
            resource type. A monitored resource and metric pair may only be used once
            in the Monitoring configuration.
            </summary>
        </member>
        <member name="F:Google.Api.Monitoring.ConsumerDestinationsFieldNumber">
            <summary>Field number for the "consumer_destinations" field.</summary>
        </member>
        <member name="P:Google.Api.Monitoring.ConsumerDestinations">
            <summary>
            Monitoring configurations for sending metrics to the consumer project.
            There can be multiple consumer destinations. A monitored resource type may
            appear in multiple monitoring destinations if different aggregations are
            needed for different sets of metrics associated with that monitored
            resource type. A monitored resource and metric pair may only be used once
            in the Monitoring configuration.
            </summary>
        </member>
        <member name="T:Google.Api.Monitoring.Types">
            <summary>Container for nested types declared in the Monitoring message type.</summary>
        </member>
        <member name="T:Google.Api.Monitoring.Types.MonitoringDestination">
            <summary>
            Configuration of a specific monitoring destination (the producer project
            or the consumer project).
            </summary>
        </member>
        <member name="F:Google.Api.Monitoring.Types.MonitoringDestination.MonitoredResourceFieldNumber">
            <summary>Field number for the "monitored_resource" field.</summary>
        </member>
        <member name="P:Google.Api.Monitoring.Types.MonitoringDestination.MonitoredResource">
            <summary>
            The monitored resource type. The type must be defined in
            [Service.monitored_resources][google.api.Service.monitored_resources] section.
            </summary>
        </member>
        <member name="F:Google.Api.Monitoring.Types.MonitoringDestination.MetricsFieldNumber">
            <summary>Field number for the "metrics" field.</summary>
        </member>
        <member name="P:Google.Api.Monitoring.Types.MonitoringDestination.Metrics">
            <summary>
            Types of the metrics to report to this monitoring destination.
            Each type must be defined in [Service.metrics][google.api.Service.metrics] section.
            </summary>
        </member>
        <member name="T:Google.Api.QuotaReflection">
            <summary>Holder for reflection information generated from google/api/quota.proto</summary>
        </member>
        <member name="P:Google.Api.QuotaReflection.Descriptor">
            <summary>File descriptor for google/api/quota.proto</summary>
        </member>
        <member name="T:Google.Api.Quota">
             <summary>
             Quota configuration helps to achieve fairness and budgeting in service
             usage.
            
             The metric based quota configuration works this way:
             - The service configuration defines a set of metrics.
             - For API calls, the quota.metric_rules maps methods to metrics with
               corresponding costs.
             - The quota.limits defines limits on the metrics, which will be used for
               quota checks at runtime.
            
             An example quota configuration in yaml format:
            
                quota:
                  limits:
            
                  - name: apiWriteQpsPerProject
                    metric: library.googleapis.com/write_calls
                    unit: "1/min/{project}"  # rate limit for consumer projects
                    values:
                      STANDARD: 10000
            
                  # The metric rules bind all methods to the read_calls metric,
                  # except for the UpdateBook and DeleteBook methods. These two methods
                  # are mapped to the write_calls metric, with the UpdateBook method
                  # consuming at twice rate as the DeleteBook method.
                  metric_rules:
                  - selector: "*"
                    metric_costs:
                      library.googleapis.com/read_calls: 1
                  - selector: google.example.library.v1.LibraryService.UpdateBook
                    metric_costs:
                      library.googleapis.com/write_calls: 2
                  - selector: google.example.library.v1.LibraryService.DeleteBook
                    metric_costs:
                      library.googleapis.com/write_calls: 1
            
              Corresponding Metric definition:
            
                  metrics:
                  - name: library.googleapis.com/read_calls
                    display_name: Read requests
                    metric_kind: DELTA
                    value_type: INT64
            
                  - name: library.googleapis.com/write_calls
                    display_name: Write requests
                    metric_kind: DELTA
                    value_type: INT64
             </summary>
        </member>
        <member name="F:Google.Api.Quota.LimitsFieldNumber">
            <summary>Field number for the "limits" field.</summary>
        </member>
        <member name="P:Google.Api.Quota.Limits">
            <summary>
            List of `QuotaLimit` definitions for the service.
            </summary>
        </member>
        <member name="F:Google.Api.Quota.MetricRulesFieldNumber">
            <summary>Field number for the "metric_rules" field.</summary>
        </member>
        <member name="P:Google.Api.Quota.MetricRules">
            <summary>
            List of `MetricRule` definitions, each one mapping a selected method to one
            or more metrics.
            </summary>
        </member>
        <member name="T:Google.Api.MetricRule">
            <summary>
            Bind API methods to metrics. Binding a method to a metric causes that
            metric's configured quota behaviors to apply to the method call.
            </summary>
        </member>
        <member name="F:Google.Api.MetricRule.SelectorFieldNumber">
            <summary>Field number for the "selector" field.</summary>
        </member>
        <member name="P:Google.Api.MetricRule.Selector">
             <summary>
             Selects the methods to which this rule applies.
            
             Refer to [selector][google.api.DocumentationRule.selector] for syntax details.
             </summary>
        </member>
        <member name="F:Google.Api.MetricRule.MetricCostsFieldNumber">
            <summary>Field number for the "metric_costs" field.</summary>
        </member>
        <member name="P:Google.Api.MetricRule.MetricCosts">
             <summary>
             Metrics to update when the selected methods are called, and the associated
             cost applied to each metric.
            
             The key of the map is the metric name, and the values are the amount
             increased for the metric against which the quota limits are defined.
             The value must not be negative.
             </summary>
        </member>
        <member name="T:Google.Api.QuotaLimit">
            <summary>
            `QuotaLimit` defines a specific limit that applies over a specified duration
            for a limit type. There can be at most one limit for a duration and limit
            type combination defined within a `QuotaGroup`.
            </summary>
        </member>
        <member name="F:Google.Api.QuotaLimit.NameFieldNumber">
            <summary>Field number for the "name" field.</summary>
        </member>
        <member name="P:Google.Api.QuotaLimit.Name">
             <summary>
             Name of the quota limit.
            
             The name must be provided, and it must be unique within the service. The
             name can only include alphanumeric characters as well as '-'.
            
             The maximum length of the limit name is 64 characters.
             </summary>
        </member>
        <member name="F:Google.Api.QuotaLimit.DescriptionFieldNumber">
            <summary>Field number for the "description" field.</summary>
        </member>
        <member name="P:Google.Api.QuotaLimit.Description">
            <summary>
            Optional. User-visible, extended description for this quota limit.
            Should be used only when more context is needed to understand this limit
            than provided by the limit's display name (see: `display_name`).
            </summary>
        </member>
        <member name="F:Google.Api.QuotaLimit.DefaultLimitFieldNumber">
            <summary>Field number for the "default_limit" field.</summary>
        </member>
        <member name="P:Google.Api.QuotaLimit.DefaultLimit">
             <summary>
             Default number of tokens that can be consumed during the specified
             duration. This is the number of tokens assigned when a client
             application developer activates the service for his/her project.
            
             Specifying a value of 0 will block all requests. This can be used if you
             are provisioning quota to selected consumers and blocking others.
             Similarly, a value of -1 will indicate an unlimited quota. No other
             negative values are allowed.
            
             Used by group-based quotas only.
             </summary>
        </member>
        <member name="F:Google.Api.QuotaLimit.MaxLimitFieldNumber">
            <summary>Field number for the "max_limit" field.</summary>
        </member>
        <member name="P:Google.Api.QuotaLimit.MaxLimit">
             <summary>
             Maximum number of tokens that can be consumed during the specified
             duration. Client application developers can override the default limit up
             to this maximum. If specified, this value cannot be set to a value less
             than the default limit. If not specified, it is set to the default limit.
            
             To allow clients to apply overrides with no upper bound, set this to -1,
             indicating unlimited maximum quota.
            
             Used by group-based quotas only.
             </summary>
        </member>
        <member name="F:Google.Api.QuotaLimit.FreeTierFieldNumber">
            <summary>Field number for the "free_tier" field.</summary>
        </member>
        <member name="P:Google.Api.QuotaLimit.FreeTier">
             <summary>
             Free tier value displayed in the Developers Console for this limit.
             The free tier is the number of tokens that will be subtracted from the
             billed amount when billing is enabled.
             This field can only be set on a limit with duration "1d", in a billable
             group; it is invalid on any other limit. If this field is not set, it
             defaults to 0, indicating that there is no free tier for this service.
            
             Used by group-based quotas only.
             </summary>
        </member>
        <member name="F:Google.Api.QuotaLimit.DurationFieldNumber">
            <summary>Field number for the "duration" field.</summary>
        </member>
        <member name="P:Google.Api.QuotaLimit.Duration">
             <summary>
             Duration of this limit in textual notation. Must be "100s" or "1d".
            
             Used by group-based quotas only.
             </summary>
        </member>
        <member name="F:Google.Api.QuotaLimit.MetricFieldNumber">
            <summary>Field number for the "metric" field.</summary>
        </member>
        <member name="P:Google.Api.QuotaLimit.Metric">
            <summary>
            The name of the metric this quota limit applies to. The quota limits with
            the same metric will be checked together during runtime. The metric must be
            defined within the service config.
            </summary>
        </member>
        <member name="F:Google.Api.QuotaLimit.UnitFieldNumber">
            <summary>Field number for the "unit" field.</summary>
        </member>
        <member name="P:Google.Api.QuotaLimit.Unit">
             <summary>
             Specify the unit of the quota limit. It uses the same syntax as
             [Metric.unit][]. The supported unit kinds are determined by the quota
             backend system.
            
             Here are some examples:
             * "1/min/{project}" for quota per minute per project.
            
             Note: the order of unit components is insignificant.
             The "1" at the beginning is required to follow the metric unit syntax.
             </summary>
        </member>
        <member name="F:Google.Api.QuotaLimit.ValuesFieldNumber">
            <summary>Field number for the "values" field.</summary>
        </member>
        <member name="P:Google.Api.QuotaLimit.Values">
            <summary>
            Tiered limit values. You must specify this as a key:value pair, with an
            integer value that is the maximum number of requests allowed for the
            specified unit. Currently only STANDARD is supported.
            </summary>
        </member>
        <member name="F:Google.Api.QuotaLimit.DisplayNameFieldNumber">
            <summary>Field number for the "display_name" field.</summary>
        </member>
        <member name="P:Google.Api.QuotaLimit.DisplayName">
            <summary>
            User-visible display name for this limit.
            Optional. If not set, the UI will provide a default display name based on
            the quota configuration. This field can be used to override the default
            display name generated from the configuration.
            </summary>
        </member>
        <member name="T:Google.Api.ResourceReflection">
            <summary>Holder for reflection information generated from google/api/resource.proto</summary>
        </member>
        <member name="P:Google.Api.ResourceReflection.Descriptor">
            <summary>File descriptor for google/api/resource.proto</summary>
        </member>
        <member name="T:Google.Api.ResourceExtensions">
            <summary>Holder for extension identifiers generated from the top level of google/api/resource.proto</summary>
        </member>
        <member name="F:Google.Api.ResourceExtensions.ResourceReference">
            <summary>
            An annotation that describes a resource reference, see
            [ResourceReference][].
            </summary>
        </member>
        <member name="F:Google.Api.ResourceExtensions.ResourceDefinition">
            <summary>
            An annotation that describes a resource definition without a corresponding
            message; see [ResourceDescriptor][].
            </summary>
        </member>
        <member name="F:Google.Api.ResourceExtensions.Resource">
            <summary>
            An annotation that describes a resource definition, see
            [ResourceDescriptor][].
            </summary>
        </member>
        <member name="T:Google.Api.ResourceDescriptor">
             <summary>
             A simple descriptor of a resource type.
            
             ResourceDescriptor annotates a resource message (either by means of a
             protobuf annotation or use in the service config), and associates the
             resource's schema, the resource type, and the pattern of the resource name.
            
             Example:
            
                 message Topic {
                   // Indicates this message defines a resource schema.
                   // Declares the resource type in the format of {service}/{kind}.
                   // For Kubernetes resources, the format is {api group}/{kind}.
                   option (google.api.resource) = {
                     type: "pubsub.googleapis.com/Topic"
                     pattern: "projects/{project}/topics/{topic}"
                   };
                 }
            
             The ResourceDescriptor Yaml config will look like:
            
                 resources:
                 - type: "pubsub.googleapis.com/Topic"
                   pattern: "projects/{project}/topics/{topic}"
            
             Sometimes, resources have multiple patterns, typically because they can
             live under multiple parents.
            
             Example:
            
                 message LogEntry {
                   option (google.api.resource) = {
                     type: "logging.googleapis.com/LogEntry"
                     pattern: "projects/{project}/logs/{log}"
                     pattern: "folders/{folder}/logs/{log}"
                     pattern: "organizations/{organization}/logs/{log}"
                     pattern: "billingAccounts/{billing_account}/logs/{log}"
                   };
                 }
            
             The ResourceDescriptor Yaml config will look like:
            
                 resources:
                 - type: 'logging.googleapis.com/LogEntry'
                   pattern: "projects/{project}/logs/{log}"
                   pattern: "folders/{folder}/logs/{log}"
                   pattern: "organizations/{organization}/logs/{log}"
                   pattern: "billingAccounts/{billing_account}/logs/{log}"
             </summary>
        </member>
        <member name="F:Google.Api.ResourceDescriptor.TypeFieldNumber">
            <summary>Field number for the "type" field.</summary>
        </member>
        <member name="P:Google.Api.ResourceDescriptor.Type">
             <summary>
             The resource type. It must be in the format of
             {service_name}/{resource_type_kind}. The `resource_type_kind` must be
             singular and must not include version numbers.
            
             Example: `storage.googleapis.com/Bucket`
            
             The value of the resource_type_kind must follow the regular expression
             /[A-Za-z][a-zA-Z0-9]+/. It should start with an upper case character and
             should use PascalCase (UpperCamelCase). The maximum number of
             characters allowed for the `resource_type_kind` is 100.
             </summary>
        </member>
        <member name="F:Google.Api.ResourceDescriptor.PatternFieldNumber">
            <summary>Field number for the "pattern" field.</summary>
        </member>
        <member name="P:Google.Api.ResourceDescriptor.Pattern">
             <summary>
             Optional. The relative resource name pattern associated with this resource
             type. The DNS prefix of the full resource name shouldn't be specified here.
            
             The path pattern must follow the syntax, which aligns with HTTP binding
             syntax:
            
                 Template = Segment { "/" Segment } ;
                 Segment = LITERAL | Variable ;
                 Variable = "{" LITERAL "}" ;
            
             Examples:
            
                 - "projects/{project}/topics/{topic}"
                 - "projects/{project}/knowledgeBases/{knowledge_base}"
            
             The components in braces correspond to the IDs for each resource in the
             hierarchy. It is expected that, if multiple patterns are provided,
             the same component name (e.g. "project") refers to IDs of the same
             type of resource.
             </summary>
        </member>
        <member name="F:Google.Api.ResourceDescriptor.NameFieldFieldNumber">
            <summary>Field number for the "name_field" field.</summary>
        </member>
        <member name="P:Google.Api.ResourceDescriptor.NameField">
            <summary>
            Optional. The field on the resource that designates the resource name
            field. If omitted, this is assumed to be "name".
            </summary>
        </member>
        <member name="F:Google.Api.ResourceDescriptor.HistoryFieldNumber">
            <summary>Field number for the "history" field.</summary>
        </member>
        <member name="P:Google.Api.ResourceDescriptor.History">
             <summary>
             Optional. The historical or future-looking state of the resource pattern.
            
             Example:
            
                 // The InspectTemplate message originally only supported resource
                 // names with organization, and project was added later.
                 message InspectTemplate {
                   option (google.api.resource) = {
                     type: "dlp.googleapis.com/InspectTemplate"
                     pattern:
                     "organizations/{organization}/inspectTemplates/{inspect_template}"
                     pattern: "projects/{project}/inspectTemplates/{inspect_template}"
                     history: ORIGINALLY_SINGLE_PATTERN
                   };
                 }
             </summary>
        </member>
        <member name="F:Google.Api.ResourceDescriptor.PluralFieldNumber">
            <summary>Field number for the "plural" field.</summary>
        </member>
        <member name="P:Google.Api.ResourceDescriptor.Plural">
             <summary>
             The plural name used in the resource name and permission names, such as
             'projects' for the resource name of 'projects/{project}' and the permission
             name of 'cloudresourcemanager.googleapis.com/projects.get'. It is the same
             concept of the `plural` field in k8s CRD spec
             https://kubernetes.io/docs/tasks/access-kubernetes-api/custom-resources/custom-resource-definitions/
            
             Note: The plural form is required even for singleton resources. See
             https://aip.dev/156
             </summary>
        </member>
        <member name="F:Google.Api.ResourceDescriptor.SingularFieldNumber">
            <summary>Field number for the "singular" field.</summary>
        </member>
        <member name="P:Google.Api.ResourceDescriptor.Singular">
            <summary>
            The same concept of the `singular` field in k8s CRD spec
            https://kubernetes.io/docs/tasks/access-kubernetes-api/custom-resources/custom-resource-definitions/
            Such as "project" for the `resourcemanager.googleapis.com/Project` type.
            </summary>
        </member>
        <member name="F:Google.Api.ResourceDescriptor.StyleFieldNumber">
            <summary>Field number for the "style" field.</summary>
        </member>
        <member name="P:Google.Api.ResourceDescriptor.Style">
            <summary>
            Style flag(s) for this resource.
            These indicate that a resource is expected to conform to a given
            style. See the specific style flags for additional information.
            </summary>
        </member>
        <member name="T:Google.Api.ResourceDescriptor.Types">
            <summary>Container for nested types declared in the ResourceDescriptor message type.</summary>
        </member>
        <member name="T:Google.Api.ResourceDescriptor.Types.History">
            <summary>
            A description of the historical or future-looking state of the
            resource pattern.
            </summary>
        </member>
        <member name="F:Google.Api.ResourceDescriptor.Types.History.Unspecified">
            <summary>
            The "unset" value.
            </summary>
        </member>
        <member name="F:Google.Api.ResourceDescriptor.Types.History.OriginallySinglePattern">
            <summary>
            The resource originally had one pattern and launched as such, and
            additional patterns were added later.
            </summary>
        </member>
        <member name="F:Google.Api.ResourceDescriptor.Types.History.FutureMultiPattern">
            <summary>
            The resource has one pattern, but the API owner expects to add more
            later. (This is the inverse of ORIGINALLY_SINGLE_PATTERN, and prevents
            that from being necessary once there are multiple patterns.)
            </summary>
        </member>
        <member name="T:Google.Api.ResourceDescriptor.Types.Style">
            <summary>
            A flag representing a specific style that a resource claims to conform to.
            </summary>
        </member>
        <member name="F:Google.Api.ResourceDescriptor.Types.Style.Unspecified">
            <summary>
            The unspecified value. Do not use.
            </summary>
        </member>
        <member name="F:Google.Api.ResourceDescriptor.Types.Style.DeclarativeFriendly">
             <summary>
             This resource is intended to be "declarative-friendly".
            
             Declarative-friendly resources must be more strictly consistent, and
             setting this to true communicates to tools that this resource should
             adhere to declarative-friendly expectations.
            
             Note: This is used by the API linter (linter.aip.dev) to enable
             additional checks.
             </summary>
        </member>
        <member name="T:Google.Api.ResourceReference">
            <summary>
            Defines a proto annotation that describes a string field that refers to
            an API resource.
            </summary>
        </member>
        <member name="F:Google.Api.ResourceReference.TypeFieldNumber">
            <summary>Field number for the "type" field.</summary>
        </member>
        <member name="P:Google.Api.ResourceReference.Type">
             <summary>
             The resource type that the annotated field references.
            
             Example:
            
                 message Subscription {
                   string topic = 2 [(google.api.resource_reference) = {
                     type: "pubsub.googleapis.com/Topic"
                   }];
                 }
            
             Occasionally, a field may reference an arbitrary resource. In this case,
             APIs use the special value * in their resource reference.
            
             Example:
            
                 message GetIamPolicyRequest {
                   string resource = 2 [(google.api.resource_reference) = {
                     type: "*"
                   }];
                 }
             </summary>
        </member>
        <member name="F:Google.Api.ResourceReference.ChildTypeFieldNumber">
            <summary>Field number for the "child_type" field.</summary>
        </member>
        <member name="P:Google.Api.ResourceReference.ChildType">
             <summary>
             The resource type of a child collection that the annotated field
             references. This is useful for annotating the `parent` field that
             doesn't have a fixed resource type.
            
             Example:
            
                 message ListLogEntriesRequest {
                   string parent = 1 [(google.api.resource_reference) = {
                     child_type: "logging.googleapis.com/LogEntry"
                   };
                 }
             </summary>
        </member>
        <member name="T:Google.Api.RoutingReflection">
            <summary>Holder for reflection information generated from google/api/routing.proto</summary>
        </member>
        <member name="P:Google.Api.RoutingReflection.Descriptor">
            <summary>File descriptor for google/api/routing.proto</summary>
        </member>
        <member name="T:Google.Api.RoutingExtensions">
            <summary>Holder for extension identifiers generated from the top level of google/api/routing.proto</summary>
        </member>
        <member name="F:Google.Api.RoutingExtensions.Routing">
            <summary>
            See RoutingRule.
            </summary>
        </member>
        <member name="T:Google.Api.RoutingRule">
             <summary>
             Specifies the routing information that should be sent along with the request
             in the form of routing header.
             **NOTE:** All service configuration rules follow the "last one wins" order.
            
             The examples below will apply to an RPC which has the following request type:
            
             Message Definition:
            
                 message Request {
                   // The name of the Table
                   // Values can be of the following formats:
                   // - `projects/&lt;project>/tables/&lt;table>`
                   // - `projects/&lt;project>/instances/&lt;instance>/tables/&lt;table>`
                   // - `region/&lt;region>/zones/&lt;zone>/tables/&lt;table>`
                   string table_name = 1;
            
                   // This value specifies routing for replication.
                   // It can be in the following formats:
                   // - `profiles/&lt;profile_id>`
                   // - a legacy `profile_id` that can be any string
                   string app_profile_id = 2;
                 }
            
             Example message:
            
                 {
                   table_name: projects/proj_foo/instances/instance_bar/table/table_baz,
                   app_profile_id: profiles/prof_qux
                 }
            
             The routing header consists of one or multiple key-value pairs. Every key
             and value must be percent-encoded, and joined together in the format of
             `key1=value1&amp;key2=value2`.
             In the examples below I am skipping the percent-encoding for readablity.
            
             Example 1
            
             Extracting a field from the request to put into the routing header
             unchanged, with the key equal to the field name.
            
             annotation:
            
                 option (google.api.routing) = {
                   // Take the `app_profile_id`.
                   routing_parameters {
                     field: "app_profile_id"
                   }
                 };
            
             result:
            
                 x-goog-request-params: app_profile_id=profiles/prof_qux
            
             Example 2
            
             Extracting a field from the request to put into the routing header
             unchanged, with the key different from the field name.
            
             annotation:
            
                 option (google.api.routing) = {
                   // Take the `app_profile_id`, but name it `routing_id` in the header.
                   routing_parameters {
                     field: "app_profile_id"
                     path_template: "{routing_id=**}"
                   }
                 };
            
             result:
            
                 x-goog-request-params: routing_id=profiles/prof_qux
            
             Example 3
            
             Extracting a field from the request to put into the routing
             header, while matching a path template syntax on the field's value.
            
             NB: it is more useful to send nothing than to send garbage for the purpose
             of dynamic routing, since garbage pollutes cache. Thus the matching.
            
             Sub-example 3a
            
             The field matches the template.
            
             annotation:
            
                 option (google.api.routing) = {
                   // Take the `table_name`, if it's well-formed (with project-based
                   // syntax).
                   routing_parameters {
                     field: "table_name"
                     path_template: "{table_name=projects/*/instances/*/**}"
                   }
                 };
            
             result:
            
                 x-goog-request-params:
                 table_name=projects/proj_foo/instances/instance_bar/table/table_baz
            
             Sub-example 3b
            
             The field does not match the template.
            
             annotation:
            
                 option (google.api.routing) = {
                   // Take the `table_name`, if it's well-formed (with region-based
                   // syntax).
                   routing_parameters {
                     field: "table_name"
                     path_template: "{table_name=regions/*/zones/*/**}"
                   }
                 };
            
             result:
            
                 &lt;no routing header will be sent>
            
             Sub-example 3c
            
             Multiple alternative conflictingly named path templates are
             specified. The one that matches is used to construct the header.
            
             annotation:
            
                 option (google.api.routing) = {
                   // Take the `table_name`, if it's well-formed, whether
                   // using the region- or projects-based syntax.
            
                   routing_parameters {
                     field: "table_name"
                     path_template: "{table_name=regions/*/zones/*/**}"
                   }
                   routing_parameters {
                     field: "table_name"
                     path_template: "{table_name=projects/*/instances/*/**}"
                   }
                 };
            
             result:
            
                 x-goog-request-params:
                 table_name=projects/proj_foo/instances/instance_bar/table/table_baz
            
             Example 4
            
             Extracting a single routing header key-value pair by matching a
             template syntax on (a part of) a single request field.
            
             annotation:
            
                 option (google.api.routing) = {
                   // Take just the project id from the `table_name` field.
                   routing_parameters {
                     field: "table_name"
                     path_template: "{routing_id=projects/*}/**"
                   }
                 };
            
             result:
            
                 x-goog-request-params: routing_id=projects/proj_foo
            
             Example 5
            
             Extracting a single routing header key-value pair by matching
             several conflictingly named path templates on (parts of) a single request
             field. The last template to match "wins" the conflict.
            
             annotation:
            
                 option (google.api.routing) = {
                   // If the `table_name` does not have instances information,
                   // take just the project id for routing.
                   // Otherwise take project + instance.
            
                   routing_parameters {
                     field: "table_name"
                     path_template: "{routing_id=projects/*}/**"
                   }
                   routing_parameters {
                     field: "table_name"
                     path_template: "{routing_id=projects/*/instances/*}/**"
                   }
                 };
            
             result:
            
                 x-goog-request-params:
                 routing_id=projects/proj_foo/instances/instance_bar
            
             Example 6
            
             Extracting multiple routing header key-value pairs by matching
             several non-conflicting path templates on (parts of) a single request field.
            
             Sub-example 6a
            
             Make the templates strict, so that if the `table_name` does not
             have an instance information, nothing is sent.
            
             annotation:
            
                 option (google.api.routing) = {
                   // The routing code needs two keys instead of one composite
                   // but works only for the tables with the "project-instance" name
                   // syntax.
            
                   routing_parameters {
                     field: "table_name"
                     path_template: "{project_id=projects/*}/instances/*/**"
                   }
                   routing_parameters {
                     field: "table_name"
                     path_template: "projects/*/{instance_id=instances/*}/**"
                   }
                 };
            
             result:
            
                 x-goog-request-params:
                 project_id=projects/proj_foo&amp;instance_id=instances/instance_bar
            
             Sub-example 6b
            
             Make the templates loose, so that if the `table_name` does not
             have an instance information, just the project id part is sent.
            
             annotation:
            
                 option (google.api.routing) = {
                   // The routing code wants two keys instead of one composite
                   // but will work with just the `project_id` for tables without
                   // an instance in the `table_name`.
            
                   routing_parameters {
                     field: "table_name"
                     path_template: "{project_id=projects/*}/**"
                   }
                   routing_parameters {
                     field: "table_name"
                     path_template: "projects/*/{instance_id=instances/*}/**"
                   }
                 };
            
             result (is the same as 6a for our example message because it has the instance
             information):
            
                 x-goog-request-params:
                 project_id=projects/proj_foo&amp;instance_id=instances/instance_bar
            
             Example 7
            
             Extracting multiple routing header key-value pairs by matching
             several path templates on multiple request fields.
            
             NB: note that here there is no way to specify sending nothing if one of the
             fields does not match its template. E.g. if the `table_name` is in the wrong
             format, the `project_id` will not be sent, but the `routing_id` will be.
             The backend routing code has to be aware of that and be prepared to not
             receive a full complement of keys if it expects multiple.
            
             annotation:
            
                 option (google.api.routing) = {
                   // The routing needs both `project_id` and `routing_id`
                   // (from the `app_profile_id` field) for routing.
            
                   routing_parameters {
                     field: "table_name"
                     path_template: "{project_id=projects/*}/**"
                   }
                   routing_parameters {
                     field: "app_profile_id"
                     path_template: "{routing_id=**}"
                   }
                 };
            
             result:
            
                 x-goog-request-params:
                 project_id=projects/proj_foo&amp;routing_id=profiles/prof_qux
            
             Example 8
            
             Extracting a single routing header key-value pair by matching
             several conflictingly named path templates on several request fields. The
             last template to match "wins" the conflict.
            
             annotation:
            
                 option (google.api.routing) = {
                   // The `routing_id` can be a project id or a region id depending on
                   // the table name format, but only if the `app_profile_id` is not set.
                   // If `app_profile_id` is set it should be used instead.
            
                   routing_parameters {
                     field: "table_name"
                     path_template: "{routing_id=projects/*}/**"
                   }
                   routing_parameters {
                      field: "table_name"
                      path_template: "{routing_id=regions/*}/**"
                   }
                   routing_parameters {
                     field: "app_profile_id"
                     path_template: "{routing_id=**}"
                   }
                 };
            
             result:
            
                 x-goog-request-params: routing_id=profiles/prof_qux
            
             Example 9
            
             Bringing it all together.
            
             annotation:
            
                 option (google.api.routing) = {
                   // For routing both `table_location` and a `routing_id` are needed.
                   //
                   // table_location can be either an instance id or a region+zone id.
                   //
                   // For `routing_id`, take the value of `app_profile_id`
                   // - If it's in the format `profiles/&lt;profile_id>`, send
                   // just the `&lt;profile_id>` part.
                   // - If it's any other literal, send it as is.
                   // If the `app_profile_id` is empty, and the `table_name` starts with
                   // the project_id, send that instead.
            
                   routing_parameters {
                     field: "table_name"
                     path_template: "projects/*/{table_location=instances/*}/tables/*"
                   }
                   routing_parameters {
                     field: "table_name"
                     path_template: "{table_location=regions/*/zones/*}/tables/*"
                   }
                   routing_parameters {
                     field: "table_name"
                     path_template: "{routing_id=projects/*}/**"
                   }
                   routing_parameters {
                     field: "app_profile_id"
                     path_template: "{routing_id=**}"
                   }
                   routing_parameters {
                     field: "app_profile_id"
                     path_template: "profiles/{routing_id=*}"
                   }
                 };
            
             result:
            
                 x-goog-request-params:
                 table_location=instances/instance_bar&amp;routing_id=prof_qux
             </summary>
        </member>
        <member name="F:Google.Api.RoutingRule.RoutingParametersFieldNumber">
            <summary>Field number for the "routing_parameters" field.</summary>
        </member>
        <member name="P:Google.Api.RoutingRule.RoutingParameters">
            <summary>
            A collection of Routing Parameter specifications.
            **NOTE:** If multiple Routing Parameters describe the same key
            (via the `path_template` field or via the `field` field when
            `path_template` is not provided), "last one wins" rule
            determines which Parameter gets used.
            See the examples for more details.
            </summary>
        </member>
        <member name="T:Google.Api.RoutingParameter">
            <summary>
            A projection from an input message to the GRPC or REST header.
            </summary>
        </member>
        <member name="F:Google.Api.RoutingParameter.FieldFieldNumber">
            <summary>Field number for the "field" field.</summary>
        </member>
        <member name="P:Google.Api.RoutingParameter.Field">
            <summary>
            A request field to extract the header key-value pair from.
            </summary>
        </member>
        <member name="F:Google.Api.RoutingParameter.PathTemplateFieldNumber">
            <summary>Field number for the "path_template" field.</summary>
        </member>
        <member name="P:Google.Api.RoutingParameter.PathTemplate">
             <summary>
             A pattern matching the key-value field. Optional.
             If not specified, the whole field specified in the `field` field will be
             taken as value, and its name used as key. If specified, it MUST contain
             exactly one named segment (along with any number of unnamed segments) The
             pattern will be matched over the field specified in the `field` field, then
             if the match is successful:
             - the name of the single named segment will be used as a header name,
             - the match value of the segment will be used as a header value;
             if the match is NOT successful, nothing will be sent.
            
             Example:
            
                           -- This is a field in the request message
                          |   that the header value will be extracted from.
                          |
                          |                     -- This is the key name in the
                          |                    |   routing header.
                          V                    |
                 field: "table_name"           v
                 path_template: "projects/*/{table_location=instances/*}/tables/*"
                                                            ^            ^
                                                            |            |
                   In the {} brackets is the pattern that --             |
                   specifies what to extract from the                    |
                   field as a value to be sent.                          |
                                                                         |
                  The string in the field must match the whole pattern --
                  before brackets, inside brackets, after brackets.
            
             When looking at this specific example, we can see that:
             - A key-value pair with the key `table_location`
               and the value matching `instances/*` should be added
               to the x-goog-request-params routing header.
             - The value is extracted from the request message's `table_name` field
               if it matches the full pattern specified:
               `projects/*/instances/*/tables/*`.
            
             **NB:** If the `path_template` field is not provided, the key name is
             equal to the field name, and the whole field should be sent as a value.
             This makes the pattern for the field and the value functionally equivalent
             to `**`, and the configuration
            
                 {
                   field: "table_name"
                 }
            
             is a functionally equivalent shorthand to:
            
                 {
                   field: "table_name"
                   path_template: "{table_name=**}"
                 }
            
             See Example 1 for more details.
             </summary>
        </member>
        <member name="T:Google.Api.ServiceReflection">
            <summary>Holder for reflection information generated from google/api/service.proto</summary>
        </member>
        <member name="P:Google.Api.ServiceReflection.Descriptor">
            <summary>File descriptor for google/api/service.proto</summary>
        </member>
        <member name="T:Google.Api.Service">
             <summary>
             `Service` is the root object of Google service configuration schema. It
             describes basic information about a service, such as the name and the
             title, and delegates other aspects to sub-sections. Each sub-section is
             either a proto message or a repeated proto message that configures a
             specific aspect, such as auth. See each proto message definition for details.
            
             Example:
            
                 type: google.api.Service
                 name: calendar.googleapis.com
                 title: Google Calendar API
                 apis:
                 - name: google.calendar.v3.Calendar
                 authentication:
                   providers:
                   - id: google_calendar_auth
                     jwks_uri: https://www.googleapis.com/oauth2/v1/certs
                     issuer: https://securetoken.google.com
                   rules:
                   - selector: "*"
                     requirements:
                       provider_id: google_calendar_auth
             </summary>
        </member>
        <member name="F:Google.Api.Service.NameFieldNumber">
            <summary>Field number for the "name" field.</summary>
        </member>
        <member name="P:Google.Api.Service.Name">
            <summary>
            The service name, which is a DNS-like logical identifier for the
            service, such as `calendar.googleapis.com`. The service name
            typically goes through DNS verification to make sure the owner
            of the service also owns the DNS name.
            </summary>
        </member>
        <member name="F:Google.Api.Service.TitleFieldNumber">
            <summary>Field number for the "title" field.</summary>
        </member>
        <member name="P:Google.Api.Service.Title">
            <summary>
            The product title for this service.
            </summary>
        </member>
        <member name="F:Google.Api.Service.ProducerProjectIdFieldNumber">
            <summary>Field number for the "producer_project_id" field.</summary>
        </member>
        <member name="P:Google.Api.Service.ProducerProjectId">
            <summary>
            The Google project that owns this service.
            </summary>
        </member>
        <member name="F:Google.Api.Service.IdFieldNumber">
            <summary>Field number for the "id" field.</summary>
        </member>
        <member name="P:Google.Api.Service.Id">
            <summary>
            A unique ID for a specific instance of this message, typically assigned
            by the client for tracking purpose. Must be no longer than 63 characters
            and only lower case letters, digits, '.', '_' and '-' are allowed. If
            empty, the server may choose to generate one instead.
            </summary>
        </member>
        <member name="F:Google.Api.Service.ApisFieldNumber">
            <summary>Field number for the "apis" field.</summary>
        </member>
        <member name="P:Google.Api.Service.Apis">
            <summary>
            A list of API interfaces exported by this service. Only the `name` field
            of the [google.protobuf.Api][google.protobuf.Api] needs to be provided by the configuration
            author, as the remaining fields will be derived from the IDL during the
            normalization process. It is an error to specify an API interface here
            which cannot be resolved against the associated IDL files.
            </summary>
        </member>
        <member name="F:Google.Api.Service.Types_FieldNumber">
            <summary>Field number for the "types" field.</summary>
        </member>
        <member name="P:Google.Api.Service.Types_">
             <summary>
             A list of all proto message types included in this API service.
             Types referenced directly or indirectly by the `apis` are
             automatically included.  Messages which are not referenced but
             shall be included, such as types used by the `google.protobuf.Any` type,
             should be listed here by name. Example:
            
                 types:
                 - name: google.protobuf.Int32
             </summary>
        </member>
        <member name="F:Google.Api.Service.EnumsFieldNumber">
            <summary>Field number for the "enums" field.</summary>
        </member>
        <member name="P:Google.Api.Service.Enums">
             <summary>
             A list of all enum types included in this API service.  Enums
             referenced directly or indirectly by the `apis` are automatically
             included.  Enums which are not referenced but shall be included
             should be listed here by name. Example:
            
                 enums:
                 - name: google.someapi.v1.SomeEnum
             </summary>
        </member>
        <member name="F:Google.Api.Service.DocumentationFieldNumber">
            <summary>Field number for the "documentation" field.</summary>
        </member>
        <member name="P:Google.Api.Service.Documentation">
            <summary>
            Additional API documentation.
            </summary>
        </member>
        <member name="F:Google.Api.Service.BackendFieldNumber">
            <summary>Field number for the "backend" field.</summary>
        </member>
        <member name="P:Google.Api.Service.Backend">
            <summary>
            API backend configuration.
            </summary>
        </member>
        <member name="F:Google.Api.Service.HttpFieldNumber">
            <summary>Field number for the "http" field.</summary>
        </member>
        <member name="P:Google.Api.Service.Http">
            <summary>
            HTTP configuration.
            </summary>
        </member>
        <member name="F:Google.Api.Service.QuotaFieldNumber">
            <summary>Field number for the "quota" field.</summary>
        </member>
        <member name="P:Google.Api.Service.Quota">
            <summary>
            Quota configuration.
            </summary>
        </member>
        <member name="F:Google.Api.Service.AuthenticationFieldNumber">
            <summary>Field number for the "authentication" field.</summary>
        </member>
        <member name="P:Google.Api.Service.Authentication">
            <summary>
            Auth configuration.
            </summary>
        </member>
        <member name="F:Google.Api.Service.ContextFieldNumber">
            <summary>Field number for the "context" field.</summary>
        </member>
        <member name="P:Google.Api.Service.Context">
            <summary>
            Context configuration.
            </summary>
        </member>
        <member name="F:Google.Api.Service.UsageFieldNumber">
            <summary>Field number for the "usage" field.</summary>
        </member>
        <member name="P:Google.Api.Service.Usage">
            <summary>
            Configuration controlling usage of this service.
            </summary>
        </member>
        <member name="F:Google.Api.Service.EndpointsFieldNumber">
            <summary>Field number for the "endpoints" field.</summary>
        </member>
        <member name="P:Google.Api.Service.Endpoints">
            <summary>
            Configuration for network endpoints.  If this is empty, then an endpoint
            with the same name as the service is automatically generated to service all
            defined APIs.
            </summary>
        </member>
        <member name="F:Google.Api.Service.ControlFieldNumber">
            <summary>Field number for the "control" field.</summary>
        </member>
        <member name="P:Google.Api.Service.Control">
            <summary>
            Configuration for the service control plane.
            </summary>
        </member>
        <member name="F:Google.Api.Service.LogsFieldNumber">
            <summary>Field number for the "logs" field.</summary>
        </member>
        <member name="P:Google.Api.Service.Logs">
            <summary>
            Defines the logs used by this service.
            </summary>
        </member>
        <member name="F:Google.Api.Service.MetricsFieldNumber">
            <summary>Field number for the "metrics" field.</summary>
        </member>
        <member name="P:Google.Api.Service.Metrics">
            <summary>
            Defines the metrics used by this service.
            </summary>
        </member>
        <member name="F:Google.Api.Service.MonitoredResourcesFieldNumber">
            <summary>Field number for the "monitored_resources" field.</summary>
        </member>
        <member name="P:Google.Api.Service.MonitoredResources">
            <summary>
            Defines the monitored resources used by this service. This is required
            by the [Service.monitoring][google.api.Service.monitoring] and [Service.logging][google.api.Service.logging] configurations.
            </summary>
        </member>
        <member name="F:Google.Api.Service.BillingFieldNumber">
            <summary>Field number for the "billing" field.</summary>
        </member>
        <member name="P:Google.Api.Service.Billing">
            <summary>
            Billing configuration.
            </summary>
        </member>
        <member name="F:Google.Api.Service.LoggingFieldNumber">
            <summary>Field number for the "logging" field.</summary>
        </member>
        <member name="P:Google.Api.Service.Logging">
            <summary>
            Logging configuration.
            </summary>
        </member>
        <member name="F:Google.Api.Service.MonitoringFieldNumber">
            <summary>Field number for the "monitoring" field.</summary>
        </member>
        <member name="P:Google.Api.Service.Monitoring">
            <summary>
            Monitoring configuration.
            </summary>
        </member>
        <member name="F:Google.Api.Service.SystemParametersFieldNumber">
            <summary>Field number for the "system_parameters" field.</summary>
        </member>
        <member name="P:Google.Api.Service.SystemParameters">
            <summary>
            System parameter configuration.
            </summary>
        </member>
        <member name="F:Google.Api.Service.SourceInfoFieldNumber">
            <summary>Field number for the "source_info" field.</summary>
        </member>
        <member name="P:Google.Api.Service.SourceInfo">
            <summary>
            Output only. The source information for this configuration if available.
            </summary>
        </member>
        <member name="F:Google.Api.Service.ConfigVersionFieldNumber">
            <summary>Field number for the "config_version" field.</summary>
        </member>
        <member name="P:Google.Api.Service.ConfigVersion">
             <summary>
             Obsolete. Do not use.
            
             This field has no semantic meaning. The service config compiler always
             sets this field to `3`.
             </summary>
        </member>
        <member name="T:Google.Api.SourceInfoReflection">
            <summary>Holder for reflection information generated from google/api/source_info.proto</summary>
        </member>
        <member name="P:Google.Api.SourceInfoReflection.Descriptor">
            <summary>File descriptor for google/api/source_info.proto</summary>
        </member>
        <member name="T:Google.Api.SourceInfo">
            <summary>
            Source information used to create a Service Config
            </summary>
        </member>
        <member name="F:Google.Api.SourceInfo.SourceFilesFieldNumber">
            <summary>Field number for the "source_files" field.</summary>
        </member>
        <member name="P:Google.Api.SourceInfo.SourceFiles">
            <summary>
            All files used during config generation.
            </summary>
        </member>
        <member name="T:Google.Api.SystemParameterReflection">
            <summary>Holder for reflection information generated from google/api/system_parameter.proto</summary>
        </member>
        <member name="P:Google.Api.SystemParameterReflection.Descriptor">
            <summary>File descriptor for google/api/system_parameter.proto</summary>
        </member>
        <member name="T:Google.Api.SystemParameters">
             <summary>
             ### System parameter configuration
            
             A system parameter is a special kind of parameter defined by the API
             system, not by an individual API. It is typically mapped to an HTTP header
             and/or a URL query parameter. This configuration specifies which methods
             change the names of the system parameters.
             </summary>
        </member>
        <member name="F:Google.Api.SystemParameters.RulesFieldNumber">
            <summary>Field number for the "rules" field.</summary>
        </member>
        <member name="P:Google.Api.SystemParameters.Rules">
             <summary>
             Define system parameters.
            
             The parameters defined here will override the default parameters
             implemented by the system. If this field is missing from the service
             config, default system parameters will be used. Default system parameters
             and names is implementation-dependent.
            
             Example: define api key for all methods
            
                 system_parameters
                   rules:
                     - selector: "*"
                       parameters:
                         - name: api_key
                           url_query_parameter: api_key
            
             Example: define 2 api key names for a specific method.
            
                 system_parameters
                   rules:
                     - selector: "/ListShelves"
                       parameters:
                         - name: api_key
                           http_header: Api-Key1
                         - name: api_key
                           http_header: Api-Key2
            
             **NOTE:** All service configuration rules follow "last one wins" order.
             </summary>
        </member>
        <member name="T:Google.Api.SystemParameterRule">
            <summary>
            Define a system parameter rule mapping system parameter definitions to
            methods.
            </summary>
        </member>
        <member name="F:Google.Api.SystemParameterRule.SelectorFieldNumber">
            <summary>Field number for the "selector" field.</summary>
        </member>
        <member name="P:Google.Api.SystemParameterRule.Selector">
             <summary>
             Selects the methods to which this rule applies. Use '*' to indicate all
             methods in all APIs.
            
             Refer to [selector][google.api.DocumentationRule.selector] for syntax details.
             </summary>
        </member>
        <member name="F:Google.Api.SystemParameterRule.ParametersFieldNumber">
            <summary>Field number for the "parameters" field.</summary>
        </member>
        <member name="P:Google.Api.SystemParameterRule.Parameters">
            <summary>
            Define parameters. Multiple names may be defined for a parameter.
            For a given method call, only one of them should be used. If multiple
            names are used the behavior is implementation-dependent.
            If none of the specified names are present the behavior is
            parameter-dependent.
            </summary>
        </member>
        <member name="T:Google.Api.SystemParameter">
            <summary>
            Define a parameter's name and location. The parameter may be passed as either
            an HTTP header or a URL query parameter, and if both are passed the behavior
            is implementation-dependent.
            </summary>
        </member>
        <member name="F:Google.Api.SystemParameter.NameFieldNumber">
            <summary>Field number for the "name" field.</summary>
        </member>
        <member name="P:Google.Api.SystemParameter.Name">
            <summary>
            Define the name of the parameter, such as "api_key" . It is case sensitive.
            </summary>
        </member>
        <member name="F:Google.Api.SystemParameter.HttpHeaderFieldNumber">
            <summary>Field number for the "http_header" field.</summary>
        </member>
        <member name="P:Google.Api.SystemParameter.HttpHeader">
            <summary>
            Define the HTTP header name to use for the parameter. It is case
            insensitive.
            </summary>
        </member>
        <member name="F:Google.Api.SystemParameter.UrlQueryParameterFieldNumber">
            <summary>Field number for the "url_query_parameter" field.</summary>
        </member>
        <member name="P:Google.Api.SystemParameter.UrlQueryParameter">
            <summary>
            Define the URL query parameter name to use for the parameter. It is case
            sensitive.
            </summary>
        </member>
        <member name="T:Google.Api.UsageReflection">
            <summary>Holder for reflection information generated from google/api/usage.proto</summary>
        </member>
        <member name="P:Google.Api.UsageReflection.Descriptor">
            <summary>File descriptor for google/api/usage.proto</summary>
        </member>
        <member name="T:Google.Api.Usage">
            <summary>
            Configuration controlling usage of a service.
            </summary>
        </member>
        <member name="F:Google.Api.Usage.RequirementsFieldNumber">
            <summary>Field number for the "requirements" field.</summary>
        </member>
        <member name="P:Google.Api.Usage.Requirements">
             <summary>
             Requirements that must be satisfied before a consumer project can use the
             service. Each requirement is of the form &lt;service.name>/&lt;requirement-id>;
             for example 'serviceusage.googleapis.com/billing-enabled'.
            
             For Google APIs, a Terms of Service requirement must be included here.
             Google Cloud APIs must include "serviceusage.googleapis.com/tos/cloud".
             Other Google APIs should include
             "serviceusage.googleapis.com/tos/universal". Additional ToS can be
             included based on the business needs.
             </summary>
        </member>
        <member name="F:Google.Api.Usage.RulesFieldNumber">
            <summary>Field number for the "rules" field.</summary>
        </member>
        <member name="P:Google.Api.Usage.Rules">
             <summary>
             A list of usage rules that apply to individual API methods.
            
             **NOTE:** All service configuration rules follow "last one wins" order.
             </summary>
        </member>
        <member name="F:Google.Api.Usage.ProducerNotificationChannelFieldNumber">
            <summary>Field number for the "producer_notification_channel" field.</summary>
        </member>
        <member name="P:Google.Api.Usage.ProducerNotificationChannel">
             <summary>
             The full resource name of a channel used for sending notifications to the
             service producer.
            
             Google Service Management currently only supports
             [Google Cloud Pub/Sub](https://cloud.google.com/pubsub) as a notification
             channel. To use Google Cloud Pub/Sub as the channel, this must be the name
             of a Cloud Pub/Sub topic that uses the Cloud Pub/Sub topic name format
             documented in https://cloud.google.com/pubsub/docs/overview.
             </summary>
        </member>
        <member name="T:Google.Api.UsageRule">
             <summary>
             Usage configuration rules for the service.
            
             NOTE: Under development.
            
             Use this rule to configure unregistered calls for the service. Unregistered
             calls are calls that do not contain consumer project identity.
             (Example: calls that do not contain an API key).
             By default, API methods do not allow unregistered calls, and each method call
             must be identified by a consumer project identity. Use this rule to
             allow/disallow unregistered calls.
            
             Example of an API that wants to allow unregistered calls for entire service.
            
                 usage:
                   rules:
                   - selector: "*"
                     allow_unregistered_calls: true
            
             Example of a method that wants to allow unregistered calls.
            
                 usage:
                   rules:
                   - selector: "google.example.library.v1.LibraryService.CreateBook"
                     allow_unregistered_calls: true
             </summary>
        </member>
        <member name="F:Google.Api.UsageRule.SelectorFieldNumber">
            <summary>Field number for the "selector" field.</summary>
        </member>
        <member name="P:Google.Api.UsageRule.Selector">
             <summary>
             Selects the methods to which this rule applies. Use '*' to indicate all
             methods in all APIs.
            
             Refer to [selector][google.api.DocumentationRule.selector] for syntax details.
             </summary>
        </member>
        <member name="F:Google.Api.UsageRule.AllowUnregisteredCallsFieldNumber">
            <summary>Field number for the "allow_unregistered_calls" field.</summary>
        </member>
        <member name="P:Google.Api.UsageRule.AllowUnregisteredCalls">
            <summary>
            If true, the selected method allows unregistered calls, e.g. calls
            that don't identify any user or application.
            </summary>
        </member>
        <member name="F:Google.Api.UsageRule.SkipServiceControlFieldNumber">
            <summary>Field number for the "skip_service_control" field.</summary>
        </member>
        <member name="P:Google.Api.UsageRule.SkipServiceControl">
            <summary>
            If true, the selected method should skip service control and the control
            plane features, such as quota and billing, will not be available.
            This flag is used by Google Cloud Endpoints to bypass checks for internal
            methods, such as service health check methods.
            </summary>
        </member>
        <member name="T:Google.Api.VisibilityReflection">
            <summary>Holder for reflection information generated from google/api/visibility.proto</summary>
        </member>
        <member name="P:Google.Api.VisibilityReflection.Descriptor">
            <summary>File descriptor for google/api/visibility.proto</summary>
        </member>
        <member name="T:Google.Api.VisibilityExtensions">
            <summary>Holder for extension identifiers generated from the top level of google/api/visibility.proto</summary>
        </member>
        <member name="F:Google.Api.VisibilityExtensions.EnumVisibility">
            <summary>
            See `VisibilityRule`.
            </summary>
        </member>
        <member name="F:Google.Api.VisibilityExtensions.ValueVisibility">
            <summary>
            See `VisibilityRule`.
            </summary>
        </member>
        <member name="F:Google.Api.VisibilityExtensions.FieldVisibility">
            <summary>
            See `VisibilityRule`.
            </summary>
        </member>
        <member name="F:Google.Api.VisibilityExtensions.MessageVisibility">
            <summary>
            See `VisibilityRule`.
            </summary>
        </member>
        <member name="F:Google.Api.VisibilityExtensions.MethodVisibility">
            <summary>
            See `VisibilityRule`.
            </summary>
        </member>
        <member name="F:Google.Api.VisibilityExtensions.ApiVisibility">
            <summary>
            See `VisibilityRule`.
            </summary>
        </member>
        <member name="T:Google.Api.Visibility">
             <summary>
             `Visibility` defines restrictions for the visibility of service
             elements.  Restrictions are specified using visibility labels
             (e.g., PREVIEW) that are elsewhere linked to users and projects.
            
             Users and projects can have access to more than one visibility label. The
             effective visibility for multiple labels is the union of each label's
             elements, plus any unrestricted elements.
            
             If an element and its parents have no restrictions, visibility is
             unconditionally granted.
            
             Example:
            
                 visibility:
                   rules:
                   - selector: google.calendar.Calendar.EnhancedSearch
                     restriction: PREVIEW
                   - selector: google.calendar.Calendar.Delegate
                     restriction: INTERNAL
            
             Here, all methods are publicly visible except for the restricted methods
             EnhancedSearch and Delegate.
             </summary>
        </member>
        <member name="F:Google.Api.Visibility.RulesFieldNumber">
            <summary>Field number for the "rules" field.</summary>
        </member>
        <member name="P:Google.Api.Visibility.Rules">
             <summary>
             A list of visibility rules that apply to individual API elements.
            
             **NOTE:** All service configuration rules follow "last one wins" order.
             </summary>
        </member>
        <member name="T:Google.Api.VisibilityRule">
            <summary>
            A visibility rule provides visibility configuration for an individual API
            element.
            </summary>
        </member>
        <member name="F:Google.Api.VisibilityRule.SelectorFieldNumber">
            <summary>Field number for the "selector" field.</summary>
        </member>
        <member name="P:Google.Api.VisibilityRule.Selector">
             <summary>
             Selects methods, messages, fields, enums, etc. to which this rule applies.
            
             Refer to [selector][google.api.DocumentationRule.selector] for syntax details.
             </summary>
        </member>
        <member name="F:Google.Api.VisibilityRule.RestrictionFieldNumber">
            <summary>Field number for the "restriction" field.</summary>
        </member>
        <member name="P:Google.Api.VisibilityRule.Restriction">
             <summary>
             A comma-separated list of visibility labels that apply to the `selector`.
             Any of the listed labels can be used to grant the visibility.
            
             If a rule has multiple labels, removing one of the labels but not all of
             them can break clients.
            
             Example:
            
                 visibility:
                   rules:
                   - selector: google.calendar.Calendar.EnhancedSearch
                     restriction: INTERNAL, PREVIEW
            
             Removing INTERNAL from this restriction will break clients that rely on
             this method and only had access to it through INTERNAL.
             </summary>
        </member>
        <member name="T:Google.Rpc.CodeReflection">
            <summary>Holder for reflection information generated from google/rpc/code.proto</summary>
        </member>
        <member name="P:Google.Rpc.CodeReflection.Descriptor">
            <summary>File descriptor for google/rpc/code.proto</summary>
        </member>
        <member name="T:Google.Rpc.Code">
             <summary>
             The canonical error codes for gRPC APIs.
            
             Sometimes multiple error codes may apply.  Services should return
             the most specific error code that applies.  For example, prefer
             `OUT_OF_RANGE` over `FAILED_PRECONDITION` if both codes apply.
             Similarly prefer `NOT_FOUND` or `ALREADY_EXISTS` over `FAILED_PRECONDITION`.
             </summary>
        </member>
        <member name="F:Google.Rpc.Code.Ok">
             <summary>
             Not an error; returned on success
            
             HTTP Mapping: 200 OK
             </summary>
        </member>
        <member name="F:Google.Rpc.Code.Cancelled">
             <summary>
             The operation was cancelled, typically by the caller.
            
             HTTP Mapping: 499 Client Closed Request
             </summary>
        </member>
        <member name="F:Google.Rpc.Code.Unknown">
             <summary>
             Unknown error.  For example, this error may be returned when
             a `Status` value received from another address space belongs to
             an error space that is not known in this address space.  Also
             errors raised by APIs that do not return enough error information
             may be converted to this error.
            
             HTTP Mapping: 500 Internal Server Error
             </summary>
        </member>
        <member name="F:Google.Rpc.Code.InvalidArgument">
             <summary>
             The client specified an invalid argument.  Note that this differs
             from `FAILED_PRECONDITION`.  `INVALID_ARGUMENT` indicates arguments
             that are problematic regardless of the state of the system
             (e.g., a malformed file name).
            
             HTTP Mapping: 400 Bad Request
             </summary>
        </member>
        <member name="F:Google.Rpc.Code.DeadlineExceeded">
             <summary>
             The deadline expired before the operation could complete. For operations
             that change the state of the system, this error may be returned
             even if the operation has completed successfully.  For example, a
             successful response from a server could have been delayed long
             enough for the deadline to expire.
            
             HTTP Mapping: 504 Gateway Timeout
             </summary>
        </member>
        <member name="F:Google.Rpc.Code.NotFound">
             <summary>
             Some requested entity (e.g., file or directory) was not found.
            
             Note to server developers: if a request is denied for an entire class
             of users, such as gradual feature rollout or undocumented whitelist,
             `NOT_FOUND` may be used. If a request is denied for some users within
             a class of users, such as user-based access control, `PERMISSION_DENIED`
             must be used.
            
             HTTP Mapping: 404 Not Found
             </summary>
        </member>
        <member name="F:Google.Rpc.Code.AlreadyExists">
             <summary>
             The entity that a client attempted to create (e.g., file or directory)
             already exists.
            
             HTTP Mapping: 409 Conflict
             </summary>
        </member>
        <member name="F:Google.Rpc.Code.PermissionDenied">
             <summary>
             The caller does not have permission to execute the specified
             operation. `PERMISSION_DENIED` must not be used for rejections
             caused by exhausting some resource (use `RESOURCE_EXHAUSTED`
             instead for those errors). `PERMISSION_DENIED` must not be
             used if the caller can not be identified (use `UNAUTHENTICATED`
             instead for those errors). This error code does not imply the
             request is valid or the requested entity exists or satisfies
             other pre-conditions.
            
             HTTP Mapping: 403 Forbidden
             </summary>
        </member>
        <member name="F:Google.Rpc.Code.Unauthenticated">
             <summary>
             The request does not have valid authentication credentials for the
             operation.
            
             HTTP Mapping: 401 Unauthorized
             </summary>
        </member>
        <member name="F:Google.Rpc.Code.ResourceExhausted">
             <summary>
             Some resource has been exhausted, perhaps a per-user quota, or
             perhaps the entire file system is out of space.
            
             HTTP Mapping: 429 Too Many Requests
             </summary>
        </member>
        <member name="F:Google.Rpc.Code.FailedPrecondition">
             <summary>
             The operation was rejected because the system is not in a state
             required for the operation's execution.  For example, the directory
             to be deleted is non-empty, an rmdir operation is applied to
             a non-directory, etc.
            
             Service implementors can use the following guidelines to decide
             between `FAILED_PRECONDITION`, `ABORTED`, and `UNAVAILABLE`:
              (a) Use `UNAVAILABLE` if the client can retry just the failing call.
              (b) Use `ABORTED` if the client should retry at a higher level
                  (e.g., when a client-specified test-and-set fails, indicating the
                  client should restart a read-modify-write sequence).
              (c) Use `FAILED_PRECONDITION` if the client should not retry until
                  the system state has been explicitly fixed.  E.g., if an "rmdir"
                  fails because the directory is non-empty, `FAILED_PRECONDITION`
                  should be returned since the client should not retry unless
                  the files are deleted from the directory.
            
             HTTP Mapping: 400 Bad Request
             </summary>
        </member>
        <member name="F:Google.Rpc.Code.Aborted">
             <summary>
             The operation was aborted, typically due to a concurrency issue such as
             a sequencer check failure or transaction abort.
            
             See the guidelines above for deciding between `FAILED_PRECONDITION`,
             `ABORTED`, and `UNAVAILABLE`.
            
             HTTP Mapping: 409 Conflict
             </summary>
        </member>
        <member name="F:Google.Rpc.Code.OutOfRange">
             <summary>
             The operation was attempted past the valid range.  E.g., seeking or
             reading past end-of-file.
            
             Unlike `INVALID_ARGUMENT`, this error indicates a problem that may
             be fixed if the system state changes. For example, a 32-bit file
             system will generate `INVALID_ARGUMENT` if asked to read at an
             offset that is not in the range [0,2^32-1], but it will generate
             `OUT_OF_RANGE` if asked to read from an offset past the current
             file size.
            
             There is a fair bit of overlap between `FAILED_PRECONDITION` and
             `OUT_OF_RANGE`.  We recommend using `OUT_OF_RANGE` (the more specific
             error) when it applies so that callers who are iterating through
             a space can easily look for an `OUT_OF_RANGE` error to detect when
             they are done.
            
             HTTP Mapping: 400 Bad Request
             </summary>
        </member>
        <member name="F:Google.Rpc.Code.Unimplemented">
             <summary>
             The operation is not implemented or is not supported/enabled in this
             service.
            
             HTTP Mapping: 501 Not Implemented
             </summary>
        </member>
        <member name="F:Google.Rpc.Code.Internal">
             <summary>
             Internal errors.  This means that some invariants expected by the
             underlying system have been broken.  This error code is reserved
             for serious errors.
            
             HTTP Mapping: 500 Internal Server Error
             </summary>
        </member>
        <member name="F:Google.Rpc.Code.Unavailable">
             <summary>
             The service is currently unavailable.  This is most likely a
             transient condition, which can be corrected by retrying with
             a backoff. Note that it is not always safe to retry
             non-idempotent operations.
            
             See the guidelines above for deciding between `FAILED_PRECONDITION`,
             `ABORTED`, and `UNAVAILABLE`.
            
             HTTP Mapping: 503 Service Unavailable
             </summary>
        </member>
        <member name="F:Google.Rpc.Code.DataLoss">
             <summary>
             Unrecoverable data loss or corruption.
            
             HTTP Mapping: 500 Internal Server Error
             </summary>
        </member>
        <member name="T:Google.Rpc.Context.AttributeContextReflection">
            <summary>Holder for reflection information generated from google/rpc/context/attribute_context.proto</summary>
        </member>
        <member name="P:Google.Rpc.Context.AttributeContextReflection.Descriptor">
            <summary>File descriptor for google/rpc/context/attribute_context.proto</summary>
        </member>
        <member name="T:Google.Rpc.Context.AttributeContext">
             <summary>
             This message defines the standard attribute vocabulary for Google APIs.
            
             An attribute is a piece of metadata that describes an activity on a network
             service. For example, the size of an HTTP request, or the status code of
             an HTTP response.
            
             Each attribute has a type and a name, which is logically defined as
             a proto message field in `AttributeContext`. The field type becomes the
             attribute type, and the field path becomes the attribute name. For example,
             the attribute `source.ip` maps to field `AttributeContext.source.ip`.
            
             This message definition is guaranteed not to have any wire breaking change.
             So you can use it directly for passing attributes across different systems.
            
             NOTE: Different system may generate different subset of attributes. Please
             verify the system specification before relying on an attribute generated
             a system.
             </summary>
        </member>
        <member name="F:Google.Rpc.Context.AttributeContext.OriginFieldNumber">
            <summary>Field number for the "origin" field.</summary>
        </member>
        <member name="P:Google.Rpc.Context.AttributeContext.Origin">
            <summary>
            The origin of a network activity. In a multi hop network activity,
            the origin represents the sender of the first hop. For the first hop,
            the `source` and the `origin` must have the same content.
            </summary>
        </member>
        <member name="F:Google.Rpc.Context.AttributeContext.SourceFieldNumber">
            <summary>Field number for the "source" field.</summary>
        </member>
        <member name="P:Google.Rpc.Context.AttributeContext.Source">
            <summary>
            The source of a network activity, such as starting a TCP connection.
            In a multi hop network activity, the source represents the sender of the
            last hop.
            </summary>
        </member>
        <member name="F:Google.Rpc.Context.AttributeContext.DestinationFieldNumber">
            <summary>Field number for the "destination" field.</summary>
        </member>
        <member name="P:Google.Rpc.Context.AttributeContext.Destination">
            <summary>
            The destination of a network activity, such as accepting a TCP connection.
            In a multi hop network activity, the destination represents the receiver of
            the last hop.
            </summary>
        </member>
        <member name="F:Google.Rpc.Context.AttributeContext.RequestFieldNumber">
            <summary>Field number for the "request" field.</summary>
        </member>
        <member name="P:Google.Rpc.Context.AttributeContext.Request">
            <summary>
            Represents a network request, such as an HTTP request.
            </summary>
        </member>
        <member name="F:Google.Rpc.Context.AttributeContext.ResponseFieldNumber">
            <summary>Field number for the "response" field.</summary>
        </member>
        <member name="P:Google.Rpc.Context.AttributeContext.Response">
            <summary>
            Represents a network response, such as an HTTP response.
            </summary>
        </member>
        <member name="F:Google.Rpc.Context.AttributeContext.ResourceFieldNumber">
            <summary>Field number for the "resource" field.</summary>
        </member>
        <member name="P:Google.Rpc.Context.AttributeContext.Resource">
            <summary>
            Represents a target resource that is involved with a network activity.
            If multiple resources are involved with an activity, this must be the
            primary one.
            </summary>
        </member>
        <member name="F:Google.Rpc.Context.AttributeContext.ApiFieldNumber">
            <summary>Field number for the "api" field.</summary>
        </member>
        <member name="P:Google.Rpc.Context.AttributeContext.Api">
            <summary>
            Represents an API operation that is involved to a network activity.
            </summary>
        </member>
        <member name="F:Google.Rpc.Context.AttributeContext.ExtensionsFieldNumber">
            <summary>Field number for the "extensions" field.</summary>
        </member>
        <member name="P:Google.Rpc.Context.AttributeContext.Extensions">
            <summary>
            Supports extensions for advanced use cases, such as logs and metrics.
            </summary>
        </member>
        <member name="T:Google.Rpc.Context.AttributeContext.Types">
            <summary>Container for nested types declared in the AttributeContext message type.</summary>
        </member>
        <member name="T:Google.Rpc.Context.AttributeContext.Types.Peer">
            <summary>
            This message defines attributes for a node that handles a network request.
            The node can be either a service or an application that sends, forwards,
            or receives the request. Service peers should fill in
            `principal` and `labels` as appropriate.
            </summary>
        </member>
        <member name="F:Google.Rpc.Context.AttributeContext.Types.Peer.IpFieldNumber">
            <summary>Field number for the "ip" field.</summary>
        </member>
        <member name="P:Google.Rpc.Context.AttributeContext.Types.Peer.Ip">
            <summary>
            The IP address of the peer.
            </summary>
        </member>
        <member name="F:Google.Rpc.Context.AttributeContext.Types.Peer.PortFieldNumber">
            <summary>Field number for the "port" field.</summary>
        </member>
        <member name="P:Google.Rpc.Context.AttributeContext.Types.Peer.Port">
            <summary>
            The network port of the peer.
            </summary>
        </member>
        <member name="F:Google.Rpc.Context.AttributeContext.Types.Peer.LabelsFieldNumber">
            <summary>Field number for the "labels" field.</summary>
        </member>
        <member name="P:Google.Rpc.Context.AttributeContext.Types.Peer.Labels">
            <summary>
            The labels associated with the peer.
            </summary>
        </member>
        <member name="F:Google.Rpc.Context.AttributeContext.Types.Peer.PrincipalFieldNumber">
            <summary>Field number for the "principal" field.</summary>
        </member>
        <member name="P:Google.Rpc.Context.AttributeContext.Types.Peer.Principal">
            <summary>
            The identity of this peer. Similar to `Request.auth.principal`, but
            relative to the peer instead of the request. For example, the
            idenity associated with a load balancer that forwared the request.
            </summary>
        </member>
        <member name="F:Google.Rpc.Context.AttributeContext.Types.Peer.RegionCodeFieldNumber">
            <summary>Field number for the "region_code" field.</summary>
        </member>
        <member name="P:Google.Rpc.Context.AttributeContext.Types.Peer.RegionCode">
            <summary>
            The CLDR country/region code associated with the above IP address.
            If the IP address is private, the `region_code` should reflect the
            physical location where this peer is running.
            </summary>
        </member>
        <member name="T:Google.Rpc.Context.AttributeContext.Types.Api">
            <summary>
            This message defines attributes associated with API operations, such as
            a network API request. The terminology is based on the conventions used
            by Google APIs, Istio, and OpenAPI.
            </summary>
        </member>
        <member name="F:Google.Rpc.Context.AttributeContext.Types.Api.ServiceFieldNumber">
            <summary>Field number for the "service" field.</summary>
        </member>
        <member name="P:Google.Rpc.Context.AttributeContext.Types.Api.Service">
            <summary>
            The API service name. It is a logical identifier for a networked API,
            such as "pubsub.googleapis.com". The naming syntax depends on the
            API management system being used for handling the request.
            </summary>
        </member>
        <member name="F:Google.Rpc.Context.AttributeContext.Types.Api.OperationFieldNumber">
            <summary>Field number for the "operation" field.</summary>
        </member>
        <member name="P:Google.Rpc.Context.AttributeContext.Types.Api.Operation">
            <summary>
            The API operation name. For gRPC requests, it is the fully qualified API
            method name, such as "google.pubsub.v1.Publisher.Publish". For OpenAPI
            requests, it is the `operationId`, such as "getPet".
            </summary>
        </member>
        <member name="F:Google.Rpc.Context.AttributeContext.Types.Api.ProtocolFieldNumber">
            <summary>Field number for the "protocol" field.</summary>
        </member>
        <member name="P:Google.Rpc.Context.AttributeContext.Types.Api.Protocol">
            <summary>
            The API protocol used for sending the request, such as "http", "https",
            "grpc", or "internal".
            </summary>
        </member>
        <member name="F:Google.Rpc.Context.AttributeContext.Types.Api.VersionFieldNumber">
            <summary>Field number for the "version" field.</summary>
        </member>
        <member name="P:Google.Rpc.Context.AttributeContext.Types.Api.Version">
            <summary>
            The API version associated with the API operation above, such as "v1" or
            "v1alpha1".
            </summary>
        </member>
        <member name="T:Google.Rpc.Context.AttributeContext.Types.Auth">
            <summary>
            This message defines request authentication attributes. Terminology is
            based on the JSON Web Token (JWT) standard, but the terms also
            correlate to concepts in other standards.
            </summary>
        </member>
        <member name="F:Google.Rpc.Context.AttributeContext.Types.Auth.PrincipalFieldNumber">
            <summary>Field number for the "principal" field.</summary>
        </member>
        <member name="P:Google.Rpc.Context.AttributeContext.Types.Auth.Principal">
            <summary>
            The authenticated principal. Reflects the issuer (`iss`) and subject
            (`sub`) claims within a JWT. The issuer and subject should be `/`
            delimited, with `/` percent-encoded within the subject fragment. For
            Google accounts, the principal format is:
            "https://accounts.google.com/{id}"
            </summary>
        </member>
        <member name="F:Google.Rpc.Context.AttributeContext.Types.Auth.AudiencesFieldNumber">
            <summary>Field number for the "audiences" field.</summary>
        </member>
        <member name="P:Google.Rpc.Context.AttributeContext.Types.Auth.Audiences">
             <summary>
             The intended audience(s) for this authentication information. Reflects
             the audience (`aud`) claim within a JWT. The audience
             value(s) depends on the `issuer`, but typically include one or more of
             the following pieces of information:
            
             *  The services intended to receive the credential. For example,
                ["https://pubsub.googleapis.com/", "https://storage.googleapis.com/"].
             *  A set of service-based scopes. For example,
                ["https://www.googleapis.com/auth/cloud-platform"].
             *  The client id of an app, such as the Firebase project id for JWTs
                from Firebase Auth.
            
             Consult the documentation for the credential issuer to determine the
             information provided.
             </summary>
        </member>
        <member name="F:Google.Rpc.Context.AttributeContext.Types.Auth.PresenterFieldNumber">
            <summary>Field number for the "presenter" field.</summary>
        </member>
        <member name="P:Google.Rpc.Context.AttributeContext.Types.Auth.Presenter">
            <summary>
            The authorized presenter of the credential. Reflects the optional
            Authorized Presenter (`azp`) claim within a JWT or the
            OAuth client id. For example, a Google Cloud Platform client id looks
            as follows: "************.apps.googleusercontent.com".
            </summary>
        </member>
        <member name="F:Google.Rpc.Context.AttributeContext.Types.Auth.ClaimsFieldNumber">
            <summary>Field number for the "claims" field.</summary>
        </member>
        <member name="P:Google.Rpc.Context.AttributeContext.Types.Auth.Claims">
             <summary>
             Structured claims presented with the credential. JWTs include
             `{key: value}` pairs for standard and private claims. The following
             is a subset of the standard required and optional claims that would
             typically be presented for a Google-based JWT:
            
                {'iss': 'accounts.google.com',
                 'sub': '113289723416554971153',
                 'aud': ['************', 'pubsub.googleapis.com'],
                 'azp': '************.apps.googleusercontent.com',
                 'email': '<EMAIL>',
                 'iat': **********,
                 'exp': **********}
            
             SAML assertions are similarly specified, but with an identity provider
             dependent structure.
             </summary>
        </member>
        <member name="F:Google.Rpc.Context.AttributeContext.Types.Auth.AccessLevelsFieldNumber">
            <summary>Field number for the "access_levels" field.</summary>
        </member>
        <member name="P:Google.Rpc.Context.AttributeContext.Types.Auth.AccessLevels">
             <summary>
             A list of access level resource names that allow resources to be
             accessed by authenticated requester. It is part of Secure GCP processing
             for the incoming request. An access level string has the format:
             "//{api_service_name}/accessPolicies/{policy_id}/accessLevels/{short_name}"
            
             Example:
             "//accesscontextmanager.googleapis.com/accessPolicies/MY_POLICY_ID/accessLevels/MY_LEVEL"
             </summary>
        </member>
        <member name="T:Google.Rpc.Context.AttributeContext.Types.Request">
            <summary>
            This message defines attributes for an HTTP request. If the actual
            request is not an HTTP request, the runtime system should try to map
            the actual request to an equivalent HTTP request.
            </summary>
        </member>
        <member name="F:Google.Rpc.Context.AttributeContext.Types.Request.IdFieldNumber">
            <summary>Field number for the "id" field.</summary>
        </member>
        <member name="P:Google.Rpc.Context.AttributeContext.Types.Request.Id">
            <summary>
            The unique ID for a request, which can be propagated to downstream
            systems. The ID should have low probability of collision
            within a single day for a specific service.
            </summary>
        </member>
        <member name="F:Google.Rpc.Context.AttributeContext.Types.Request.MethodFieldNumber">
            <summary>Field number for the "method" field.</summary>
        </member>
        <member name="P:Google.Rpc.Context.AttributeContext.Types.Request.Method">
            <summary>
            The HTTP request method, such as `GET`, `POST`.
            </summary>
        </member>
        <member name="F:Google.Rpc.Context.AttributeContext.Types.Request.HeadersFieldNumber">
            <summary>Field number for the "headers" field.</summary>
        </member>
        <member name="P:Google.Rpc.Context.AttributeContext.Types.Request.Headers">
            <summary>
            The HTTP request headers. If multiple headers share the same key, they
            must be merged according to the HTTP spec. All header keys must be
            lowercased, because HTTP header keys are case-insensitive.
            </summary>
        </member>
        <member name="F:Google.Rpc.Context.AttributeContext.Types.Request.PathFieldNumber">
            <summary>Field number for the "path" field.</summary>
        </member>
        <member name="P:Google.Rpc.Context.AttributeContext.Types.Request.Path">
            <summary>
            The HTTP URL path.
            </summary>
        </member>
        <member name="F:Google.Rpc.Context.AttributeContext.Types.Request.HostFieldNumber">
            <summary>Field number for the "host" field.</summary>
        </member>
        <member name="P:Google.Rpc.Context.AttributeContext.Types.Request.Host">
            <summary>
            The HTTP request `Host` header value.
            </summary>
        </member>
        <member name="F:Google.Rpc.Context.AttributeContext.Types.Request.SchemeFieldNumber">
            <summary>Field number for the "scheme" field.</summary>
        </member>
        <member name="P:Google.Rpc.Context.AttributeContext.Types.Request.Scheme">
            <summary>
            The HTTP URL scheme, such as `http` and `https`.
            </summary>
        </member>
        <member name="F:Google.Rpc.Context.AttributeContext.Types.Request.QueryFieldNumber">
            <summary>Field number for the "query" field.</summary>
        </member>
        <member name="P:Google.Rpc.Context.AttributeContext.Types.Request.Query">
            <summary>
            The HTTP URL query in the format of `name1=value1&amp;name2=value2`, as it
            appears in the first line of the HTTP request. No decoding is performed.
            </summary>
        </member>
        <member name="F:Google.Rpc.Context.AttributeContext.Types.Request.TimeFieldNumber">
            <summary>Field number for the "time" field.</summary>
        </member>
        <member name="P:Google.Rpc.Context.AttributeContext.Types.Request.Time">
            <summary>
            The timestamp when the `destination` service receives the last byte of
            the request.
            </summary>
        </member>
        <member name="F:Google.Rpc.Context.AttributeContext.Types.Request.SizeFieldNumber">
            <summary>Field number for the "size" field.</summary>
        </member>
        <member name="P:Google.Rpc.Context.AttributeContext.Types.Request.Size">
            <summary>
            The HTTP request size in bytes. If unknown, it must be -1.
            </summary>
        </member>
        <member name="F:Google.Rpc.Context.AttributeContext.Types.Request.ProtocolFieldNumber">
            <summary>Field number for the "protocol" field.</summary>
        </member>
        <member name="P:Google.Rpc.Context.AttributeContext.Types.Request.Protocol">
            <summary>
            The network protocol used with the request, such as "http/1.1",
            "spdy/3", "h2", "h2c", "webrtc", "tcp", "udp", "quic". See
            https://www.iana.org/assignments/tls-extensiontype-values/tls-extensiontype-values.xhtml#alpn-protocol-ids
            for details.
            </summary>
        </member>
        <member name="F:Google.Rpc.Context.AttributeContext.Types.Request.ReasonFieldNumber">
            <summary>Field number for the "reason" field.</summary>
        </member>
        <member name="P:Google.Rpc.Context.AttributeContext.Types.Request.Reason">
            <summary>
            A special parameter for request reason. It is used by security systems
            to associate auditing information with a request.
            </summary>
        </member>
        <member name="F:Google.Rpc.Context.AttributeContext.Types.Request.AuthFieldNumber">
            <summary>Field number for the "auth" field.</summary>
        </member>
        <member name="P:Google.Rpc.Context.AttributeContext.Types.Request.Auth">
            <summary>
            The request authentication. May be absent for unauthenticated requests.
            Derived from the HTTP request `Authorization` header or equivalent.
            </summary>
        </member>
        <member name="T:Google.Rpc.Context.AttributeContext.Types.Response">
            <summary>
            This message defines attributes for a typical network response. It
            generally models semantics of an HTTP response.
            </summary>
        </member>
        <member name="F:Google.Rpc.Context.AttributeContext.Types.Response.CodeFieldNumber">
            <summary>Field number for the "code" field.</summary>
        </member>
        <member name="P:Google.Rpc.Context.AttributeContext.Types.Response.Code">
            <summary>
            The HTTP response status code, such as `200` and `404`.
            </summary>
        </member>
        <member name="F:Google.Rpc.Context.AttributeContext.Types.Response.SizeFieldNumber">
            <summary>Field number for the "size" field.</summary>
        </member>
        <member name="P:Google.Rpc.Context.AttributeContext.Types.Response.Size">
            <summary>
            The HTTP response size in bytes. If unknown, it must be -1.
            </summary>
        </member>
        <member name="F:Google.Rpc.Context.AttributeContext.Types.Response.HeadersFieldNumber">
            <summary>Field number for the "headers" field.</summary>
        </member>
        <member name="P:Google.Rpc.Context.AttributeContext.Types.Response.Headers">
            <summary>
            The HTTP response headers. If multiple headers share the same key, they
            must be merged according to HTTP spec. All header keys must be
            lowercased, because HTTP header keys are case-insensitive.
            </summary>
        </member>
        <member name="F:Google.Rpc.Context.AttributeContext.Types.Response.TimeFieldNumber">
            <summary>Field number for the "time" field.</summary>
        </member>
        <member name="P:Google.Rpc.Context.AttributeContext.Types.Response.Time">
            <summary>
            The timestamp when the `destination` service sends the last byte of
            the response.
            </summary>
        </member>
        <member name="F:Google.Rpc.Context.AttributeContext.Types.Response.BackendLatencyFieldNumber">
            <summary>Field number for the "backend_latency" field.</summary>
        </member>
        <member name="P:Google.Rpc.Context.AttributeContext.Types.Response.BackendLatency">
            <summary>
            The length of time it takes the backend service to fully respond to a
            request. Measured from when the destination service starts to send the
            request to the backend until when the destination service receives the
            complete response from the backend.
            </summary>
        </member>
        <member name="T:Google.Rpc.Context.AttributeContext.Types.Resource">
            <summary>
            This message defines core attributes for a resource. A resource is an
            addressable (named) entity provided by the destination service. For
            example, a file stored on a network storage service.
            </summary>
        </member>
        <member name="F:Google.Rpc.Context.AttributeContext.Types.Resource.ServiceFieldNumber">
            <summary>Field number for the "service" field.</summary>
        </member>
        <member name="P:Google.Rpc.Context.AttributeContext.Types.Resource.Service">
            <summary>
            The name of the service that this resource belongs to, such as
            `pubsub.googleapis.com`. The service may be different from the DNS
            hostname that actually serves the request.
            </summary>
        </member>
        <member name="F:Google.Rpc.Context.AttributeContext.Types.Resource.NameFieldNumber">
            <summary>Field number for the "name" field.</summary>
        </member>
        <member name="P:Google.Rpc.Context.AttributeContext.Types.Resource.Name">
             <summary>
             The stable identifier (name) of a resource on the `service`. A resource
             can be logically identified as "//{resource.service}/{resource.name}".
             The differences between a resource name and a URI are:
            
             *   Resource name is a logical identifier, independent of network
                 protocol and API version. For example,
                 `//pubsub.googleapis.com/projects/123/topics/news-feed`.
             *   URI often includes protocol and version information, so it can
                 be used directly by applications. For example,
                 `https://pubsub.googleapis.com/v1/projects/123/topics/news-feed`.
            
             See https://cloud.google.com/apis/design/resource_names for details.
             </summary>
        </member>
        <member name="F:Google.Rpc.Context.AttributeContext.Types.Resource.TypeFieldNumber">
            <summary>Field number for the "type" field.</summary>
        </member>
        <member name="P:Google.Rpc.Context.AttributeContext.Types.Resource.Type">
             <summary>
             The type of the resource. The syntax is platform-specific because
             different platforms define their resources differently.
            
             For Google APIs, the type format must be "{service}/{kind}".
             </summary>
        </member>
        <member name="F:Google.Rpc.Context.AttributeContext.Types.Resource.LabelsFieldNumber">
            <summary>Field number for the "labels" field.</summary>
        </member>
        <member name="P:Google.Rpc.Context.AttributeContext.Types.Resource.Labels">
            <summary>
            The labels or tags on the resource, such as AWS resource tags and
            Kubernetes resource labels.
            </summary>
        </member>
        <member name="F:Google.Rpc.Context.AttributeContext.Types.Resource.UidFieldNumber">
            <summary>Field number for the "uid" field.</summary>
        </member>
        <member name="P:Google.Rpc.Context.AttributeContext.Types.Resource.Uid">
            <summary>
            The unique identifier of the resource. UID is unique in the time
            and space for this resource within the scope of the service. It is
            typically generated by the server on successful creation of a resource
            and must not be changed. UID is used to uniquely identify resources
            with resource name reuses. This should be a UUID4.
            </summary>
        </member>
        <member name="F:Google.Rpc.Context.AttributeContext.Types.Resource.AnnotationsFieldNumber">
            <summary>Field number for the "annotations" field.</summary>
        </member>
        <member name="P:Google.Rpc.Context.AttributeContext.Types.Resource.Annotations">
             <summary>
             Annotations is an unstructured key-value map stored with a resource that
             may be set by external tools to store and retrieve arbitrary metadata.
             They are not queryable and should be preserved when modifying objects.
            
             More info: https://kubernetes.io/docs/user-guide/annotations
             </summary>
        </member>
        <member name="F:Google.Rpc.Context.AttributeContext.Types.Resource.DisplayNameFieldNumber">
            <summary>Field number for the "display_name" field.</summary>
        </member>
        <member name="P:Google.Rpc.Context.AttributeContext.Types.Resource.DisplayName">
            <summary>
            Mutable. The display name set by clients. Must be &lt;= 63 characters.
            </summary>
        </member>
        <member name="F:Google.Rpc.Context.AttributeContext.Types.Resource.CreateTimeFieldNumber">
            <summary>Field number for the "create_time" field.</summary>
        </member>
        <member name="P:Google.Rpc.Context.AttributeContext.Types.Resource.CreateTime">
            <summary>
            Output only. The timestamp when the resource was created. This may
            be either the time creation was initiated or when it was completed.
            </summary>
        </member>
        <member name="F:Google.Rpc.Context.AttributeContext.Types.Resource.UpdateTimeFieldNumber">
            <summary>Field number for the "update_time" field.</summary>
        </member>
        <member name="P:Google.Rpc.Context.AttributeContext.Types.Resource.UpdateTime">
            <summary>
            Output only. The timestamp when the resource was last updated. Any
            change to the resource made by users must refresh this value.
            Changes to a resource made by the service should refresh this value.
            </summary>
        </member>
        <member name="F:Google.Rpc.Context.AttributeContext.Types.Resource.DeleteTimeFieldNumber">
            <summary>Field number for the "delete_time" field.</summary>
        </member>
        <member name="P:Google.Rpc.Context.AttributeContext.Types.Resource.DeleteTime">
            <summary>
            Output only. The timestamp when the resource was deleted.
            If the resource is not deleted, this must be empty.
            </summary>
        </member>
        <member name="F:Google.Rpc.Context.AttributeContext.Types.Resource.EtagFieldNumber">
            <summary>Field number for the "etag" field.</summary>
        </member>
        <member name="P:Google.Rpc.Context.AttributeContext.Types.Resource.Etag">
            <summary>
            Output only. An opaque value that uniquely identifies a version or
            generation of a resource. It can be used to confirm that the client
            and server agree on the ordering of a resource being written.
            </summary>
        </member>
        <member name="F:Google.Rpc.Context.AttributeContext.Types.Resource.LocationFieldNumber">
            <summary>Field number for the "location" field.</summary>
        </member>
        <member name="P:Google.Rpc.Context.AttributeContext.Types.Resource.Location">
             <summary>
             Immutable. The location of the resource. The location encoding is
             specific to the service provider, and new encoding may be introduced
             as the service evolves.
            
             For Google Cloud products, the encoding is what is used by Google Cloud
             APIs, such as `us-east1`, `aws-us-east-1`, and `azure-eastus2`. The
             semantics of `location` is identical to the
             `cloud.googleapis.com/location` label used by some Google Cloud APIs.
             </summary>
        </member>
        <member name="T:Google.Rpc.ErrorDetailsReflection">
            <summary>Holder for reflection information generated from google/rpc/error_details.proto</summary>
        </member>
        <member name="P:Google.Rpc.ErrorDetailsReflection.Descriptor">
            <summary>File descriptor for google/rpc/error_details.proto</summary>
        </member>
        <member name="T:Google.Rpc.RetryInfo">
             <summary>
             Describes when the clients can retry a failed request. Clients could ignore
             the recommendation here or retry when this information is missing from error
             responses.
            
             It's always recommended that clients should use exponential backoff when
             retrying.
            
             Clients should wait until `retry_delay` amount of time has passed since
             receiving the error response before retrying.  If retrying requests also
             fail, clients should use an exponential backoff scheme to gradually increase
             the delay between retries based on `retry_delay`, until either a maximum
             number of retries have been reached or a maximum retry delay cap has been
             reached.
             </summary>
        </member>
        <member name="F:Google.Rpc.RetryInfo.RetryDelayFieldNumber">
            <summary>Field number for the "retry_delay" field.</summary>
        </member>
        <member name="P:Google.Rpc.RetryInfo.RetryDelay">
            <summary>
            Clients should wait at least this long between retrying the same request.
            </summary>
        </member>
        <member name="T:Google.Rpc.DebugInfo">
            <summary>
            Describes additional debugging info.
            </summary>
        </member>
        <member name="F:Google.Rpc.DebugInfo.StackEntriesFieldNumber">
            <summary>Field number for the "stack_entries" field.</summary>
        </member>
        <member name="P:Google.Rpc.DebugInfo.StackEntries">
            <summary>
            The stack trace entries indicating where the error occurred.
            </summary>
        </member>
        <member name="F:Google.Rpc.DebugInfo.DetailFieldNumber">
            <summary>Field number for the "detail" field.</summary>
        </member>
        <member name="P:Google.Rpc.DebugInfo.Detail">
            <summary>
            Additional debugging information provided by the server.
            </summary>
        </member>
        <member name="T:Google.Rpc.QuotaFailure">
             <summary>
             Describes how a quota check failed.
            
             For example if a daily limit was exceeded for the calling project,
             a service could respond with a QuotaFailure detail containing the project
             id and the description of the quota limit that was exceeded.  If the
             calling project hasn't enabled the service in the developer console, then
             a service could respond with the project id and set `service_disabled`
             to true.
            
             Also see RetryInfo and Help types for other details about handling a
             quota failure.
             </summary>
        </member>
        <member name="F:Google.Rpc.QuotaFailure.ViolationsFieldNumber">
            <summary>Field number for the "violations" field.</summary>
        </member>
        <member name="P:Google.Rpc.QuotaFailure.Violations">
            <summary>
            Describes all quota violations.
            </summary>
        </member>
        <member name="T:Google.Rpc.QuotaFailure.Types">
            <summary>Container for nested types declared in the QuotaFailure message type.</summary>
        </member>
        <member name="T:Google.Rpc.QuotaFailure.Types.Violation">
            <summary>
            A message type used to describe a single quota violation.  For example, a
            daily quota or a custom quota that was exceeded.
            </summary>
        </member>
        <member name="F:Google.Rpc.QuotaFailure.Types.Violation.SubjectFieldNumber">
            <summary>Field number for the "subject" field.</summary>
        </member>
        <member name="P:Google.Rpc.QuotaFailure.Types.Violation.Subject">
            <summary>
            The subject on which the quota check failed.
            For example, "clientip:&lt;ip address of client>" or "project:&lt;Google
            developer project id>".
            </summary>
        </member>
        <member name="F:Google.Rpc.QuotaFailure.Types.Violation.DescriptionFieldNumber">
            <summary>Field number for the "description" field.</summary>
        </member>
        <member name="P:Google.Rpc.QuotaFailure.Types.Violation.Description">
             <summary>
             A description of how the quota check failed. Clients can use this
             description to find more about the quota configuration in the service's
             public documentation, or find the relevant quota limit to adjust through
             developer console.
            
             For example: "Service disabled" or "Daily Limit for read operations
             exceeded".
             </summary>
        </member>
        <member name="T:Google.Rpc.ErrorInfo">
             <summary>
             Describes the cause of the error with structured details.
            
             Example of an error when contacting the "pubsub.googleapis.com" API when it
             is not enabled:
            
                 { "reason": "API_DISABLED"
                   "domain": "googleapis.com"
                   "metadata": {
                     "resource": "projects/123",
                     "service": "pubsub.googleapis.com"
                   }
                 }
            
             This response indicates that the pubsub.googleapis.com API is not enabled.
            
             Example of an error that is returned when attempting to create a Spanner
             instance in a region that is out of stock:
            
                 { "reason": "STOCKOUT"
                   "domain": "spanner.googleapis.com",
                   "metadata": {
                     "availableRegions": "us-central1,us-east2"
                   }
                 }
             </summary>
        </member>
        <member name="F:Google.Rpc.ErrorInfo.ReasonFieldNumber">
            <summary>Field number for the "reason" field.</summary>
        </member>
        <member name="P:Google.Rpc.ErrorInfo.Reason">
            <summary>
            The reason of the error. This is a constant value that identifies the
            proximate cause of the error. Error reasons are unique within a particular
            domain of errors. This should be at most 63 characters and match
            /[A-Z0-9_]+/.
            </summary>
        </member>
        <member name="F:Google.Rpc.ErrorInfo.DomainFieldNumber">
            <summary>Field number for the "domain" field.</summary>
        </member>
        <member name="P:Google.Rpc.ErrorInfo.Domain">
            <summary>
            The logical grouping to which the "reason" belongs. The error domain
            is typically the registered service name of the tool or product that
            generates the error. Example: "pubsub.googleapis.com". If the error is
            generated by some common infrastructure, the error domain must be a
            globally unique value that identifies the infrastructure. For Google API
            infrastructure, the error domain is "googleapis.com".
            </summary>
        </member>
        <member name="F:Google.Rpc.ErrorInfo.MetadataFieldNumber">
            <summary>Field number for the "metadata" field.</summary>
        </member>
        <member name="P:Google.Rpc.ErrorInfo.Metadata">
             <summary>
             Additional structured details about this error.
            
             Keys should match /[a-zA-Z0-9-_]/ and be limited to 64 characters in
             length. When identifying the current value of an exceeded limit, the units
             should be contained in the key, not the value.  For example, rather than
             {"instanceLimit": "100/request"}, should be returned as,
             {"instanceLimitPerRequest": "100"}, if the client exceeds the number of
             instances that can be created in a single (batch) request.
             </summary>
        </member>
        <member name="T:Google.Rpc.PreconditionFailure">
             <summary>
             Describes what preconditions have failed.
            
             For example, if an RPC failed because it required the Terms of Service to be
             acknowledged, it could list the terms of service violation in the
             PreconditionFailure message.
             </summary>
        </member>
        <member name="F:Google.Rpc.PreconditionFailure.ViolationsFieldNumber">
            <summary>Field number for the "violations" field.</summary>
        </member>
        <member name="P:Google.Rpc.PreconditionFailure.Violations">
            <summary>
            Describes all precondition violations.
            </summary>
        </member>
        <member name="T:Google.Rpc.PreconditionFailure.Types">
            <summary>Container for nested types declared in the PreconditionFailure message type.</summary>
        </member>
        <member name="T:Google.Rpc.PreconditionFailure.Types.Violation">
            <summary>
            A message type used to describe a single precondition failure.
            </summary>
        </member>
        <member name="F:Google.Rpc.PreconditionFailure.Types.Violation.TypeFieldNumber">
            <summary>Field number for the "type" field.</summary>
        </member>
        <member name="P:Google.Rpc.PreconditionFailure.Types.Violation.Type">
            <summary>
            The type of PreconditionFailure. We recommend using a service-specific
            enum type to define the supported precondition violation subjects. For
            example, "TOS" for "Terms of Service violation".
            </summary>
        </member>
        <member name="F:Google.Rpc.PreconditionFailure.Types.Violation.SubjectFieldNumber">
            <summary>Field number for the "subject" field.</summary>
        </member>
        <member name="P:Google.Rpc.PreconditionFailure.Types.Violation.Subject">
            <summary>
            The subject, relative to the type, that failed.
            For example, "google.com/cloud" relative to the "TOS" type would indicate
            which terms of service is being referenced.
            </summary>
        </member>
        <member name="F:Google.Rpc.PreconditionFailure.Types.Violation.DescriptionFieldNumber">
            <summary>Field number for the "description" field.</summary>
        </member>
        <member name="P:Google.Rpc.PreconditionFailure.Types.Violation.Description">
             <summary>
             A description of how the precondition failed. Developers can use this
             description to understand how to fix the failure.
            
             For example: "Terms of service not accepted".
             </summary>
        </member>
        <member name="T:Google.Rpc.BadRequest">
            <summary>
            Describes violations in a client request. This error type focuses on the
            syntactic aspects of the request.
            </summary>
        </member>
        <member name="F:Google.Rpc.BadRequest.FieldViolationsFieldNumber">
            <summary>Field number for the "field_violations" field.</summary>
        </member>
        <member name="P:Google.Rpc.BadRequest.FieldViolations">
            <summary>
            Describes all violations in a client request.
            </summary>
        </member>
        <member name="T:Google.Rpc.BadRequest.Types">
            <summary>Container for nested types declared in the BadRequest message type.</summary>
        </member>
        <member name="T:Google.Rpc.BadRequest.Types.FieldViolation">
            <summary>
            A message type used to describe a single bad request field.
            </summary>
        </member>
        <member name="F:Google.Rpc.BadRequest.Types.FieldViolation.FieldFieldNumber">
            <summary>Field number for the "field" field.</summary>
        </member>
        <member name="P:Google.Rpc.BadRequest.Types.FieldViolation.Field">
            <summary>
            A path leading to a field in the request body. The value will be a
            sequence of dot-separated identifiers that identify a protocol buffer
            field. E.g., "field_violations.field" would identify this field.
            </summary>
        </member>
        <member name="F:Google.Rpc.BadRequest.Types.FieldViolation.DescriptionFieldNumber">
            <summary>Field number for the "description" field.</summary>
        </member>
        <member name="P:Google.Rpc.BadRequest.Types.FieldViolation.Description">
            <summary>
            A description of why the request element is bad.
            </summary>
        </member>
        <member name="T:Google.Rpc.RequestInfo">
            <summary>
            Contains metadata about the request that clients can attach when filing a bug
            or providing other forms of feedback.
            </summary>
        </member>
        <member name="F:Google.Rpc.RequestInfo.RequestIdFieldNumber">
            <summary>Field number for the "request_id" field.</summary>
        </member>
        <member name="P:Google.Rpc.RequestInfo.RequestId">
            <summary>
            An opaque string that should only be interpreted by the service generating
            it. For example, it can be used to identify requests in the service's logs.
            </summary>
        </member>
        <member name="F:Google.Rpc.RequestInfo.ServingDataFieldNumber">
            <summary>Field number for the "serving_data" field.</summary>
        </member>
        <member name="P:Google.Rpc.RequestInfo.ServingData">
            <summary>
            Any data that was used to serve this request. For example, an encrypted
            stack trace that can be sent back to the service provider for debugging.
            </summary>
        </member>
        <member name="T:Google.Rpc.ResourceInfo">
            <summary>
            Describes the resource that is being accessed.
            </summary>
        </member>
        <member name="F:Google.Rpc.ResourceInfo.ResourceTypeFieldNumber">
            <summary>Field number for the "resource_type" field.</summary>
        </member>
        <member name="P:Google.Rpc.ResourceInfo.ResourceType">
            <summary>
            A name for the type of resource being accessed, e.g. "sql table",
            "cloud storage bucket", "file", "Google calendar"; or the type URL
            of the resource: e.g. "type.googleapis.com/google.pubsub.v1.Topic".
            </summary>
        </member>
        <member name="F:Google.Rpc.ResourceInfo.ResourceNameFieldNumber">
            <summary>Field number for the "resource_name" field.</summary>
        </member>
        <member name="P:Google.Rpc.ResourceInfo.ResourceName">
            <summary>
            The name of the resource being accessed.  For example, a shared calendar
            name: "<EMAIL>", if the current
            error is [google.rpc.Code.PERMISSION_DENIED][google.rpc.Code.PERMISSION_DENIED].
            </summary>
        </member>
        <member name="F:Google.Rpc.ResourceInfo.OwnerFieldNumber">
            <summary>Field number for the "owner" field.</summary>
        </member>
        <member name="P:Google.Rpc.ResourceInfo.Owner">
            <summary>
            The owner of the resource (optional).
            For example, "user:&lt;owner email>" or "project:&lt;Google developer project
            id>".
            </summary>
        </member>
        <member name="F:Google.Rpc.ResourceInfo.DescriptionFieldNumber">
            <summary>Field number for the "description" field.</summary>
        </member>
        <member name="P:Google.Rpc.ResourceInfo.Description">
            <summary>
            Describes what error is encountered when accessing this resource.
            For example, updating a cloud project may require the `writer` permission
            on the developer console project.
            </summary>
        </member>
        <member name="T:Google.Rpc.Help">
             <summary>
             Provides links to documentation or for performing an out of band action.
            
             For example, if a quota check failed with an error indicating the calling
             project hasn't enabled the accessed service, this can contain a URL pointing
             directly to the right place in the developer console to flip the bit.
             </summary>
        </member>
        <member name="F:Google.Rpc.Help.LinksFieldNumber">
            <summary>Field number for the "links" field.</summary>
        </member>
        <member name="P:Google.Rpc.Help.Links">
            <summary>
            URL(s) pointing to additional information on handling the current error.
            </summary>
        </member>
        <member name="T:Google.Rpc.Help.Types">
            <summary>Container for nested types declared in the Help message type.</summary>
        </member>
        <member name="T:Google.Rpc.Help.Types.Link">
            <summary>
            Describes a URL link.
            </summary>
        </member>
        <member name="F:Google.Rpc.Help.Types.Link.DescriptionFieldNumber">
            <summary>Field number for the "description" field.</summary>
        </member>
        <member name="P:Google.Rpc.Help.Types.Link.Description">
            <summary>
            Describes what the link offers.
            </summary>
        </member>
        <member name="F:Google.Rpc.Help.Types.Link.UrlFieldNumber">
            <summary>Field number for the "url" field.</summary>
        </member>
        <member name="P:Google.Rpc.Help.Types.Link.Url">
            <summary>
            The URL of the link.
            </summary>
        </member>
        <member name="T:Google.Rpc.LocalizedMessage">
            <summary>
            Provides a localized error message that is safe to return to the user
            which can be attached to an RPC error.
            </summary>
        </member>
        <member name="F:Google.Rpc.LocalizedMessage.LocaleFieldNumber">
            <summary>Field number for the "locale" field.</summary>
        </member>
        <member name="P:Google.Rpc.LocalizedMessage.Locale">
            <summary>
            The locale used following the specification defined at
            http://www.rfc-editor.org/rfc/bcp/bcp47.txt.
            Examples are: "en-US", "fr-CH", "es-MX"
            </summary>
        </member>
        <member name="F:Google.Rpc.LocalizedMessage.MessageFieldNumber">
            <summary>Field number for the "message" field.</summary>
        </member>
        <member name="P:Google.Rpc.LocalizedMessage.Message">
            <summary>
            The localized error message in the above locale.
            </summary>
        </member>
        <member name="T:Google.Rpc.StatusReflection">
            <summary>Holder for reflection information generated from google/rpc/status.proto</summary>
        </member>
        <member name="P:Google.Rpc.StatusReflection.Descriptor">
            <summary>File descriptor for google/rpc/status.proto</summary>
        </member>
        <member name="T:Google.Rpc.Status">
             <summary>
             The `Status` type defines a logical error model that is suitable for
             different programming environments, including REST APIs and RPC APIs. It is
             used by [gRPC](https://github.com/grpc). Each `Status` message contains
             three pieces of data: error code, error message, and error details.
            
             You can find out more about this error model and how to work with it in the
             [API Design Guide](https://cloud.google.com/apis/design/errors).
             </summary>
        </member>
        <member name="F:Google.Rpc.Status.CodeFieldNumber">
            <summary>Field number for the "code" field.</summary>
        </member>
        <member name="P:Google.Rpc.Status.Code">
            <summary>
            The status code, which should be an enum value of [google.rpc.Code][google.rpc.Code].
            </summary>
        </member>
        <member name="F:Google.Rpc.Status.MessageFieldNumber">
            <summary>Field number for the "message" field.</summary>
        </member>
        <member name="P:Google.Rpc.Status.Message">
            <summary>
            A developer-facing error message, which should be in English. Any
            user-facing error message should be localized and sent in the
            [google.rpc.Status.details][google.rpc.Status.details] field, or localized by the client.
            </summary>
        </member>
        <member name="F:Google.Rpc.Status.DetailsFieldNumber">
            <summary>Field number for the "details" field.</summary>
        </member>
        <member name="P:Google.Rpc.Status.Details">
            <summary>
            A list of messages that carry the error details.  There is a common set of
            message types for APIs to use.
            </summary>
        </member>
        <member name="T:Google.Type.CalendarPeriodReflection">
            <summary>Holder for reflection information generated from google/type/calendar_period.proto</summary>
        </member>
        <member name="P:Google.Type.CalendarPeriodReflection.Descriptor">
            <summary>File descriptor for google/type/calendar_period.proto</summary>
        </member>
        <member name="T:Google.Type.CalendarPeriod">
            <summary>
            A `CalendarPeriod` represents the abstract concept of a time period that has
            a canonical start. Grammatically, "the start of the current
            `CalendarPeriod`." All calendar times begin at midnight UTC.
            </summary>
        </member>
        <member name="F:Google.Type.CalendarPeriod.Unspecified">
            <summary>
            Undefined period, raises an error.
            </summary>
        </member>
        <member name="F:Google.Type.CalendarPeriod.Day">
            <summary>
            A day.
            </summary>
        </member>
        <member name="F:Google.Type.CalendarPeriod.Week">
            <summary>
            A week. Weeks begin on Monday, following
            [ISO 8601](https://en.wikipedia.org/wiki/ISO_week_date).
            </summary>
        </member>
        <member name="F:Google.Type.CalendarPeriod.Fortnight">
            <summary>
            A fortnight. The first calendar fortnight of the year begins at the start
            of week 1 according to
            [ISO 8601](https://en.wikipedia.org/wiki/ISO_week_date).
            </summary>
        </member>
        <member name="F:Google.Type.CalendarPeriod.Month">
            <summary>
            A month.
            </summary>
        </member>
        <member name="F:Google.Type.CalendarPeriod.Quarter">
            <summary>
            A quarter. Quarters start on dates 1-Jan, 1-Apr, 1-Jul, and 1-Oct of each
            year.
            </summary>
        </member>
        <member name="F:Google.Type.CalendarPeriod.Half">
            <summary>
            A half-year. Half-years start on dates 1-Jan and 1-Jul.
            </summary>
        </member>
        <member name="F:Google.Type.CalendarPeriod.Year">
            <summary>
            A year.
            </summary>
        </member>
        <member name="T:Google.Type.ColorReflection">
            <summary>Holder for reflection information generated from google/type/color.proto</summary>
        </member>
        <member name="P:Google.Type.ColorReflection.Descriptor">
            <summary>File descriptor for google/type/color.proto</summary>
        </member>
        <member name="T:Google.Type.Color">
             <summary>
             Represents a color in the RGBA color space. This representation is designed
             for simplicity of conversion to/from color representations in various
             languages over compactness. For example, the fields of this representation
             can be trivially provided to the constructor of `java.awt.Color` in Java; it
             can also be trivially provided to UIColor's `+colorWithRed:green:blue:alpha`
             method in iOS; and, with just a little work, it can be easily formatted into
             a CSS `rgba()` string in JavaScript.
            
             This reference page doesn't carry information about the absolute color
             space
             that should be used to interpret the RGB value (e.g. sRGB, Adobe RGB,
             DCI-P3, BT.2020, etc.). By default, applications should assume the sRGB color
             space.
            
             When color equality needs to be decided, implementations, unless
             documented otherwise, treat two colors as equal if all their red,
             green, blue, and alpha values each differ by at most 1e-5.
            
             Example (Java):
            
                  import com.google.type.Color;
            
                  // ...
                  public static java.awt.Color fromProto(Color protocolor) {
                    float alpha = protocolor.hasAlpha()
                        ? protocolor.getAlpha().getValue()
                        : 1.0;
            
                    return new java.awt.Color(
                        protocolor.getRed(),
                        protocolor.getGreen(),
                        protocolor.getBlue(),
                        alpha);
                  }
            
                  public static Color toProto(java.awt.Color color) {
                    float red = (float) color.getRed();
                    float green = (float) color.getGreen();
                    float blue = (float) color.getBlue();
                    float denominator = 255.0;
                    Color.Builder resultBuilder =
                        Color
                            .newBuilder()
                            .setRed(red / denominator)
                            .setGreen(green / denominator)
                            .setBlue(blue / denominator);
                    int alpha = color.getAlpha();
                    if (alpha != 255) {
                      result.setAlpha(
                          FloatValue
                              .newBuilder()
                              .setValue(((float) alpha) / denominator)
                              .build());
                    }
                    return resultBuilder.build();
                  }
                  // ...
            
             Example (iOS / Obj-C):
            
                  // ...
                  static UIColor* fromProto(Color* protocolor) {
                     float red = [protocolor red];
                     float green = [protocolor green];
                     float blue = [protocolor blue];
                     FloatValue* alpha_wrapper = [protocolor alpha];
                     float alpha = 1.0;
                     if (alpha_wrapper != nil) {
                       alpha = [alpha_wrapper value];
                     }
                     return [UIColor colorWithRed:red green:green blue:blue alpha:alpha];
                  }
            
                  static Color* toProto(UIColor* color) {
                      CGFloat red, green, blue, alpha;
                      if (![color getRed:&amp;red green:&amp;green blue:&amp;blue alpha:&amp;alpha]) {
                        return nil;
                      }
                      Color* result = [[Color alloc] init];
                      [result setRed:red];
                      [result setGreen:green];
                      [result setBlue:blue];
                      if (alpha &lt;= 0.9999) {
                        [result setAlpha:floatWrapperWithValue(alpha)];
                      }
                      [result autorelease];
                      return result;
                 }
                 // ...
            
              Example (JavaScript):
            
                 // ...
            
                 var protoToCssColor = function(rgb_color) {
                    var redFrac = rgb_color.red || 0.0;
                    var greenFrac = rgb_color.green || 0.0;
                    var blueFrac = rgb_color.blue || 0.0;
                    var red = Math.floor(redFrac * 255);
                    var green = Math.floor(greenFrac * 255);
                    var blue = Math.floor(blueFrac * 255);
            
                    if (!('alpha' in rgb_color)) {
                       return rgbToCssColor(red, green, blue);
                    }
            
                    var alphaFrac = rgb_color.alpha.value || 0.0;
                    var rgbParams = [red, green, blue].join(',');
                    return ['rgba(', rgbParams, ',', alphaFrac, ')'].join('');
                 };
            
                 var rgbToCssColor = function(red, green, blue) {
                   var rgbNumber = new Number((red &lt;&lt; 16) | (green &lt;&lt; 8) | blue);
                   var hexString = rgbNumber.toString(16);
                   var missingZeros = 6 - hexString.length;
                   var resultBuilder = ['#'];
                   for (var i = 0; i &lt; missingZeros; i++) {
                      resultBuilder.push('0');
                   }
                   resultBuilder.push(hexString);
                   return resultBuilder.join('');
                 };
            
                 // ...
             </summary>
        </member>
        <member name="F:Google.Type.Color.RedFieldNumber">
            <summary>Field number for the "red" field.</summary>
        </member>
        <member name="P:Google.Type.Color.Red">
            <summary>
            The amount of red in the color as a value in the interval [0, 1].
            </summary>
        </member>
        <member name="F:Google.Type.Color.GreenFieldNumber">
            <summary>Field number for the "green" field.</summary>
        </member>
        <member name="P:Google.Type.Color.Green">
            <summary>
            The amount of green in the color as a value in the interval [0, 1].
            </summary>
        </member>
        <member name="F:Google.Type.Color.BlueFieldNumber">
            <summary>Field number for the "blue" field.</summary>
        </member>
        <member name="P:Google.Type.Color.Blue">
            <summary>
            The amount of blue in the color as a value in the interval [0, 1].
            </summary>
        </member>
        <member name="F:Google.Type.Color.AlphaFieldNumber">
            <summary>Field number for the "alpha" field.</summary>
        </member>
        <member name="P:Google.Type.Color.Alpha">
             <summary>
             The fraction of this color that should be applied to the pixel. That is,
             the final pixel color is defined by the equation:
            
               `pixel color = alpha * (this color) + (1.0 - alpha) * (background color)`
            
             This means that a value of 1.0 corresponds to a solid color, whereas
             a value of 0.0 corresponds to a completely transparent color. This
             uses a wrapper message rather than a simple float scalar so that it is
             possible to distinguish between a default value and the value being unset.
             If omitted, this color object is rendered as a solid color
             (as if the alpha value had been explicitly given a value of 1.0).
             </summary>
        </member>
        <member name="T:Google.Type.DateReflection">
            <summary>Holder for reflection information generated from google/type/date.proto</summary>
        </member>
        <member name="P:Google.Type.DateReflection.Descriptor">
            <summary>File descriptor for google/type/date.proto</summary>
        </member>
        <member name="T:Google.Type.Date">
             <summary>
             Represents a whole or partial calendar date, such as a birthday. The time of
             day and time zone are either specified elsewhere or are insignificant. The
             date is relative to the Gregorian Calendar. This can represent one of the
             following:
            
             * A full date, with non-zero year, month, and day values
             * A month and day value, with a zero year, such as an anniversary
             * A year on its own, with zero month and day values
             * A year and month value, with a zero day, such as a credit card expiration
             date
            
             Related types are [google.type.TimeOfDay][google.type.TimeOfDay] and
             `google.protobuf.Timestamp`.
             </summary>
        </member>
        <member name="F:Google.Type.Date.YearFieldNumber">
            <summary>Field number for the "year" field.</summary>
        </member>
        <member name="P:Google.Type.Date.Year">
            <summary>
            Year of the date. Must be from 1 to 9999, or 0 to specify a date without
            a year.
            </summary>
        </member>
        <member name="F:Google.Type.Date.MonthFieldNumber">
            <summary>Field number for the "month" field.</summary>
        </member>
        <member name="P:Google.Type.Date.Month">
            <summary>
            Month of a year. Must be from 1 to 12, or 0 to specify a year without a
            month and day.
            </summary>
        </member>
        <member name="F:Google.Type.Date.DayFieldNumber">
            <summary>Field number for the "day" field.</summary>
        </member>
        <member name="P:Google.Type.Date.Day">
            <summary>
            Day of a month. Must be from 1 to 31 and valid for the year and month, or 0
            to specify a year by itself or a year and month where the day isn't
            significant.
            </summary>
        </member>
        <member name="M:Google.Type.Date.ToDateTime">
            <summary>
            Converts <see cref="T:Google.Type.Date"/> to <see cref="T:System.DateTime"/>.
            </summary>
            <returns>The converted <see cref="T:Google.Type.DateTime"/> with time at midnight and <see cref="P:System.DateTime.Kind"/> of <see cref="F:System.DateTimeKind.Unspecified"/>.</returns>
            <exception cref="T:System.InvalidOperationException">Thrown when <see cref="P:Google.Type.Date.Year"/>, <see cref="P:Google.Type.Date.Month"/>, and/or <see cref="P:Google.Type.Date.Day"/> are not within the valid range.</exception>
        </member>
        <member name="M:Google.Type.Date.ToDateTimeOffset">
            <summary>
            Converts <see cref="T:Google.Type.Date"/> to <see cref="T:System.DateTimeOffset"/>.
            </summary>
            <returns>The converted <see cref="T:System.DateTimeOffset"/> with time at midnight, <see cref="P:System.DateTime.Kind"/> of <see cref="F:System.DateTimeKind.Unspecified"/>, and an <see cref="P:System.DateTimeOffset.Offset"/> of <see cref="F:System.TimeSpan.Zero"/>.</returns>
            <exception cref="T:System.InvalidOperationException">Thrown when <see cref="P:Google.Type.Date.Year"/>, <see cref="P:Google.Type.Date.Month"/>, and/or <see cref="P:Google.Type.Date.Day"/> are not within the valid range.</exception>        
        </member>
        <member name="M:Google.Type.Date.FromDateTime(System.DateTime)">
            <summary>
            Creates a <see cref="T:Google.Type.Date"/> instance from the <see cref="P:System.DateTime.Date"/> part of <see cref="T:Google.Type.DateTime"/>.
            </summary>     
            <param name="dateTime">The <see cref="T:System.DateTime"/> value being converted.</param>
            <returns>The created <see cref="T:Google.Type.Date"/>.</returns>
        </member>
        <member name="M:Google.Type.Date.FromDateTimeOffset(System.DateTimeOffset)">
            <summary>
            Creates a <see cref="T:Google.Type.Date"/> instance from the <see cref="P:System.DateTimeOffset.Date"/> part of <see cref="T:System.DateTimeOffset"/>.
            </summary>  
            <param name="dateTimeOffset">The <see cref="T:System.DateTimeOffset"/> value being converted.</param>
            <returns>The created <see cref="T:Google.Type.Date"/>.</returns>
        </member>
        <member name="T:Google.Type.DateExtensions">
            <summary>
            Extension methods built for <see cref="T:Google.Type.Date"/>.
            </summary>
        </member>
        <member name="M:Google.Type.DateExtensions.ToDate(System.DateTime)">
            <summary>
            Converts the <see cref="P:System.DateTime.Date"/> part of <see cref="T:System.DateTime"/> to <see cref="T:Google.Type.Date"/>.
            </summary>
            <param name="dateTime">The <see cref="T:System.DateTime"/> instance being converted.</param>
            <returns>The <see cref="T:Google.Type.Date"/>.</returns>
        </member>
        <member name="M:Google.Type.DateExtensions.ToDate(System.DateTimeOffset)">
            <summary>
            Converts the <see cref="P:System.DateTimeOffset.Date"/> part of <see cref="T:System.DateTimeOffset"/> to <see cref="T:Google.Type.Date"/>.
            </summary>
            <param name="dateTimeOffset">The <see cref="T:System.DateTimeOffset"/> instance being converted.</param>
            <returns>The converted <see cref="T:Google.Type.Date"/>.</returns>
        </member>
        <member name="T:Google.Type.DatetimeReflection">
            <summary>Holder for reflection information generated from google/type/datetime.proto</summary>
        </member>
        <member name="P:Google.Type.DatetimeReflection.Descriptor">
            <summary>File descriptor for google/type/datetime.proto</summary>
        </member>
        <member name="T:Google.Type.DateTime">
             <summary>
             Represents civil time (or occasionally physical time).
            
             This type can represent a civil time in one of a few possible ways:
            
              * When utc_offset is set and time_zone is unset: a civil time on a calendar
                day with a particular offset from UTC.
              * When time_zone is set and utc_offset is unset: a civil time on a calendar
                day in a particular time zone.
              * When neither time_zone nor utc_offset is set: a civil time on a calendar
                day in local time.
            
             The date is relative to the Proleptic Gregorian Calendar.
            
             If year is 0, the DateTime is considered not to have a specific year. month
             and day must have valid, non-zero values.
            
             This type may also be used to represent a physical time if all the date and
             time fields are set and either case of the `time_offset` oneof is set.
             Consider using `Timestamp` message for physical time instead. If your use
             case also would like to store the user's timezone, that can be done in
             another field.
            
             This type is more flexible than some applications may want. Make sure to
             document and validate your application's limitations.
             </summary>
        </member>
        <member name="F:Google.Type.DateTime.YearFieldNumber">
            <summary>Field number for the "year" field.</summary>
        </member>
        <member name="P:Google.Type.DateTime.Year">
            <summary>
            Optional. Year of date. Must be from 1 to 9999, or 0 if specifying a
            datetime without a year.
            </summary>
        </member>
        <member name="F:Google.Type.DateTime.MonthFieldNumber">
            <summary>Field number for the "month" field.</summary>
        </member>
        <member name="P:Google.Type.DateTime.Month">
            <summary>
            Required. Month of year. Must be from 1 to 12.
            </summary>
        </member>
        <member name="F:Google.Type.DateTime.DayFieldNumber">
            <summary>Field number for the "day" field.</summary>
        </member>
        <member name="P:Google.Type.DateTime.Day">
            <summary>
            Required. Day of month. Must be from 1 to 31 and valid for the year and
            month.
            </summary>
        </member>
        <member name="F:Google.Type.DateTime.HoursFieldNumber">
            <summary>Field number for the "hours" field.</summary>
        </member>
        <member name="P:Google.Type.DateTime.Hours">
            <summary>
            Required. Hours of day in 24 hour format. Should be from 0 to 23. An API
            may choose to allow the value "24:00:00" for scenarios like business
            closing time.
            </summary>
        </member>
        <member name="F:Google.Type.DateTime.MinutesFieldNumber">
            <summary>Field number for the "minutes" field.</summary>
        </member>
        <member name="P:Google.Type.DateTime.Minutes">
            <summary>
            Required. Minutes of hour of day. Must be from 0 to 59.
            </summary>
        </member>
        <member name="F:Google.Type.DateTime.SecondsFieldNumber">
            <summary>Field number for the "seconds" field.</summary>
        </member>
        <member name="P:Google.Type.DateTime.Seconds">
            <summary>
            Required. Seconds of minutes of the time. Must normally be from 0 to 59. An
            API may allow the value 60 if it allows leap-seconds.
            </summary>
        </member>
        <member name="F:Google.Type.DateTime.NanosFieldNumber">
            <summary>Field number for the "nanos" field.</summary>
        </member>
        <member name="P:Google.Type.DateTime.Nanos">
            <summary>
            Required. Fractions of seconds in nanoseconds. Must be from 0 to
            999,999,999.
            </summary>
        </member>
        <member name="F:Google.Type.DateTime.UtcOffsetFieldNumber">
            <summary>Field number for the "utc_offset" field.</summary>
        </member>
        <member name="P:Google.Type.DateTime.UtcOffset">
            <summary>
            UTC offset. Must be whole seconds, between -18 hours and +18 hours.
            For example, a UTC offset of -4:00 would be represented as
            { seconds: -14400 }.
            </summary>
        </member>
        <member name="F:Google.Type.DateTime.TimeZoneFieldNumber">
            <summary>Field number for the "time_zone" field.</summary>
        </member>
        <member name="P:Google.Type.DateTime.TimeZone">
            <summary>
            Time zone.
            </summary>
        </member>
        <member name="T:Google.Type.DateTime.TimeOffsetOneofCase">
            <summary>Enum of possible cases for the "time_offset" oneof.</summary>
        </member>
        <member name="T:Google.Type.TimeZone">
            <summary>
            Represents a time zone from the
            [IANA Time Zone Database](https://www.iana.org/time-zones).
            </summary>
        </member>
        <member name="F:Google.Type.TimeZone.IdFieldNumber">
            <summary>Field number for the "id" field.</summary>
        </member>
        <member name="P:Google.Type.TimeZone.Id">
            <summary>
            IANA Time Zone Database time zone, e.g. "America/New_York".
            </summary>
        </member>
        <member name="F:Google.Type.TimeZone.VersionFieldNumber">
            <summary>Field number for the "version" field.</summary>
        </member>
        <member name="P:Google.Type.TimeZone.Version">
            <summary>
            Optional. IANA Time Zone Database version number, e.g. "2019a".
            </summary>
        </member>
        <member name="T:Google.Type.DayofweekReflection">
            <summary>Holder for reflection information generated from google/type/dayofweek.proto</summary>
        </member>
        <member name="P:Google.Type.DayofweekReflection.Descriptor">
            <summary>File descriptor for google/type/dayofweek.proto</summary>
        </member>
        <member name="T:Google.Type.DayOfWeek">
            <summary>
            Represents a day of the week.
            </summary>
        </member>
        <member name="F:Google.Type.DayOfWeek.Unspecified">
            <summary>
            The day of the week is unspecified.
            </summary>
        </member>
        <member name="F:Google.Type.DayOfWeek.Monday">
            <summary>
            Monday
            </summary>
        </member>
        <member name="F:Google.Type.DayOfWeek.Tuesday">
            <summary>
            Tuesday
            </summary>
        </member>
        <member name="F:Google.Type.DayOfWeek.Wednesday">
            <summary>
            Wednesday
            </summary>
        </member>
        <member name="F:Google.Type.DayOfWeek.Thursday">
            <summary>
            Thursday
            </summary>
        </member>
        <member name="F:Google.Type.DayOfWeek.Friday">
            <summary>
            Friday
            </summary>
        </member>
        <member name="F:Google.Type.DayOfWeek.Saturday">
            <summary>
            Saturday
            </summary>
        </member>
        <member name="F:Google.Type.DayOfWeek.Sunday">
            <summary>
            Sunday
            </summary>
        </member>
        <member name="T:Google.Type.Decimal">
             <summary>
             A representation of a decimal value, such as 2.5. Clients may convert values
             into language-native decimal formats, such as Java's [BigDecimal][] or
             Python's [decimal.Decimal][].
            
             [BigDecimal]:
             https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/math/BigDecimal.html
             [decimal.Decimal]: https://docs.python.org/3/library/decimal.html
             </summary>
        </member>
        <member name="M:Google.Type.Decimal.FromClrDecimal(System.Decimal)">
            <summary>
            Converts the given <see cref="T:System.Decimal"/> value to the protobuf <see cref="T:Google.Type.Decimal"/>
            representation. If the input value naturally contains trailing decimal zeroes (e.g. "1.00")
            these are preserved in the protobuf representation.
            </summary>
            <param name="value">The value to convert.</param>
            <returns>The protobuf representation.</returns>
        </member>
        <member name="M:Google.Type.Decimal.ToClrDecimal">
            <summary>
            Converts this protobuf <see cref="T:Google.Type.Decimal"/> value to a CLR <see cref="T:System.Decimal"/>
            value. If the value is within the range of <see cref="T:System.Decimal"/> but contains
            more than 29 significant digits, the returned value is truncated towards zero.
            </summary>
            <returns>The CLR representation of this value.</returns>
            <exception cref="T:System.FormatException">This protobuf value is invalid, either because
            <see cref="P:Google.Type.Decimal.Value"/> has not been set, or because it does not represent a valid decimal value.</exception>
            <exception cref="T:System.OverflowException">The protobuf value is too large or small to be represented
            by <see cref="T:System.Decimal"/>.</exception>
        </member>
        <member name="F:Google.Type.Decimal.ValueFieldNumber">
            <summary>Field number for the "value" field.</summary>
        </member>
        <member name="P:Google.Type.Decimal.Value">
             <summary>
             The decimal value, as a string.
            
             The string representation consists of an optional sign, `+` (`U+002B`)
             or `-` (`U+002D`), followed by a sequence of zero or more decimal digits
             ("the integer"), optionally followed by a fraction, optionally followed
             by an exponent.
            
             The fraction consists of a decimal point followed by zero or more decimal
             digits. The string must contain at least one digit in either the integer
             or the fraction. The number formed by the sign, the integer and the
             fraction is referred to as the significand.
            
             The exponent consists of the character `e` (`U+0065`) or `E` (`U+0045`)
             followed by one or more decimal digits.
            
             Services **should** normalize decimal values before storing them by:
            
               - Removing an explicitly-provided `+` sign (`+2.5` -> `2.5`).
               - Replacing a zero-length integer value with `0` (`.5` -> `0.5`).
               - Coercing the exponent character to lower-case (`2.5E8` -> `2.5e8`).
               - Removing an explicitly-provided zero exponent (`2.5e0` -> `2.5`).
            
             Services **may** perform additional normalization based on its own needs
             and the internal decimal implementation selected, such as shifting the
             decimal point and exponent value together (example: `2.5e-1` &lt;-> `0.25`).
             Additionally, services **may** preserve trailing zeroes in the fraction
             to indicate increased precision, but are not required to do so.
            
             Note that only the `.` character is supported to divide the integer
             and the fraction; `,` **should not** be supported regardless of locale.
             Additionally, thousand separators **should not** be supported. If a
             service does support them, values **must** be normalized.
            
             The ENBF grammar is:
            
                 DecimalString =
                   [Sign] Significand [Exponent];
            
                 Sign = '+' | '-';
            
                 Significand =
                   Digits ['.'] [Digits] | [Digits] '.' Digits;
            
                 Exponent = ('e' | 'E') [Sign] Digits;
            
                 Digits = { '0' | '1' | '2' | '3' | '4' | '5' | '6' | '7' | '8' | '9' };
            
             Services **should** clearly document the range of supported values, the
             maximum supported precision (total number of digits), and, if applicable,
             the scale (number of digits after the decimal point), as well as how it
             behaves when receiving out-of-bounds values.
            
             Services **may** choose to accept values passed as input even when the
             value has a higher precision or scale than the service supports, and
             **should** round the value to fit the supported scale. Alternatively, the
             service **may** error with `400 Bad Request` (`INVALID_ARGUMENT` in gRPC)
             if precision would be lost.
            
             Services **should** error with `400 Bad Request` (`INVALID_ARGUMENT` in
             gRPC) if the service receives a value outside of the supported range.
             </summary>
        </member>
        <member name="T:Google.Type.DecimalReflection">
            <summary>Holder for reflection information generated from google/type/decimal.proto</summary>
        </member>
        <member name="P:Google.Type.DecimalReflection.Descriptor">
            <summary>File descriptor for google/type/decimal.proto</summary>
        </member>
        <member name="T:Google.Type.ExprReflection">
            <summary>Holder for reflection information generated from google/type/expr.proto</summary>
        </member>
        <member name="P:Google.Type.ExprReflection.Descriptor">
            <summary>File descriptor for google/type/expr.proto</summary>
        </member>
        <member name="T:Google.Type.Expr">
             <summary>
             Represents a textual expression in the Common Expression Language (CEL)
             syntax. CEL is a C-like expression language. The syntax and semantics of CEL
             are documented at https://github.com/google/cel-spec.
            
             Example (Comparison):
            
                 title: "Summary size limit"
                 description: "Determines if a summary is less than 100 chars"
                 expression: "document.summary.size() &lt; 100"
            
             Example (Equality):
            
                 title: "Requestor is owner"
                 description: "Determines if requestor is the document owner"
                 expression: "document.owner == request.auth.claims.email"
            
             Example (Logic):
            
                 title: "Public documents"
                 description: "Determine whether the document should be publicly visible"
                 expression: "document.type != 'private' &amp;&amp; document.type != 'internal'"
            
             Example (Data Manipulation):
            
                 title: "Notification string"
                 description: "Create a notification string with a timestamp."
                 expression: "'New message received at ' + string(document.create_time)"
            
             The exact variables and functions that may be referenced within an expression
             are determined by the service that evaluates it. See the service
             documentation for additional information.
             </summary>
        </member>
        <member name="F:Google.Type.Expr.ExpressionFieldNumber">
            <summary>Field number for the "expression" field.</summary>
        </member>
        <member name="P:Google.Type.Expr.Expression">
            <summary>
            Textual representation of an expression in Common Expression Language
            syntax.
            </summary>
        </member>
        <member name="F:Google.Type.Expr.TitleFieldNumber">
            <summary>Field number for the "title" field.</summary>
        </member>
        <member name="P:Google.Type.Expr.Title">
            <summary>
            Optional. Title for the expression, i.e. a short string describing
            its purpose. This can be used e.g. in UIs which allow to enter the
            expression.
            </summary>
        </member>
        <member name="F:Google.Type.Expr.DescriptionFieldNumber">
            <summary>Field number for the "description" field.</summary>
        </member>
        <member name="P:Google.Type.Expr.Description">
            <summary>
            Optional. Description of the expression. This is a longer text which
            describes the expression, e.g. when hovered over it in a UI.
            </summary>
        </member>
        <member name="F:Google.Type.Expr.LocationFieldNumber">
            <summary>Field number for the "location" field.</summary>
        </member>
        <member name="P:Google.Type.Expr.Location">
            <summary>
            Optional. String indicating the location of the expression for error
            reporting, e.g. a file name and a position in the file.
            </summary>
        </member>
        <member name="T:Google.Type.FractionReflection">
            <summary>Holder for reflection information generated from google/type/fraction.proto</summary>
        </member>
        <member name="P:Google.Type.FractionReflection.Descriptor">
            <summary>File descriptor for google/type/fraction.proto</summary>
        </member>
        <member name="T:Google.Type.Fraction">
            <summary>
            Represents a fraction in terms of a numerator divided by a denominator.
            </summary>
        </member>
        <member name="F:Google.Type.Fraction.NumeratorFieldNumber">
            <summary>Field number for the "numerator" field.</summary>
        </member>
        <member name="P:Google.Type.Fraction.Numerator">
            <summary>
            The numerator in the fraction, e.g. 2 in 2/3.
            </summary>
        </member>
        <member name="F:Google.Type.Fraction.DenominatorFieldNumber">
            <summary>Field number for the "denominator" field.</summary>
        </member>
        <member name="P:Google.Type.Fraction.Denominator">
            <summary>
            The value by which the numerator is divided, e.g. 3 in 2/3. Must be
            positive.
            </summary>
        </member>
        <member name="T:Google.Type.IntervalReflection">
            <summary>Holder for reflection information generated from google/type/interval.proto</summary>
        </member>
        <member name="P:Google.Type.IntervalReflection.Descriptor">
            <summary>File descriptor for google/type/interval.proto</summary>
        </member>
        <member name="T:Google.Type.Interval">
             <summary>
             Represents a time interval, encoded as a Timestamp start (inclusive) and a
             Timestamp end (exclusive).
            
             The start must be less than or equal to the end.
             When the start equals the end, the interval is empty (matches no time).
             When both start and end are unspecified, the interval matches any time.
             </summary>
        </member>
        <member name="F:Google.Type.Interval.StartTimeFieldNumber">
            <summary>Field number for the "start_time" field.</summary>
        </member>
        <member name="P:Google.Type.Interval.StartTime">
             <summary>
             Optional. Inclusive start of the interval.
            
             If specified, a Timestamp matching this interval will have to be the same
             or after the start.
             </summary>
        </member>
        <member name="F:Google.Type.Interval.EndTimeFieldNumber">
            <summary>Field number for the "end_time" field.</summary>
        </member>
        <member name="P:Google.Type.Interval.EndTime">
             <summary>
             Optional. Exclusive end of the interval.
            
             If specified, a Timestamp matching this interval will have to be before the
             end.
             </summary>
        </member>
        <member name="T:Google.Type.LatlngReflection">
            <summary>Holder for reflection information generated from google/type/latlng.proto</summary>
        </member>
        <member name="P:Google.Type.LatlngReflection.Descriptor">
            <summary>File descriptor for google/type/latlng.proto</summary>
        </member>
        <member name="T:Google.Type.LatLng">
            <summary>
            An object that represents a latitude/longitude pair. This is expressed as a
            pair of doubles to represent degrees latitude and degrees longitude. Unless
            specified otherwise, this must conform to the
            &lt;a href="http://www.unoosa.org/pdf/icg/2012/template/WGS_84.pdf">WGS84
            standard&lt;/a>. Values must be within normalized ranges.
            </summary>
        </member>
        <member name="F:Google.Type.LatLng.LatitudeFieldNumber">
            <summary>Field number for the "latitude" field.</summary>
        </member>
        <member name="P:Google.Type.LatLng.Latitude">
            <summary>
            The latitude in degrees. It must be in the range [-90.0, +90.0].
            </summary>
        </member>
        <member name="F:Google.Type.LatLng.LongitudeFieldNumber">
            <summary>Field number for the "longitude" field.</summary>
        </member>
        <member name="P:Google.Type.LatLng.Longitude">
            <summary>
            The longitude in degrees. It must be in the range [-180.0, +180.0].
            </summary>
        </member>
        <member name="T:Google.Type.LocalizedTextReflection">
            <summary>Holder for reflection information generated from google/type/localized_text.proto</summary>
        </member>
        <member name="P:Google.Type.LocalizedTextReflection.Descriptor">
            <summary>File descriptor for google/type/localized_text.proto</summary>
        </member>
        <member name="T:Google.Type.LocalizedText">
            <summary>
            Localized variant of a text in a particular language.
            </summary>
        </member>
        <member name="F:Google.Type.LocalizedText.TextFieldNumber">
            <summary>Field number for the "text" field.</summary>
        </member>
        <member name="P:Google.Type.LocalizedText.Text">
            <summary>
            Localized string in the language corresponding to `language_code' below.
            </summary>
        </member>
        <member name="F:Google.Type.LocalizedText.LanguageCodeFieldNumber">
            <summary>Field number for the "language_code" field.</summary>
        </member>
        <member name="P:Google.Type.LocalizedText.LanguageCode">
             <summary>
             The text's BCP-47 language code, such as "en-US" or "sr-Latn".
            
             For more information, see
             http://www.unicode.org/reports/tr35/#Unicode_locale_identifier.
             </summary>
        </member>
        <member name="T:Google.Type.MoneyReflection">
            <summary>Holder for reflection information generated from google/type/money.proto</summary>
        </member>
        <member name="P:Google.Type.MoneyReflection.Descriptor">
            <summary>File descriptor for google/type/money.proto</summary>
        </member>
        <member name="T:Google.Type.Money">
            <summary>
            Represents an amount of money with its currency type.
            </summary>
        </member>
        <member name="F:Google.Type.Money.CurrencyCodeFieldNumber">
            <summary>Field number for the "currency_code" field.</summary>
        </member>
        <member name="P:Google.Type.Money.CurrencyCode">
            <summary>
            The three-letter currency code defined in ISO 4217.
            </summary>
        </member>
        <member name="F:Google.Type.Money.UnitsFieldNumber">
            <summary>Field number for the "units" field.</summary>
        </member>
        <member name="P:Google.Type.Money.Units">
            <summary>
            The whole units of the amount.
            For example if `currencyCode` is `"USD"`, then 1 unit is one US dollar.
            </summary>
        </member>
        <member name="F:Google.Type.Money.NanosFieldNumber">
            <summary>Field number for the "nanos" field.</summary>
        </member>
        <member name="P:Google.Type.Money.Nanos">
            <summary>
            Number of nano (10^-9) units of the amount.
            The value must be between -999,999,999 and +999,999,999 inclusive.
            If `units` is positive, `nanos` must be positive or zero.
            If `units` is zero, `nanos` can be positive, zero, or negative.
            If `units` is negative, `nanos` must be negative or zero.
            For example $-1.75 is represented as `units`=-1 and `nanos`=-750,000,000.
            </summary>
        </member>
        <member name="P:Google.Type.Money.DecimalValue">
            <summary>
            The amount of money in <see cref="T:System.Decimal"/> format. This is an abstraction of the Units and Nanos properties.
            Getting this property combines those property values, and setting this property will set both of those properties.
            </summary>
            <exception cref="T:System.ArgumentOutOfRangeException">The integral part of the decimal must be a valid <see cref="T:System.Int64"/>, and the fractional part must have a maximum of 9 digits of precision.</exception>
        </member>
        <member name="T:Google.Type.MonthReflection">
            <summary>Holder for reflection information generated from google/type/month.proto</summary>
        </member>
        <member name="P:Google.Type.MonthReflection.Descriptor">
            <summary>File descriptor for google/type/month.proto</summary>
        </member>
        <member name="T:Google.Type.Month">
            <summary>
            Represents a month in the Gregorian calendar.
            </summary>
        </member>
        <member name="F:Google.Type.Month.Unspecified">
            <summary>
            The unspecified month.
            </summary>
        </member>
        <member name="F:Google.Type.Month.January">
            <summary>
            The month of January.
            </summary>
        </member>
        <member name="F:Google.Type.Month.February">
            <summary>
            The month of February.
            </summary>
        </member>
        <member name="F:Google.Type.Month.March">
            <summary>
            The month of March.
            </summary>
        </member>
        <member name="F:Google.Type.Month.April">
            <summary>
            The month of April.
            </summary>
        </member>
        <member name="F:Google.Type.Month.May">
            <summary>
            The month of May.
            </summary>
        </member>
        <member name="F:Google.Type.Month.June">
            <summary>
            The month of June.
            </summary>
        </member>
        <member name="F:Google.Type.Month.July">
            <summary>
            The month of July.
            </summary>
        </member>
        <member name="F:Google.Type.Month.August">
            <summary>
            The month of August.
            </summary>
        </member>
        <member name="F:Google.Type.Month.September">
            <summary>
            The month of September.
            </summary>
        </member>
        <member name="F:Google.Type.Month.October">
            <summary>
            The month of October.
            </summary>
        </member>
        <member name="F:Google.Type.Month.November">
            <summary>
            The month of November.
            </summary>
        </member>
        <member name="F:Google.Type.Month.December">
            <summary>
            The month of December.
            </summary>
        </member>
        <member name="T:Google.Type.PhoneNumberReflection">
            <summary>Holder for reflection information generated from google/type/phone_number.proto</summary>
        </member>
        <member name="P:Google.Type.PhoneNumberReflection.Descriptor">
            <summary>File descriptor for google/type/phone_number.proto</summary>
        </member>
        <member name="T:Google.Type.PhoneNumber">
             <summary>
             An object representing a phone number, suitable as an API wire format.
            
             This representation:
            
              - should not be used for locale-specific formatting of a phone number, such
                as "+**************** ext. 123"
            
              - is not designed for efficient storage
              - may not be suitable for dialing - specialized libraries (see references)
                should be used to parse the number for that purpose
            
             To do something meaningful with this number, such as format it for various
             use-cases, convert it to an `i18n.phonenumbers.PhoneNumber` object first.
            
             For instance, in Java this would be:
            
                com.google.type.PhoneNumber wireProto =
                    com.google.type.PhoneNumber.newBuilder().build();
                com.google.i18n.phonenumbers.Phonenumber.PhoneNumber phoneNumber =
                    PhoneNumberUtil.getInstance().parse(wireProto.getE164Number(), "ZZ");
                if (!wireProto.getExtension().isEmpty()) {
                  phoneNumber.setExtension(wireProto.getExtension());
                }
            
              Reference(s):
               - https://github.com/google/libphonenumber
             </summary>
        </member>
        <member name="F:Google.Type.PhoneNumber.E164NumberFieldNumber">
            <summary>Field number for the "e164_number" field.</summary>
        </member>
        <member name="P:Google.Type.PhoneNumber.E164Number">
             <summary>
             The phone number, represented as a leading plus sign ('+'), followed by a
             phone number that uses a relaxed ITU E.164 format consisting of the
             country calling code (1 to 3 digits) and the subscriber number, with no
             additional spaces or formatting, e.g.:
              - correct: "+15552220123"
              - incorrect: "+1 (555) 222-01234 x123".
            
             The ITU E.164 format limits the latter to 12 digits, but in practice not
             all countries respect that, so we relax that restriction here.
             National-only numbers are not allowed.
            
             References:
              - https://www.itu.int/rec/T-REC-E.164-201011-I
              - https://en.wikipedia.org/wiki/E.164.
              - https://en.wikipedia.org/wiki/List_of_country_calling_codes
             </summary>
        </member>
        <member name="F:Google.Type.PhoneNumber.ShortCodeFieldNumber">
            <summary>Field number for the "short_code" field.</summary>
        </member>
        <member name="P:Google.Type.PhoneNumber.ShortCode">
             <summary>
             A short code.
            
             Reference(s):
              - https://en.wikipedia.org/wiki/Short_code
             </summary>
        </member>
        <member name="F:Google.Type.PhoneNumber.ExtensionFieldNumber">
            <summary>Field number for the "extension" field.</summary>
        </member>
        <member name="P:Google.Type.PhoneNumber.Extension">
             <summary>
             The phone number's extension. The extension is not standardized in ITU
             recommendations, except for being defined as a series of numbers with a
             maximum length of 40 digits. Other than digits, some other dialing
             characters such as ',' (indicating a wait) or '#' may be stored here.
            
             Note that no regions currently use extensions with short codes, so this
             field is normally only set in conjunction with an E.164 number. It is held
             separately from the E.164 number to allow for short code extensions in the
             future.
             </summary>
        </member>
        <member name="T:Google.Type.PhoneNumber.KindOneofCase">
            <summary>Enum of possible cases for the "kind" oneof.</summary>
        </member>
        <member name="T:Google.Type.PhoneNumber.Types">
            <summary>Container for nested types declared in the PhoneNumber message type.</summary>
        </member>
        <member name="T:Google.Type.PhoneNumber.Types.ShortCode">
             <summary>
             An object representing a short code, which is a phone number that is
             typically much shorter than regular phone numbers and can be used to
             address messages in MMS and SMS systems, as well as for abbreviated dialing
             (e.g. "Text 611 to see how many minutes you have remaining on your plan.").
            
             Short codes are restricted to a region and are not internationally
             dialable, which means the same short code can exist in different regions,
             with different usage and pricing, even if those regions share the same
             country calling code (e.g. US and CA).
             </summary>
        </member>
        <member name="F:Google.Type.PhoneNumber.Types.ShortCode.RegionCodeFieldNumber">
            <summary>Field number for the "region_code" field.</summary>
        </member>
        <member name="P:Google.Type.PhoneNumber.Types.ShortCode.RegionCode">
             <summary>
             Required. The BCP-47 region code of the location where calls to this
             short code can be made, such as "US" and "BB".
            
             Reference(s):
              - http://www.unicode.org/reports/tr35/#unicode_region_subtag
             </summary>
        </member>
        <member name="F:Google.Type.PhoneNumber.Types.ShortCode.NumberFieldNumber">
            <summary>Field number for the "number" field.</summary>
        </member>
        <member name="P:Google.Type.PhoneNumber.Types.ShortCode.Number">
            <summary>
            Required. The short code digits, without a leading plus ('+') or country
            calling code, e.g. "611".
            </summary>
        </member>
        <member name="T:Google.Type.PostalAddressReflection">
            <summary>Holder for reflection information generated from google/type/postal_address.proto</summary>
        </member>
        <member name="P:Google.Type.PostalAddressReflection.Descriptor">
            <summary>File descriptor for google/type/postal_address.proto</summary>
        </member>
        <member name="T:Google.Type.PostalAddress">
             <summary>
             Represents a postal address, e.g. for postal delivery or payments addresses.
             Given a postal address, a postal service can deliver items to a premise, P.O.
             Box or similar.
             It is not intended to model geographical locations (roads, towns,
             mountains).
            
             In typical usage an address would be created via user input or from importing
             existing data, depending on the type of process.
            
             Advice on address input / editing:
              - Use an i18n-ready address widget such as
                https://github.com/google/libaddressinput)
             - Users should not be presented with UI elements for input or editing of
               fields outside countries where that field is used.
            
             For more guidance on how to use this schema, please see:
             https://support.google.com/business/answer/6397478
             </summary>
        </member>
        <member name="F:Google.Type.PostalAddress.RevisionFieldNumber">
            <summary>Field number for the "revision" field.</summary>
        </member>
        <member name="P:Google.Type.PostalAddress.Revision">
             <summary>
             The schema revision of the `PostalAddress`. This must be set to 0, which is
             the latest revision.
            
             All new revisions **must** be backward compatible with old revisions.
             </summary>
        </member>
        <member name="F:Google.Type.PostalAddress.RegionCodeFieldNumber">
            <summary>Field number for the "region_code" field.</summary>
        </member>
        <member name="P:Google.Type.PostalAddress.RegionCode">
            <summary>
            Required. CLDR region code of the country/region of the address. This
            is never inferred and it is up to the user to ensure the value is
            correct. See http://cldr.unicode.org/ and
            http://www.unicode.org/cldr/charts/30/supplemental/territory_information.html
            for details. Example: "CH" for Switzerland.
            </summary>
        </member>
        <member name="F:Google.Type.PostalAddress.LanguageCodeFieldNumber">
            <summary>Field number for the "language_code" field.</summary>
        </member>
        <member name="P:Google.Type.PostalAddress.LanguageCode">
             <summary>
             Optional. BCP-47 language code of the contents of this address (if
             known). This is often the UI language of the input form or is expected
             to match one of the languages used in the address' country/region, or their
             transliterated equivalents.
             This can affect formatting in certain countries, but is not critical
             to the correctness of the data and will never affect any validation or
             other non-formatting related operations.
            
             If this value is not known, it should be omitted (rather than specifying a
             possibly incorrect default).
            
             Examples: "zh-Hant", "ja", "ja-Latn", "en".
             </summary>
        </member>
        <member name="F:Google.Type.PostalAddress.PostalCodeFieldNumber">
            <summary>Field number for the "postal_code" field.</summary>
        </member>
        <member name="P:Google.Type.PostalAddress.PostalCode">
            <summary>
            Optional. Postal code of the address. Not all countries use or require
            postal codes to be present, but where they are used, they may trigger
            additional validation with other parts of the address (e.g. state/zip
            validation in the U.S.A.).
            </summary>
        </member>
        <member name="F:Google.Type.PostalAddress.SortingCodeFieldNumber">
            <summary>Field number for the "sorting_code" field.</summary>
        </member>
        <member name="P:Google.Type.PostalAddress.SortingCode">
            <summary>
            Optional. Additional, country-specific, sorting code. This is not used
            in most regions. Where it is used, the value is either a string like
            "CEDEX", optionally followed by a number (e.g. "CEDEX 7"), or just a number
            alone, representing the "sector code" (Jamaica), "delivery area indicator"
            (Malawi) or "post office indicator" (e.g. Côte d'Ivoire).
            </summary>
        </member>
        <member name="F:Google.Type.PostalAddress.AdministrativeAreaFieldNumber">
            <summary>Field number for the "administrative_area" field.</summary>
        </member>
        <member name="P:Google.Type.PostalAddress.AdministrativeArea">
            <summary>
            Optional. Highest administrative subdivision which is used for postal
            addresses of a country or region.
            For example, this can be a state, a province, an oblast, or a prefecture.
            Specifically, for Spain this is the province and not the autonomous
            community (e.g. "Barcelona" and not "Catalonia").
            Many countries don't use an administrative area in postal addresses. E.g.
            in Switzerland this should be left unpopulated.
            </summary>
        </member>
        <member name="F:Google.Type.PostalAddress.LocalityFieldNumber">
            <summary>Field number for the "locality" field.</summary>
        </member>
        <member name="P:Google.Type.PostalAddress.Locality">
            <summary>
            Optional. Generally refers to the city/town portion of the address.
            Examples: US city, IT comune, UK post town.
            In regions of the world where localities are not well defined or do not fit
            into this structure well, leave locality empty and use address_lines.
            </summary>
        </member>
        <member name="F:Google.Type.PostalAddress.SublocalityFieldNumber">
            <summary>Field number for the "sublocality" field.</summary>
        </member>
        <member name="P:Google.Type.PostalAddress.Sublocality">
            <summary>
            Optional. Sublocality of the address.
            For example, this can be neighborhoods, boroughs, districts.
            </summary>
        </member>
        <member name="F:Google.Type.PostalAddress.AddressLinesFieldNumber">
            <summary>Field number for the "address_lines" field.</summary>
        </member>
        <member name="P:Google.Type.PostalAddress.AddressLines">
             <summary>
             Unstructured address lines describing the lower levels of an address.
            
             Because values in address_lines do not have type information and may
             sometimes contain multiple values in a single field (e.g.
             "Austin, TX"), it is important that the line order is clear. The order of
             address lines should be "envelope order" for the country/region of the
             address. In places where this can vary (e.g. Japan), address_language is
             used to make it explicit (e.g. "ja" for large-to-small ordering and
             "ja-Latn" or "en" for small-to-large). This way, the most specific line of
             an address can be selected based on the language.
            
             The minimum permitted structural representation of an address consists
             of a region_code with all remaining information placed in the
             address_lines. It would be possible to format such an address very
             approximately without geocoding, but no semantic reasoning could be
             made about any of the address components until it was at least
             partially resolved.
            
             Creating an address only containing a region_code and address_lines, and
             then geocoding is the recommended way to handle completely unstructured
             addresses (as opposed to guessing which parts of the address should be
             localities or administrative areas).
             </summary>
        </member>
        <member name="F:Google.Type.PostalAddress.RecipientsFieldNumber">
            <summary>Field number for the "recipients" field.</summary>
        </member>
        <member name="P:Google.Type.PostalAddress.Recipients">
            <summary>
            Optional. The recipient at the address.
            This field may, under certain circumstances, contain multiline information.
            For example, it might contain "care of" information.
            </summary>
        </member>
        <member name="F:Google.Type.PostalAddress.OrganizationFieldNumber">
            <summary>Field number for the "organization" field.</summary>
        </member>
        <member name="P:Google.Type.PostalAddress.Organization">
            <summary>
            Optional. The name of the organization at the address.
            </summary>
        </member>
        <member name="T:Google.Type.QuaternionReflection">
            <summary>Holder for reflection information generated from google/type/quaternion.proto</summary>
        </member>
        <member name="P:Google.Type.QuaternionReflection.Descriptor">
            <summary>File descriptor for google/type/quaternion.proto</summary>
        </member>
        <member name="T:Google.Type.Quaternion">
             <summary>
             A quaternion is defined as the quotient of two directed lines in a
             three-dimensional space or equivalently as the quotient of two Euclidean
             vectors (https://en.wikipedia.org/wiki/Quaternion).
            
             Quaternions are often used in calculations involving three-dimensional
             rotations (https://en.wikipedia.org/wiki/Quaternions_and_spatial_rotation),
             as they provide greater mathematical robustness by avoiding the gimbal lock
             problems that can be encountered when using Euler angles
             (https://en.wikipedia.org/wiki/Gimbal_lock).
            
             Quaternions are generally represented in this form:
            
                 w + xi + yj + zk
            
             where x, y, z, and w are real numbers, and i, j, and k are three imaginary
             numbers.
            
             Our naming choice `(x, y, z, w)` comes from the desire to avoid confusion for
             those interested in the geometric properties of the quaternion in the 3D
             Cartesian space. Other texts often use alternative names or subscripts, such
             as `(a, b, c, d)`, `(1, i, j, k)`, or `(0, 1, 2, 3)`, which are perhaps
             better suited for mathematical interpretations.
            
             To avoid any confusion, as well as to maintain compatibility with a large
             number of software libraries, the quaternions represented using the protocol
             buffer below *must* follow the Hamilton convention, which defines `ij = k`
             (i.e. a right-handed algebra), and therefore:
            
                 i^2 = j^2 = k^2 = ijk = −1
                 ij = −ji = k
                 jk = −kj = i
                 ki = −ik = j
            
             Please DO NOT use this to represent quaternions that follow the JPL
             convention, or any of the other quaternion flavors out there.
            
             Definitions:
            
               - Quaternion norm (or magnitude): `sqrt(x^2 + y^2 + z^2 + w^2)`.
               - Unit (or normalized) quaternion: a quaternion whose norm is 1.
               - Pure quaternion: a quaternion whose scalar component (`w`) is 0.
               - Rotation quaternion: a unit quaternion used to represent rotation.
               - Orientation quaternion: a unit quaternion used to represent orientation.
            
             A quaternion can be normalized by dividing it by its norm. The resulting
             quaternion maintains the same direction, but has a norm of 1, i.e. it moves
             on the unit sphere. This is generally necessary for rotation and orientation
             quaternions, to avoid rounding errors:
             https://en.wikipedia.org/wiki/Rotation_formalisms_in_three_dimensions
            
             Note that `(x, y, z, w)` and `(-x, -y, -z, -w)` represent the same rotation,
             but normalization would be even more useful, e.g. for comparison purposes, if
             it would produce a unique representation. It is thus recommended that `w` be
             kept positive, which can be achieved by changing all the signs when `w` is
             negative.
             </summary>
        </member>
        <member name="F:Google.Type.Quaternion.XFieldNumber">
            <summary>Field number for the "x" field.</summary>
        </member>
        <member name="P:Google.Type.Quaternion.X">
            <summary>
            The x component.
            </summary>
        </member>
        <member name="F:Google.Type.Quaternion.YFieldNumber">
            <summary>Field number for the "y" field.</summary>
        </member>
        <member name="P:Google.Type.Quaternion.Y">
            <summary>
            The y component.
            </summary>
        </member>
        <member name="F:Google.Type.Quaternion.ZFieldNumber">
            <summary>Field number for the "z" field.</summary>
        </member>
        <member name="P:Google.Type.Quaternion.Z">
            <summary>
            The z component.
            </summary>
        </member>
        <member name="F:Google.Type.Quaternion.WFieldNumber">
            <summary>Field number for the "w" field.</summary>
        </member>
        <member name="P:Google.Type.Quaternion.W">
            <summary>
            The scalar component.
            </summary>
        </member>
        <member name="T:Google.Type.TimeofdayReflection">
            <summary>Holder for reflection information generated from google/type/timeofday.proto</summary>
        </member>
        <member name="P:Google.Type.TimeofdayReflection.Descriptor">
            <summary>File descriptor for google/type/timeofday.proto</summary>
        </member>
        <member name="T:Google.Type.TimeOfDay">
            <summary>
            Represents a time of day. The date and time zone are either not significant
            or are specified elsewhere. An API may choose to allow leap seconds. Related
            types are [google.type.Date][google.type.Date] and
            `google.protobuf.Timestamp`.
            </summary>
        </member>
        <member name="F:Google.Type.TimeOfDay.HoursFieldNumber">
            <summary>Field number for the "hours" field.</summary>
        </member>
        <member name="P:Google.Type.TimeOfDay.Hours">
            <summary>
            Hours of day in 24 hour format. Should be from 0 to 23. An API may choose
            to allow the value "24:00:00" for scenarios like business closing time.
            </summary>
        </member>
        <member name="F:Google.Type.TimeOfDay.MinutesFieldNumber">
            <summary>Field number for the "minutes" field.</summary>
        </member>
        <member name="P:Google.Type.TimeOfDay.Minutes">
            <summary>
            Minutes of hour of day. Must be from 0 to 59.
            </summary>
        </member>
        <member name="F:Google.Type.TimeOfDay.SecondsFieldNumber">
            <summary>Field number for the "seconds" field.</summary>
        </member>
        <member name="P:Google.Type.TimeOfDay.Seconds">
            <summary>
            Seconds of minutes of the time. Must normally be from 0 to 59. An API may
            allow the value 60 if it allows leap-seconds.
            </summary>
        </member>
        <member name="F:Google.Type.TimeOfDay.NanosFieldNumber">
            <summary>Field number for the "nanos" field.</summary>
        </member>
        <member name="P:Google.Type.TimeOfDay.Nanos">
            <summary>
            Fractions of seconds in nanoseconds. Must be from 0 to 999,999,999.
            </summary>
        </member>
    </members>
</doc>
