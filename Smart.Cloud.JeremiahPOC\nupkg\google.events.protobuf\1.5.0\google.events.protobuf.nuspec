﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2013/05/nuspec.xsd">
  <metadata>
    <id>Google.Events.Protobuf</id>
    <version>1.5.0</version>
    <title>Google Events data types (protobuf)</title>
    <authors>Google LLC</authors>
    <license type="expression">Apache-2.0</license>
    <licenseUrl>https://licenses.nuget.org/Apache-2.0</licenseUrl>
    <projectUrl>https://github.com/googleapis/google-cloudevents-dotnet</projectUrl>
    <description>This package contains the data for the CloudEvents used within Google, generated and serialized using Protocol Buffers</description>
    <copyright>Copyright 2020 Google LLC</copyright>
    <tags>google cloud events cloudevents protobuf</tags>
    <repository type="git" url="https://github.com/googleapis/google-cloudevents-dotnet" commit="4f7585f439dfc5326a6aaced94aaf9bb522670c1" />
    <dependencies>
      <group targetFramework=".NETStandard2.0">
        <dependency id="CloudNative.CloudEvents.SystemTextJson" version="2.7.1" exclude="Build,Analyzers" />
        <dependency id="Google.Api.CommonProtos" version="2.15.0" exclude="Build,Analyzers" />
      </group>
    </dependencies>
  </metadata>
</package>